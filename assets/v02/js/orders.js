/** Modal para os order details */

let id = 0;
function setId(_id) {
	$(".modal-body").html('Loading order data..');
	id = _id
}

$(document).ready(function () {
	$('#modal-dados-order').on('shown.bs.modal', function () {
		$(this).find(".modal-body").load(base_url + "/order-details/" + id);
	});
})

$(document).ready(function () {
	$('#modal-orders-purchase').on('shown.bs.modal', function () {
		$(this).find(".modal-body").load(base_url + "/order-details-purchase/" + id);
	});
})

$(document).ready(function () {
	$('#modal-logistic').on('shown.bs.modal', function () {
		$(this).find(".modal-body").load(base_url + "/order-details-logistic/" + id);
	});
})

$(document).ready(function () {
	$('#modal-dados-ship').on('shown.bs.modal', function () {
		$(this).find(".modal-body").load(base_url + "/order-shipment-details/" + id);
	});
})



function checkOrder(order_id, purchase) {
	action = 0
	msg = 'Compra desmarcada com sucesso.'
	if (purchase == 1) {
		action = 1
		msg = 'Compra marcada com sucesso.'
	}
	$.ajax({
		url: base_url + '/update-purchased-order',
		type: 'POST',
		dataType: 'json',
		data: {
			order: order_id,
			action: action
		},
		success: function (response) {
			alert(msg);
		},
		error: function (xhr, status, error) {
			// Lógica de tratamento de erros
			console.error(xhr.responseText);
		}
	});
}


function generateInvoice(orderId) {
	$("#invoice-loading-" + orderId).removeClass("esconder");
	$("#generate-invoice-" + orderId).prop("disabled", true);
	console.log('foi');

	$.ajax({
		url: base_url + '/generate-invoice',
		type: 'POST',
		dataType: 'json',
		data: {
			order_id: orderId
		},
		success: function (response) {
			if (response.status === 'success') {
				$("#invoice-loading-" + orderId).addClass("esconder");
				$("#generate-invoice-" + orderId).addClass("esconder");
				$("#see-invoice-" + orderId).removeClass("esconder");
				$("#see-invoice-" + orderId).attr("href", "https://prd-mkp.bighub.store/invoices/" + response.id + ".pdf");
			}
		},
		error: function (xhr, status, error) {
			// Lógica de tratamento de erros
			console.error(xhr.responseText);
		}
	});
}

function fetchShippingMethods(senderAddress, country, zipCode) {
	// Configuração do AJAX com jQuery
	$.ajax({
		url: base_url + `/shipment-methods/${senderAddress}/${country}/${zipCode}`,
		type: 'GET',
		dataType: 'json',
		success: function (response) {
			populateSelect(response); // Supondo que response já seja um objeto JSON
		},
		error: function (xhr, status, error) {
			// Lógica de tratamento de erros
			console.error('XHR response: ', xhr);
			console.error('There has been a problem with your fetch operation:', error);
		}
	});
}

function getLabels(data) {
	$.ajax({
		url: base_url + `/get-labels`,
		type: 'POST',
		data: JSON.stringify(data), // Converte o corpo da requisição para JSON
		success: function (response) {
			const feedbackDiv = document.getElementById('feedback');

			if (typeof response === 'string') {
				feedbackDiv.innerHTML = response;
			} else {
				const alertDiv = document.createElement('div');
				alertDiv.className = 'alert alert-success';
				alertDiv.setAttribute('role', 'alert');
				alertDiv.textContent = 'Label created successfully!';
				feedbackDiv.innerHTML = '';
				feedbackDiv.appendChild(alertDiv);

				if (response.url) {
					window.open(response.url, '_blank');
				}
			}

			document.getElementById('makeLabelButton').disabled = true;
			sessionStorage.setItem('labelCreated', 'true');
		},
		error: function (xhr, status, error) {
			const feedbackDiv = document.getElementById('feedback');
			feedbackDiv.innerHTML = '<div class="alert alert-danger" role="alert">Error creating label: ' + error + '</div>';
			console.error('XHR response: ', xhr);
			console.error('There has been a problem with your fetch operation:', error);
		}
	});
}

function getTracking(data) {
	return new Promise((resolve, reject) => {
		$.ajax({
			url: base_url + `/get-tracking`,
			type: 'POST',
			dataType: 'json',
			data: JSON.stringify(data),
			success: function (response) {
				if (response) {
					resolve(response.tracking_url);
				} else {
					reject('No tracking number found');
				}
			},
			error: function (xhr, status, error) {
				reject(error);
			}
		});
	});
}

function fetchOrigins() {
	$.ajax({
		url: base_url + `/get-origins`,
		type: 'GET',
		dataType: 'json',
		success: function (response) {
			populateOriginSelect(response);
		},
		error: function (xhr, status, error) {
			// Lógica de tratamento de erros
			console.error('XHR response: ', xhr);
			console.error('There has been a problem with your fetch operation:', error);
		}
	});
}

function postTrackingInMarketplaces(data) {
	$.ajax({
		url: base_url + `/shippment`,
		type: 'POST',
		dataType: 'json',
		data: data,
		success: function (response) {
			//
		},
		error: function (xhr, status, error) {
			console.error('XHR response: ', xhr);
			console.error('There has been a problem with your fetch operation:', error);
		}
	});
}
