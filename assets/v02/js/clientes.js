function chamarCurlImpersonate(user_token) {
	$.ajax({
	url: base_url+'/impersonate',
	type: 'POST',
	dataType: 'json',
	data: {
		user: user_token
	},
	success: function(response) {
		var token = response;
      	var redirectUrl = "https://app.bighub.store?token=" + token;
      	window.open(redirectUrl, "_blank");
	},
	error: function(xhr, status, error) {
		// Lógica de tratamento de erros
		console.error(xhr.responseText);
	}
	});
}


function updateStatusUser(user_token, status) {

	$.ajax({
	url: base_url+'/update-status-user',
	type: 'POST',
	dataType: 'json',
	data: {
		user: user_token,
        status: status
	},
	success: function(response) {
        if(status == 'active'){
            show_button_inactive(user_token);
        }else{
            show_button_active(user_token);
        }
	},
	error: function(xhr, status, error) {
		// Lógica de tratamento de erros
		console.error(xhr.responseText);
	}
	});
}


function show_button_active(user_token) {
    var elementoOriginal = document.querySelector('.badge-success');
  
    // Criar um novo elemento span
    var novoElemento = document.createElement('span');
  
    // Definir o valor do atributo id
    novoElemento.setAttribute('id', 'user_' + user_token);
  
    // Adicionar as classes 'badge-sm' e 'badge-secondary'
    novoElemento.classList.add('badge-sm', 'badge-secondary');
  
    // Definir o conteúdo do novo elemento
    novoElemento.textContent = 'Por ativar';
  
    // Substituir o elemento original pelo novo elemento
    elementoOriginal.parentNode.replaceChild(novoElemento, elementoOriginal);
  
    // Selecionar o botão com a classe 'new-button'
    var botaoOriginal = document.querySelector('.new-button');
  
    // Atualizar as classes e os valores do botão
    botaoOriginal.classList.remove('btn-primary');
    botaoOriginal.classList.add('btn-warning');
    botaoOriginal.setAttribute('onclick', "updateStatusUser('" + user_token + "', 'active')");
    botaoOriginal.textContent = 'Ativar';
  }

  function show_button_inactive(user_token) {
    var elementoOriginal = document.querySelector('.badge-secondary');
  
    // Criar um novo elemento span
    var novoElemento = document.createElement('span');
  
    // Definir o valor do atributo id
    novoElemento.setAttribute('id', 'user_' + user_token);
  
    // Adicionar as classes 'badge-sm' e 'badge-secondary'
    novoElemento.classList.add('badge-sm', 'badge-success');
  
    // Definir o conteúdo do novo elemento
    novoElemento.textContent = 'Ativo';
  
    // Substituir o elemento original pelo novo elemento
    elementoOriginal.parentNode.replaceChild(novoElemento, elementoOriginal);
  
    // Selecionar o botão com a classe 'new-button'
    var botaoOriginal = document.querySelector('.new-button');
  
    // Atualizar as classes e os valores do botão
    botaoOriginal.classList.remove('btn-warning');
    botaoOriginal.classList.add('btn-primary');
    botaoOriginal.setAttribute('onclick', "updateStatusUser('" + user_token + "', 'inactive')");
    botaoOriginal.textContent = 'Inativar';
  }
  


  
  