.new-button{
    padding: 3px 9px 3px 9px;
    margin-bottom: 0px;
    font-size: 13px;
}

.font-16{
    font-size: 16px!important;
}

.state-positive{
    background: #aee3b1!important;
    font-size: 13px!important;
    color: #025d05 ;
}

.state-negative{
    background: #e93e3d!important;
    font-size: 13px!important;
    color: #d7d9e1;
}

.state-neuter{
    background: #cccaca!important;
    font-size: 13px!important;
    color: #5c4f4f;
}

.badge-neuter{
    color: #5c4f4f;
    background-color: #cccaca;
}

.badge-negative{
    color: #d7d9e1;
    background-color: #e93e3d;
}



.margin-negativa-15{
    margin-top: -15px;
}

.color-hr{
    background: #3d3d3d;
}

.z-index-1{
    z-index: 1;
}

.timeline-day{
    color: #ffffff;
    font-size: 13px;
  }

.textarea {
    resize: vertical; /* Permite redimensionar verticalmente */
    width: 100%; /* <PERSON>enche 100% da largura do contêiner pai */
}

.tr{
    text-align: right;
}


.zap-level-2{
    background: #f1ac00;
    margin-left: 22px;
}   

.zap-level-1{
    background: #f10000;
    margin-left: 22px;
}

.zap-table{
    white-space:normal!important;
}

.zap-data-hora{
    margin-bottom: 0px;
    font-size: 11px;
    font-weight: 500;
}

.esconder{
    display: none;
}