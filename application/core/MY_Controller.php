<?php

class MY_Controller extends CI_Controller
{

	public function __construct()
	{
		parent::__construct();
		
		$logado = $this->session->userdata('logado');

		if (!$logado) {
			var_dump('nao logado');
			redirect(base_url('login'));
		}

		$this->language = 'portugues_pt';
		$this->load->helper('language');
	}


	protected function _getConfigUser()
	{
		$id_user =  $this->_getUserId()->id_usuario;
		$this->db->select('tls.name_language language, tls.sigla_language');
		$this->db->from('tbl_config conf');
		$this->db->join("tbl_language_system tls", "tls.id = conf.id_language", 'left');
		$this->db->where('id_user', $id_user);
		return $this->db->get()->row();
	}

	protected function _getUserId()
	{
		$this->db->select('*');
		$this->db->where('token', $this->session->userdata('token_usuario'));
		$user = $this->db->get('tbl_usuarios')->row();

		return $user;
	}

	protected function _getClient($code)
	{
		$this->db->select('*');
		$this->db->where('codcliente', $code);
		$client = $this->db->get('tbl_clientes')->row();

		return $client;
	}

	protected function _getRespAction()
	{
		$resp = $this->input->get('resp');
		$data = [];
		if ($resp == 's') {
			$data['mensagem'] = 'Inserido com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'e') {
			$data['mensagem'] = 'Excluido com sucesso!';
			$data['class'] = 'alert alert-danger alert-dismissible';
		} elseif ($resp == 'ed') {
			$data['mensagem'] = 'Editado com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'p') {
			$data['mensagem'] = 'Você não tem autorização para editar!';
			$data['class'] = 'alert alert-warning alert-dismissible';
		}
		return $data;
	}

	protected function _getToken()
	{
		return hash('sha512', rand(100, 1000));
	}

	protected function _generateTokenHash()
	{
		$hash = password_hash("tairam123", PASSWORD_DEFAULT);
		return $hash;
	}



	protected function _getProfissionais()
	{
		$coduser = $this->_getUserId()->id_usuario;

		$this->db->select('cod_profissional, tipo_profissional, nome');
		$this->db->from('tbl_profissionais');
		$this->db->where('status', 1);
		$this->db->where('coduser', $coduser);

		return $this->db->get()->result();
	}


	protected function _getServicos()
	{
		$coduser = $this->_getUserId()->id_usuario;

		$this->db->select('id, nome');
		$this->db->from('tbl_servicos');
		$this->db->where('status', 1);
		$this->db->where('coduser', $coduser);

		return $this->db->get()->result();
	}

	protected function _getClientes($codcli = null)
	{
		$coduser = $this->_getUserId()->id_usuario;

		$this->db->select('*');
		$this->db->from('tbl_clientes');
		$this->db->where('status', 1);
		$this->db->where('id_usuario_system', $coduser);

		if ($codcli) {
			$this->db->where('codcliente', $codcli);
			return $this->db->get()->row();
		} else {
			return $this->db->get()->result();
		}
	}



	protected function _getIdProfissional($codProfissional)
	{

		$idUser = $this->_getUserId()->id_usuario;

		$this->db->select('*');
		$this->db->from('tbl_profissionais');
		$this->db->where('status', 1);
		$this->db->where('coduser', $idUser);
		$this->db->where('cod_profissional', $codProfissional);

		return $this->db->get()->row()->id;
	}

	protected function _getLembretes($limit = null)
	{

		$idUser = $this->_getUserId()->id_usuario;

		$this->db->select('*');
		$this->db->from('tbl_lembretes');
		$this->db->where('coduser', $idUser);
		$this->db->where('is_deleted', 0);
		$this->db->where('is_checked', 0);
		if (isset($limit)) {
			$this->db->limit($limit);
		}

		return $this->db->get()->result();
	}


	protected function _getDataDashboard()
	{
		
		$data = $this->_getDataCookie();
		$data += $this->_getLanguage();
		// var_dump($data);
		// die();

		//$data['lembretes'] = $this->_getLembretes(3);
		//$data['num_lembretes'] = $this->db->affected_rows($data['lembretes']);

		return $data;
	}

	protected function _getDataClient($token_user = null)
	{
		$this->db->select('*');
		$this->db->from('tbl_user_clients');
		$this->db->where('user_token', $token_user);

		return $this->db->get()->row();
	}


	protected function _getDataCookie()
	{
		$data = [];
		$data['id_usuario'] = $this->session->userdata('id_usuario');
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');

		return $data;
	}


	protected function _getLanguage()
	{
		$this->lang->load("app", $this->language);

		#Dashboard
		$data['language']['system_name'] = lang('system_name');

		$data['language']['profile'] = lang('profile');
		$data['language']['contact'] = lang('contact');
		$data['language']['mailbox'] = lang('mailbox');
		$data['language']['sever_hours'] = lang('sever_hours');
		$data['language']['logout'] = lang('logout');

		$data['language']['dashboard'] = lang('dashboard');
		$data['language']['clients'] = lang('clients');
		$data['language']['new_client'] = lang('new_client');

		$data['language']['services'] = lang('services');
		$data['language']['new_service'] = lang('new_service');
		$data['language']['list_services'] = lang('list_services');

		$data['language']['schedules'] = lang('schedules');
		$data['language']['new_scheduling'] = lang('new_scheduling');
		$data['language']['list_schedules'] = lang('list_schedules');

		$data['language']['calendar'] = lang('calendar');
		$data['language']['config_system'] = lang('config_system');



		# register clients
		$data['language']['register_client_data_cadastro'] = lang('register_client_data_cadastro');
		$data['language']['register_client_documento'] = lang('register_client_documento');
		$data['language']['register_client_nome'] = lang('register_client_nome');
		$data['language']['register_client_email'] = lang('register_client_email');
		$data['language']['register_client_fone'] = lang('register_client_fone');
		$data['language']['register_client_nascimento'] = lang('register_client_nascimento');
		$data['language']['register_client_genero'] = lang('register_client_genero');
		$data['language']['register_client_codigo_postal'] = lang('register_client_codigo_postal');
		$data['language']['register_client_endereco'] = lang('register_client_endereco');
		$data['language']['register_client_numero'] = lang('register_client_numero');
		$data['language']['register_client_complemento'] = lang('register_client_complemento');
		$data['language']['register_client_bairro'] = lang('register_client_bairro');
		$data['language']['register_client_cidade'] = lang('register_client_cidade');
		$data['language']['register_client_uf'] = lang('register_client_uf');
		$data['language']['register_client_status'] = lang('register_client_status');
		$data['language']['register_client_obs'] = lang('register_client_obs');

		return $data;
	}


}
