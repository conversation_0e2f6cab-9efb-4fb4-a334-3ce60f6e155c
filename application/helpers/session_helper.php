<?php
/**
 * recebe os dados de uma sessão.
 * @param  string $name nome da sessão.
 * @return mixed
 */
function get_session($name = '') {
	if (isset($_SESSION[$name]))
		return $_SESSION[$name];
	return FALSE;
    // ex: get_session('sala_atendimento')
}

/**
 * cria uma nova sessão.
 * @param string $name  nome da sessão.
 * @param string $value dados a serem armazenados.
 */
function set_session($name = '', $value = '') {
	$_SESSION[$name] = $value;
}

/**
 * remove uma sessão específica.
 * @param  string $name nome da sessão.
 * @return mixed
 */
function unset_session($name = '') {
	unset($_SESSION[$name]);
}

?>