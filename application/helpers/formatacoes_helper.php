<?php

function formatarDataHora($dataHora) {
    // Cria um objeto DateTime a partir da string de data e hora
    $dateTime = new DateTime($dataHora);

    // Adiciona 1 hora
    $dateTime->modify('+1 hour');

    // Retorna a data e hora no formato desejado
    return $dateTime->format('d/m/Y - H:i');
}

function dd($value)
{
	var_dump($value);
	die();
}

function nome_user($id)
{
	$CI = &get_instance();
	$CI->db->select('*');
	$CI->db->from('tbl_usuarios');
	$CI->db->where('id_usuario', $id);
	$result = $CI->db->get()->row();
	return $result->nome;
}


function verficar($dias, $minimo, $maximo)
{
	$verificar_minimo = $dias >= $minimo;
	if ($verificar_minimo) {
		$verificar_maximo = $dias <= $maximo;
		if ($verificar_maximo) {
			return true;
		} else {
			return false;
		}
	} else {
		return false;
	}
}

function nome_plano($id)
{
	$CI = &get_instance();
	$CI->db->select('nome_plano');
	$CI->db->from('tbl_newseguros');
	$CI->db->where('id', $id);
	$result = $CI->db->get()->row();
	if ($result)
		return $result->nome_plano;
	else
		return 'Não existe plano';
}

# Verificando se email e valido
#===================================================================================

function validaEmail($email)
{
	$conta = "^[a-zA-Z0-9\._-]+@";
	$domino = "[a-zA-Z0-9\._-]+.";
	$extensao = "([a-zA-Z]{2,4})$";
	$pattern = $conta . $domino . $extensao;
	if (preg_match($pattern, $email))
		return true;
	else
		return false;
}

#Formatacao de DATA
#===================================================================================
# Faz a conversão de datas para Y-m-d

function date_human_to_mysql($str = '')
{
	return implode('-', array_reverse(explode('/', $str)));
}

# Faz a conversão de datas para d/m/Y

function date_mysql_to_human($str = '')
{
	return implode('/', array_reverse(explode('-', $str)));
}

#Formatacao de VALOR com apenas ponto.
#===================================================================================

function FormatarValorsoPonto($entrada)
{
	$data = number_format($entrada, 2, '.', '');
	return $data;
}

#Formatacao de VALOR
#===================================================================================

function FormatarValor($entrada)
{
	$data = number_format($entrada, 2, ',', '.');
	return $data;
}

#===================================================================================

function FormatarVolume($entrada)
{
	$data = number_format($entrada, 4, ',', '.');
	return $data;
}

#===================================================================================

function FormatarPercentual3($entrada)
{
	$data = number_format($entrada, 3, ',', '.');
	return $data;
}

#===================================================================================

function FormatarPercentual3soPonto($entrada)
{
	$data = number_format($entrada, 3, '.', '');
	return $data;
}

#===================================================================================

function FormatarPercentual($entrada)
{
	$data = number_format($entrada, 4, ',', '.');
	return $data;
}

#Coloca a primeira letra maiuscula de cada palavra da frase ou palavra mesmo

function PrimeiraMaiuscula($entrada)
{
	$data = ucwords(strtolower($entrada));
	return $data;
}

#Saudacao para para cliente
#===================================================================================

function saudacao()
{
	$hr = date(" H ");
	if ($hr >= 12 && $hr < 18) {
		$resp = "Boa tarde";
	} else if ($hr >= 0 && $hr < 12) {
		$resp = "Bom dia";
	} else {
		$resp = "Boa noite";
	}
	return $resp;
}

# quanttidade de meses entre datas
#===========================================================================================================

function meses_entre_datas($data_inicial, $data_final)
{

	$data1 = $data_inicial;
	$arr = explode('/', $data1);

	$data2 = $data_final;
	$arr2 = explode('/', $data2);

	$dia1 = $arr[0];
	$mes1 = $arr[1];
	$ano1 = $arr[2];

	$dia2 = $arr2[0];
	$mes2 = $arr2[1];
	$ano2 = $arr2[2];

	$a1 = ($ano2 - $ano1) * 12;
	$m1 = ($mes2 - $mes1) + 1;
	$m3 = ($m1 + $a1);

	return $m3;
}

function __bind($data = [])
{
	$_bind = array();
	$key_name = '[php_var=%s]';
	foreach ($data as $k => $v) {
		if (is_array($v))
			$v = json_encode($v);
		$_bind[sprintf($key_name, $k)] = $v;
	}
	return $_bind;
}

function scriptJS($files = [], $data = [])
{
	if (!$files)
		return FALSE;
	$src = array();
	$data = __bind($data);
	$str = '';
	foreach ($files as $file) {
		$file_dir = 'assets/js/';
		$file_dir .= $file . '.js';
		if (!file_exists($file_dir))
			die(sprintf('No foi possível carregar', $file . '.js'));
		ob_start();
		echo file_get_contents($file_dir);
		$str = ob_get_clean();
		array_push($src, str_replace(
			array_keys($data),
			$data,
			$str
		));
	}
	return implode(PHP_EOL, $src);
}

function meses($data_inicial, $data_final)
{
	$arr = explode('/', $data_inicial);
	$arr2 = explode('/', $data_final);

	$mes1 = $arr[1];
	$ano1 = $arr[2];
	$mes2 = $arr2[1];
	$ano2 = $arr2[2];

	$a1 = ($ano2 - $ano1) * 12;
	$m1 = ($mes2 - $mes1) + 1;
	$quantidade_meses = ($m1 + $a1);

	$mes_da_data_incial = $mes1;
	$mes_da_data_final = $mes2;

	$data = array();
	for ($i = $mes_da_data_incial; $i <= $mes_da_data_final; $i++) {
		switch ($i) {
			case "01":
				$mes = 'Janeiro';
				break;
			case "02":
				$mes = 'Fevereiro';
				break;
			case "03":
				$mes = 'Março';
				break;
			case "04":
				$mes = 'Abril';
				break;
			case "05":
				$mes = 'Maio';
				break;
			case "06":
				$mes = 'Junho';
				break;
			case "07":
				$mes = 'Julho';
				break;
			case "08":
				$mes = 'Agosto';
				break;
			case "09":
				$mes = 'Setembro';
				break;
			case "10":
				$mes = 'Outubro';
				break;
			case "11":
				$mes = 'Novembro';
				break;
			case "12":
				$mes = 'Dezembro';
				break;
		}
		array_push($data, $mes);
	}

	return $data;
}

function dados_da_data($data, $tipo)
{
	$arr = explode('-', $data);

	if ($tipo == 'dia')
		$resposta = $arr[0];

	if ($tipo == 'mes')
		$resposta = $arr[1];

	if ($tipo == 'ano')
		$resposta = $arr[2];

	return $resposta;
}

function random_color()
{
	$letters = '0123456789ABCDEF';
	$color = '#';
	for ($i = 0; $i < 6; $i++) {
		$index = rand(0, 15);
		$color .= $letters[$index];
	}
	return $color;
}

function cor($cor)
{
	switch ($cor) {
		case "1":
			$color = '#FFFF00';
			break;
		case "2":
			$color = '#CD2626';
			break;
		case "3":
			$color = '#00EE00';
			break;
		case "4":
			$color = '#CD00CD';
			break;
		case "5":
			$color = '#1E90FF';
			break;
		case "6":
			$color = '#00008B';
			break;
		case "7":
			$color = '#FFC125';
			break;
		case "8":
			$color = '#FF4040';
			break;
		case "9":
			$color = '#A020F0';
			break;
	}
	return $color;
}

function array_group_by(array $array, $key)
{
	if (!is_string($key) && !is_int($key) && !is_float($key) && !is_callable($key)) {
		trigger_error('array_group_by(): The key should be a string, an integer, or a callback', E_USER_ERROR);
		return null;
	}
	$func = (!is_string($key) && is_callable($key) ? $key : null);
	$_key = $key;
	// Load the new array, splitting by the target key
	$grouped = [];
	foreach ($array as $value) {
		$key = null;
		if (is_callable($func)) {
			$key = call_user_func($func, $value);
		} elseif (is_object($value) && isset($value->{$_key})) {
			$key = $value->{$_key};
		} elseif (isset($value[$_key])) {
			$key = $value[$_key];
		}
		if ($key === null) {
			continue;
		}
		$grouped[$key][] = $value;
	}
	// Recursively build a nested grouping if more parameters are supplied
	// Each grouped array value is grouped according to the next sequential key
	if (func_num_args() > 2) {
		$args = func_get_args();
		foreach ($grouped as $key => $value) {
			$params = array_merge([$value], array_slice($args, 2, func_num_args()));
			$grouped[$key] = call_user_func_array('array_group_by', $params);
		}
	}
	return $grouped;
}

function ping($host, $port = 80, $timeout = 6)
{
	$fsock = fsockopen($host, $port, $errno, $errstr, $timeout);
	if (!$fsock) {
		return FALSE;
	} else {
		return TRUE;
	}
}

function limpaCPF_CNPJ($valor)
{
	$valor = trim($valor);
	$valor = str_replace(".", "", $valor);
	$valor = str_replace(",", "", $valor);
	$valor = str_replace("-", "", $valor);
	$valor = str_replace("/", "", $valor);
	return $valor;
}

function gerar_hash($size = 20)
{
	$comb = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890';
	$hash = array();
	$combLen = strlen($comb) - 1;
	for ($i = 0; $i < $size; $i++) {
		$n = rand(0, $combLen);
		$hash[] = $comb[$n];
	}

	return implode($hash);
}
