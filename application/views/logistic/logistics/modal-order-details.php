<div class="container-fluid">
	<div class="row">

		<div class="card-header p-3 pb-0">
			<div class="row mt-2">
				<!-- Order details-->
				<div class="col-lg-8 col-md-6 col-12">
					<h6>Order : <?= $order_data->reference ?> </h6>
					<h6>Seller : <?= $seller->name ?> - <?= $seller->email ?>- <?= $seller->phone_number ?></h6>

					<p class="text-sm mb-0">
						Data Order <b> <?= $order_data->created_at ?></b> - Channel <b> <?= $order_data->channel ?> - <?= $order_data->country ?></b>
					</p>
					<p class="text-sm">
						Order Items: <b><?= $order_items ?></b><br>
						Order ID: <b><?= $order_data->id ?></b>
					</p>
				</div>
				<!-- Payment-->
				<div class="col-lg-4 col-md-6 col-12">
					<div class="row">
						<p class="text-sm mb-1"><b>Notificar Colaborador</b></p>
						<div class="card border card-plain border-radius-lg d-flex align-items-center flex-row">
							<div class="form-check form-switch ps-0 w-100">
								<select class="form-control" name="colaborador_id" id="colaborador_id">
									<option value="">Selecione o colaborador</option>
									<?php foreach ($users as $user) { ?>
										<option value="<?= $user->id_usuario ?>"><?= ' ' . $user->nome ?></option>
									<?php } ?>
								</select>
							</div>
						</div>
						<input type="text" class="form-control mt-2" name="text_notification" id="text_notification" value="" style="background-color: #f4f4f4;padding-left: 12px;">
						<span class="esconder" id="msg_notification">Notificação enviada com sucesso.</span>
					</div>
					<div class="row mt-2">
						<button type="button" class="btn bg-gradient-dark ms-auto mb-0" style="width: 35%;" onclick="send_notification()">Notificar</button>
					</div>
				</div>
			</div>
		</div>

		<div class="card-body p-3 pt-0">

			<!-- Billing / Shippinf information-->
			<div class="row mt-2">
				<div class="col-lg-6 col-md-6 col-12">
					<ul class="list-group">
						<li class="list-group-item border-0 d-flex p-2 mb-2 bg-gray-100 border-radius-lg">
							<div class="d-flex flex-column">
								<h6 class="mb-2 text-sm">Billing Information</h6>
								<?php if ($billing_address != null) { ?>
									<span class="mb-2 text-xs">Name: <span class="text-dark font-weight-bold ms-2"><?= $billing_address['first_name'] . ' ' . $billing_address['last_name'] ?></span></span>
									<span class="mb-2 text-xs">Address: <span class="text-dark ms-2 font-weight-bold"><?= $billing_address['street_1'] . ' - ' . $billing_address['street_2'] . ' - ' . $billing_address['zip_code'] . ' - ' . $billing_address['city'] ?></span></span>
									<span class="mb-2 text-xs">Country: <span class="text-dark ms-2 font-weight-bold"><?= $billing_address['country']?></span></span>
									<?php
										$billing_phone = '';
										if (!empty($billing_address['phone'])) {
											$billing_phone = $billing_address['phone'];
										} elseif (!empty($billing_address['phone_secondary'])) {
											$billing_phone = $billing_address['phone_secondary'];
										}
									?>
									<?php if (!empty($billing_phone)): ?>
										<span class="text-xs">Phone: <span class="text-dark ms-2 font-weight-bold"><?= $billing_phone ?></span></span>
									<?php endif; ?>
									<?php } else { ?>
									<span class="badge badge-sm bg-gradient-success state-negative"><?= $item->state ?></span>
								<?php } ?>
							</div>
						</li>
					</ul>
				</div>

				<div class="col-lg-6 col-md-6 col-12">
					<ul class="list-group">
						<li class="list-group-item border-0 d-flex p-2 mb-2 bg-gray-100 border-radius-lg">
							<div class="d-flex flex-column">
								<h6 class="mb-2 text-sm">Shipping Information</h6>
								<?php if ($shipping_address != null) { ?>
									<span class="mb-2 text-xs">Name: <span class="text-dark font-weight-bold ms-2"><?= $shipping_address['first_name'] . ' ' . $shipping_address['last_name'] ?></span></span>
									<span class="mb-2 text-xs">Address: <span class="text-dark ms-2 font-weight-bold"><?= $shipping_address['street_1'] ?> - <?= $shipping_address['street_2'] ?> - <?= $shipping_address['zip_code'] ?> / <?= $shipping_address['city'] ?></span></span>
									<span class="mb-2 text-xs">Country: <span class="text-dark ms-2 font-weight-bold"><?= $shipping_address['country']?></span></span>
									<?php
										$shipping_phone = '';
										if (!empty($shipping_address['phone'])) {
											$shipping_phone = $shipping_address['phone'];
										} elseif (!empty($shipping_address['phone_secondary'])) {
											$shipping_phone = $shipping_address['phone_secondary'];
										}
									?>
									<?php if (!empty($shipping_phone)): ?>
										<span class="text-xs">Phone: <span class="text-dark ms-2 font-weight-bold"><?= $shipping_phone ?></span></span>
									<?php endif; ?>
								<?php } else { ?>
									<span class="badge badge-sm bg-gradient-success state-negative"><?= $item->state ?></span>
								<?php } ?>
							</div>
						</li>
					</ul>
				</div>
				<hr class="horizontal dark mt-2 mb-3">
			</div>

			<?php foreach ($items as $key => $item) {
				// var_dump($item); die();
				$finance = json_decode($item->finance, true);
			?>
				<!-- Itens information-->
				<div class="row">
					<div class="col-lg-6 col-md-6 col-12">
						<div class="d-flex">
							<div>
								<img src="<?= $item->image ?>" class="avatar avatar-xl me-3" alt="product image">
							</div>
							<div>
								<span class="badge badge-sm bg-gradient-success <?= $item->class_state ?>"><?= $item->state ?></span>

								<p class="text-sm mb-1 mt-2"><?= $item->title ?> x <?= $item->quantity ?></p>
								<p class="text-sm mb-1"><b>Item:</b> <?= $key ?></p>
								<p class="text-sm mb-1"><b>Quantidade:</b> <?= $item->quantity ?></p>
								<p class="text-sm mb-1"><b>SKU Interno:</b> <?= $item->sku ?> | <b>EAN:</b> <?= $item->ean ?></p>
								<p class="text-sm mt-1"><b>Fornecedores:</b> <?= $item->suppliers ?></p>

							</div>
						</div>
					</div>

					<div class="col-lg-3 col-md-3 col-12">
						
					</div>



					<div class="col-lg-3 col-md-3 col-12">

						<div class="d-flex justify-content-between">
							<span class="mb-1 text-sm">
								Valor venda
							</span>

							<span class="text-dark text-sm ms-2"><?= $item->total_price ?></span>
						</div>

						<div class="d-flex justify-content-between">
							<span class="mb-1 text-sm">
								Comissão MKT (<?= $finance['percent_commission_marketplace'] ?> %)
							</span>
							<span class="text-dark text-sm ms-2">- <?= number_format($finance['total_commission_marketplace'], 2) ?></span>
						</div>

						<!-- <div class="d-flex justify-content-between">
							<span class="mb-1 text-sm">
								Comissão BIGHUB (<?= $finance['percent_commission_bighub'] ?> %) 
							</span>
							<span class="text-dark text-sm ms-2">- <?= number_format($finance['total_commission_bighub'], 2) ?></span>
						</div> -->

						<hr class="horizontal dark mt-1 mb-1">

						<div class="d-flex justify-content-between">
							<span class="mb-1 text-sm">
								Receb.MKT
							</span>
							<span class="text-dark text-sm ms-2"><?= number_format(($item->total_price - $finance['total_commission_marketplace']), 2) ?></span>
						</div>

						<div class="d-flex justify-content-between">
							<span class="mb-1 text-sm">
								P.Custo
							</span>
							<input type="text" class="form-control" name="price_cost_<?= $key ?>" id="price_cost_<?= $key ?>" value="<?php if (isset($item->cost_price)) echo $item->cost_price ?>" style="background-color: #f4f4f4;/* padding-left: 12px; */text-align: end;padding-right: 9px;margin-left: 34px;">
							<button class="save_price_cost" style="border: 0px;background: #fff;"><i class="material-icons text-secondary position-relative text-lg" style="margin-top: 8px;margin-left: 6px;" onclick="save_cost_price(<?= $key ?>)">save</i></button>

						</div>

						<hr class="horizontal dark mt-1 mb-1">
						<div class="d-flex justify-content-between">
							<span class="mb-2 text-sm">
								Lucro / Perda
							</span>
							<span class="text-dark text-sm ms-2"><?= number_format((($item->total_price - $finance['total_commission_marketplace'])) - $item->cost_price, 2) ?></span>
						</div>

					</div>

				</div>

			<?php } ?>

			<!-- Purchase information-->
			<div class="row p-3">

				<div class="col-lg-3 col-md-6 col-12">

					<h6 class="mb-3">Timeline Order</h6>
					<div class="spinner-border" role="status" id="loading-timeline">
						<span class="sr-only">Loading...</span>
					</div>
					<div class="timeline timeline-one-side">
						<div class="timeline-block mb-3 esconder" id="timeline-data-compra">
							<span class="timeline-step">
								<i class="material-icons text-secondary text-lg">shopping_cart</i>
							</span>
							<div class="timeline-content">
								<h6 class="text-dark text-sm font-weight-bold mb-0">Data da order</h6>
								<p class="text-secondary font-weight-normal text-xs mt-1 mb-0"><?= $order_data->created_at ?></p>
							</div>
						</div>
						<div class="" id="timeline">

						</div>
					</div>
				</div>

				<div class="col-lg-4 col-md-6 col-12">

					<div class="row">
						<h6 class="mt-3">Carrier</h6>
						<div class="card border card-plain border-radius-lg d-flex align-items-center flex-row">
							<div class="form-check form-switch ps-0 w-100">
								<select class="form-control" name="carrier_id" id="carrier_id">
									<option value="">Selecione Carrier</option>
									<?php foreach ($carriers as $carrier) {?>
										<option value="<?= $carrier->id ?>"><?= ' ' . $carrier->carrier_name ?></option>
									<?php } ?>
								</select>
							</div>
						</div>
					</div>

					<div class="row">
						<h6 class="mt-3">Tracking Number</h6>
						<input type="text" class="form-control" name="tracking_number" id="tracking_number" value="<?php if (isset($order_data)) echo $order_data->id ?>" style="background-color: #f4f4f4;padding-left: 12px;">
					</div>

					<div class="row mt-3">
						<input type="hidden" name="reference" id="reference" value="<?= isset($order_data) ? $order_data->reference : '' ?>">
						<input type="hidden" name="user_id" id="user_id" value="<?= isset($order_data) ? $order_data->user_id : '' ?>">
						<input type="hidden" name="order_id" id="order_id" value="<?= isset($order_data) ? $order_data->id : '' ?>">
						<a class="btn bg-gradient-dark ms-auto mb-0" id="save-details-button" onclick="send_tracking_number()">Gravar e Enviar</a>
					</div>

				</div>

				<div class="col-lg-1 col-md-6 col-12"></div>

				<div class="col-lg-4 col-md-6 col-12">
					

				</div>

			</div>
		</div>
	</div>
</div>

<script>
	function set_info_item(key) {
		var supplierItem = $('#supplier_item_' + key).val();
		var statusItem = $('#status_item_' + key).val();
		var obsItem = $('#obs_item_' + key).val();
		var orderId = $('#order_id').val();
		var keynumber = key;

		// Verifica se os valores não estão vazios
		if (supplierItem === "" || statusItem === "") {
			alert('Por favor, selecione ambos os campos Supplier e Status.');
			return;
		}
		// Envia os dados via AJAX
		$.ajax({
			url: base_url + 'save-details-item',
			type: 'POST',
			data: {
				supplier_item: supplierItem,
				status_item: statusItem,
				order_id: orderId,
				obs_item: obsItem,
				key_number: keynumber
			},
			success: function(response) {
				fetchData(orderId);
				// Exibe o alerta de sucesso
			},
			error: function(xhr, status, error) {
				// Exibe o alerta de erro
				alert('Erro ao salvar os dados: ' + error);
			}
		});
	}

	function save_details_order() {
		var orderStatus = $('#order_status').val();
		var orderId = $('#order_id').val();
		var comments = $('#comments_order').val();

		// Envia os dados via AJAX
		$.ajax({
			url: base_url + 'save-details-order',
			type: 'POST',
			data: {
				order_status: orderStatus,
				order_id: orderId,
				obs_order: comments
			},
			success: function(response) {
				fetchData(orderId);
				// Exibe o alerta de sucesso
				// alert('OK! Os dados foram salvos com sucesso.');
			},
			error: function(xhr, status, error) {
				// Exibe o alerta de erro
				alert('Erro ao salvar os dados: ' + error);
			}
		});
	}

	function updateTimeline(data) {
		// Limpa os itens atuais da timeline
		$('#timeline').empty();

		// Converte a string JSON para um objeto
		if (typeof data === 'string') {
			try {
				data = JSON.parse(data);
			} catch (e) {
				console.error('Erro ao parsear JSON:', e);
				return;
			}
		}

		if (Array.isArray(data) && data.length > 0) {
			data.forEach(purchase => {
				$('#timeline').append(`
                    <div class="timeline-block mb-3">
                        <span class="timeline-step">
                            <i class="material-icons text-success text-gradient text-lg">done</i>
                        </span>
                        <div class="timeline-content">
                            <h6 class="text-dark text-sm font-weight-bold mb-0">${purchase.status_name}</h6>
                            <p class="text-secondary font-weight-normal text-xs mt-1 mb-0">${purchase.created_at}</p>
                            <p class="text-secondary font-weight-normal text-xs mt-1 mb-0">${purchase.author_name}</p>
							 <p class="text-secondary font-weight-normal text-xs mt-1 mb-0">${purchase.complement}</p>
                        </div>
                    </div>
                `);
			});
		} else {
			//console.error('Dados recebidos não são uma array ou estão vazios:', data);
		}
	}

	function fetchData(order) {
		$('#loading-timeline').removeClass('esconder');
		$('#timeline-data-compra').addClass('esconder');

		var orderId;
		if (order !== undefined) {
			orderId = order;
		} else {
			orderId = $('#order_id').val();
		}

		$.ajax({
			url: base_url + 'get-timeline-history',
			type: 'POST',
			data: {
				order_id: orderId
			},
			dataType: 'json',
			success: function(response) {
				$('#loading-timeline').addClass('esconder');
				$('#timeline-data-compra').removeClass('esconder');
				// Chama a função para atualizar a timeline com os dados recebidos
				updateTimeline(response);

			},
			error: function(error) {
				console.error('Erro ao buscar dados:', error);
			}
		});
	}

	function send_invoice_order(key) {
		$('#loading-timeline').removeClass('esconder');
		var fileInput = $('#fileInput_' + key)[0];
		var file = fileInput.files[0];
		var orderId = $('#order_id').val(); // Obtém o valor do campo de order_id

		if (!file) {
			$('#result').text('Por favor, selecione um arquivo.');
			return;
		}

		var formData = new FormData();
		formData.append('file', file);
		formData.append('item_key', $('#item_key_' + key).val()); // Adiciona o valor do campo oculto
		formData.append('order_id', orderId); // Adiciona o valor do campo order_id

		$.ajax({
			url: '/upload-order-invoice', // Substitua pela URL do seu endpoint PHP
			type: 'POST',
			data: formData,
			contentType: false, // Impede o jQuery de definir o contentType
			processData: false, // Impede o jQuery de processar os dados
			dataType: 'json',
			success: function(response) {
				$('#loading-timeline').addClass('esconder');

				if (response.status === 'success') {
					$('#no_invoice_' + key).addClass('esconder');
					// Atualiza o link com a URL do arquivo enviado
					$('#invoice_link_' + key).removeClass('esconder');
					var linkElement = $('#invoice_link_' + key);
					linkElement.attr('href', response.file_url);
					linkElement.text('Fatura Anexada');

					fetchData(orderId);
					$('#result').text(response.message);
				} else {
					$('#result').text('Erro: ' + response.message);
				}
			},
			error: function(xhr, status, error) {
				$('#loading-timeline').addClass('esconder');
				$('#result').text('Ocorreu um erro: ' + error);
			}
		});
	}

	function send_notification() {
		// Obter os valores dos campos
		var colaborador_id = $('#colaborador_id').val();
		var text_notification = $('#text_notification').val();

		// Validação simples
		if (colaborador_id === '' || text_notification === '') {
			alert('Por favor, preencha todos os campos.');
			return;
		}

		// Desativar o botão
		var $button = $('button[onclick="send_notification()"]');
		$button.prop('disabled', true);

		// Dados a serem enviados
		var data = {
			colaborador_id: colaborador_id,
			text_notification: text_notification
		};

		// Enviar a solicitação AJAX
		$.ajax({
			url: 'send-notification', // Substitua pela URL do seu endpoint PHP
			type: 'POST',
			data: JSON.stringify(data),
			contentType: 'application/json; charset=utf-8',
			dataType: 'json',
			success: function(response) {
				$button.prop('disabled', false); // Reativar o botão

				if (response.success) {
					$('#msg_notification').removeClass('esconder');
					setTimeout(function() {
						$('#msg_notification').addClass('esconder');
					}, 5000);

					// Limpar campos
					$('#text_notification').val('');
					$('#colaborador_id').val('');
				} else {
					alert('Erro ao enviar notificação. Tente novamente.');
				}
			},
			error: function(xhr, status, error) {
				$button.prop('disabled', false); // Reativar o botão
				alert('Erro ao conectar ao servidor. Tente novamente.');
			}
		});
	}


	// Executa a função fetchData quando a página carrega
	$(document).ready(function() {
		fetchData();
	});
</script>