<div class="container-fluid py-4">
	<div class="row mt-4">
		<div class="col-12">
			<div class="card">
				<!-- Card header -->
				<div class="card-header">
					<h5 class="mb-0">Ordens para compras (Versão Melhorada)</h5>
				</div>
				<div class="table-responsive" style="padding: 9px 20px 20px 20px;">
					<table id="datatable-purchase-orders-new" class="table table-striped" style="width:100%;">
						<thead class="thead-light">
							<tr>
								<th></th>
								<th>Detalhes</th>
								<th>Pedido</th>
								<th>Data Pedido</th>
								<th>Itens</th>
								<th>Channel</th>
								<th>User</th>
								<th>Preço Total</th>
								<th>Status Compra</th>
							</tr>
						</thead>
						<tbody>
							<!-- Con<PERSON><PERSON><PERSON> da tabela ser<PERSON> carregado via AJAX -->
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
</div>

<script>
// datatable-purchase-orders-new
$(document).ready(function() {
  var start = 0; // Inicializa o índice de partida dos registros

  var dataTable = $('#datatable-purchase-orders-new').DataTable({
    "processing": true,
    "serverSide": true,
    "ajax": {
      "url": "<?php echo base_url('get-purchase-orders-new'); ?>",
      "type": "POST",
      "data": function(d) {
        // Passa o índice de partida dos registros como parâmetro
        d.start = start;
      }
    },
    "columns": [{
        "data": null,
        "defaultContent": ""
      },
      {
        "data": "1"
      },
      {
        "data": "2"
      },
      {
        "data": "3"
      },
      {
        "data": "4"
      },
      {
        "data": "5"
      },
      {
        "data": "6"
      },
      {
        "data": "7"
      },
      {
        "data": "8"
      }
    ],
    "createdRow": function(row, data, dataIndex) {
      // Verifique a condição para a linha que você deseja colorir
      if (data[8] === "Aberta") {
        $(row).css('background-color', 'rgb(22 190 255 / 39%)'); // Aplica a cor desejada
      }
    }
  });

  // Evento de mudança de página do DataTables
  $('#datatable-purchase-orders-new').on('page.dt', function(e, settings) {
    // Atualiza o índice de partida quando a página é alterada
    start = settings._iDisplayStart;
  });
});
</script>
