
<section class="content-header">
	<h1><?php echo $total_planos ?> - Resultados encontrados</h1>
	<ol class="breadcrumb">
        <li><i class="fa fa-dashboard"></i>RESULTADOS ENCONTRADOS</li>
	</ol>
</section>


<section class="content">
<div class="row">
        <?php foreach ($seguros as $seg){?>
          <?php if (verficar($seg->quantidade_dias, $seg->minimo_dias, $seg->maximo_dias)) { ?>
            <div class="col-md-2">
              <!-- Widget: user widget style 1 -->
              <div class="box box-widget widget-user">
            
                <!-- Add the bg color to the header using any of the bg-* classes -->
                <div class="widget-user-header bg-aqua-active">
               
                  <h3 class="widget-user-username"><?php echo $seg->nome_plano ?></h3>
                  <h5 class="widget-user-desc"><?php echo $seg->nome_seguradora ?>   <?php if($seg->id_tabela > 0 && !empty($seg->valor_tabela)){ echo ' ( Tabela - Valor: '.$seg->valor_tabela.')'; }?></h5>
                  <p><?php echo $seg->data_ida ?> a <?php echo $seg->data_volta ?></p>
                  <p>( <?php echo $seg->pais_destino ?> )</p>
                </div>
                <div class="widget-user-image">
                  <!-- <img class="img-circle" src="<?= base_url(); ?>assets/images/seguradoras/<?php echo $seg->img_seguradora ?>" alt="img seguradora"> -->
                </div>
                <div class="box-footer">
                  <div class="row">
                    <div class="col-sm-4 border-right"style="text-align: center;">
                        <img src="<?= base_url(); ?>assets/images/seguradoras/<?php echo $seg->img_seguradora ?>" alt="img seguradora" style="width: 69px;">
                    </div>
                    <!-- /.col -->
                    <div class="col-sm-4 border-right">
                      <div class="description-block">
                        <h5 class="description-header"><?php echo $seg->cotacao_dolar_usado ?></h5>
                        <span class="description-text">Dolar  <?php if(!empty($seg->dolar_seguradora)){ echo 'Seg.'; }else{ echo 'Geral'; }?></span>
                      </div>
                      <!-- /.description-block -->
                    </div>
                    <!-- /.col -->
                    <div class="col-sm-4">
                      <div class="description-block">
                        <h5 class="description-header"><?php echo $seg->id_maxima ?></h5>
                        <span class="description-text">Idade Máxima</span>
                      </div>
                      <!-- /.description-block -->
                    </div>
                    <!-- /.col -->
                  </div>
                  <!-- /.row -->
                </div>
                <div class="box-footer no-padding">
                  <ul class="nav nav-stacked">
                    <li><a href="#">Dias <span class="pull-right badge bg-aqua"><?php echo $seg->quantidade_dias ?></span></a></li>
                    <li><a href="#">Viajantes <span class="pull-right badge bg-blue"><?php echo $seg->quantidade_viajantes ?></span></a></li>
                    <li><a href="#">Valor por viajante <span class="pull-right badge bg-blue"><?php echo $seg->valor_por_viajante ?></span></a></li>
                    <li><a href="#">Valor total dos dias <span class="pull-right badge bg-green font-value"><?php echo $seg->valor_total_dos_dias ?></span></a></li>
                  </ul>
                </div>
                
                <button type="button" class="btn btn-block btn-success btn-sm">
                  <input type="checkbox" class="flat-red">
                </button>
              </div>
              <!-- /.widget-user -->

            </div>
          <?php } ?>  
        <?php } ?>
      

      </div>
</section>



<section class="content">
<div class="box">
            <div class="box-header">
              <h3 class="box-title">Data Table With Full Features</h3>
            </div>
            <!-- /.box-header -->
            <div class="box-body">
              <table id="example1" class="table table-bordered table-striped">
                <thead>
                <tr>
                  <th style="width: 10px">Check</th>
                  <th style="width: 80px"></th>
                  <th>Plano</th>
                  <th>Seguradora</th>
                  <th style="width: 40px">Label</th>
                </tr>

               
                </thead>
                <tbody>
                <?php foreach ($seguros as $seg){?>
                <tr>
                  <td><input type="checkbox" class="flat-red"></td>
                  <td><img src="<?= base_url(); ?>assets/images/seguradoras/<?php echo $seg->img_seguradora ?>" alt="img seguradora" style="width: 40px;"></td>
                  <td><?php echo $seg->nome_plano ?></td>
                  <td><?php echo $seg->nome_seguradora ?></td>

                  <td><span class="badge bg-red">55%</span></td>
                </tr>
                <?php } ?>
                
                </tfoot>
              </table>
            </div>
            <!-- /.box-body -->
          </div>
</section>
  