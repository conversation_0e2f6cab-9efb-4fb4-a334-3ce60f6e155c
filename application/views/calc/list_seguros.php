<section class="content">
    <form action="<?php echo base_url('enviar-cotacao') ?>" method="POST">
          <div class="box">
            <div class="box-header">
              <h3 class="box-title"><b>De:</b> <?php echo $filtro->data_ida ?> <b>-</b> <?php echo $filtro->data_volta ?>   <b>-   (</b>  <?php echo $filtro->destino ?>  <b>)</b> 
              -  <?php echo $filtro->quantidade_dias ?> <b>- Dias</b> </h3> </br></br>
              <div class="row">
									<div class="col-md-4">
										<div class="form-group">
											<label>Nome cliente</label>
											<input type="text" class="form-control"  name="nome_cliente">
										</div>
									</div>

                  <div class="col-md-3">
										<div class="form-group">
											<label>Email ( Destino )</label>
											<input type="text" class="form-control"  name="email_cliente">
										</div>
									</div>

                  <div class="col-md-5">
                    <!-- <div class="form-group">
											<label>Observação para cliente</label>
											<input type="text" class="form-control"  name="observacoes">
                    </div> -->
                    <div class="form-group">
											<label>Email ( CC )</label>
											<input type="text" class="form-control"  name="email_cc">
										</div>
										
									</div>


							</div>
              <div class="row">
                  <div class="col-md-2">
										
                    <div class="form-group">
                    <input type="checkbox" name="desconto" value='1'>
                      Enviar desconto
										</div>
                  </div>

                  <div class="col-md-2">
                    <div class="form-group">
                    <input type="checkbox" name="parcelamento" value='1'>
                      Enviar parcelamento
										</div>
									</div> 
                  
              </div>
              <div class="row">
									<div class="col-md-6">
                  <label>Observação para Cliente</label>
										<textarea name='observacao_cliente' placeholder="Digite o conteúdo aqui" style="width: 100%; height: 70px; font-size: 14px; line-height: 18px; border: 1px solid #dddddd; padding: 10px;"></textarea>
                  </div>
                  
                  <div class="col-md-6">
                  <label>Observação Interna ( Otripulante )</label>
										<textarea name='observacao_interna' placeholder="Digite o conteúdo aqui" style="width: 100%; height: 70px; font-size: 14px; line-height: 18px; border: 1px solid #dddddd; padding: 10px;"></textarea>
									</div>
								</div>
       
              <hr>
              <h3 class="box-title"><?php echo $total_planos ?> - Resultados encontrados</h3>
						</div>
       
            <div class="box-body">
              <table id="example1" class="table table-bordered table-striped">
                <thead>
                  <tr>
                    <!-- <th style="width: 10px">Check</th> -->
                    <th style="width: 80px">Img</th>
                    <th>Plano</th>
                    <th>Seguradora</th>
                    <th>$ Dolar ( Origem )</th>
                    <th style="width: 100px">Viajantes</th>
                    <th>Valor por viajante</th>
                    <th>Valor Total</th>
                    <th>Desconto aplicado - <?php echo $filtro->desconto_aplicado ?> %</th>
                    <th>Valor c/desconto </th>
                    <th>Parcelamento valor total</th>
                    <th>Idade máxima</th>
                  </tr>
                </thead>
                <tbody>

                <?php foreach ($seguros as $seg){ ?>
                	<?php if (verficar($seg->quantidade_dias, $seg->minimo_dias, $seg->maximo_dias)) { ?>

                  <tr>
                    <!-- <td><input name="plano[]" type="checkbox" value="<?php echo $seg->id ?>" class="flat-red"></td> -->
                    <td><img src="https://otripulante.com/assets/images/seguradoras/<?php echo $seg->img_seguradora ?>" alt="img seguradora" style="width: 40px;"></td>
                    <td><input name="plano[]" type="checkbox" value="<?php echo $seg->id ?>" class="flat-red">&nbsp; <?php echo $seg->nome_plano ?></td>
                    <td><?php echo $seg->nome_seguradora ?></td>
                    <td><b><?php echo $seg->cotacao_dolar_usado ?></b>&nbsp;&nbsp;   (  Dolar  <?php if(!empty($seg->dolar_seguradora)){ echo 'Seguradora'; }else{ echo 'Geral'; }?>  )</td>
                    <td class="font-value-total"><?php echo $seg->quantidade_viajantes ?></td>
                    <td class="font-value-total"><?php echo $seg->valor_por_viajante ?></td>
                    <td><span class="font-value-total badge bg-green"><?php echo $seg->valor_total_dos_dias ?></span></td>
                    <td><span class="font-value-total"><?php echo $seg->desconto_aplicado ?></span></td>
                    <td><span class="font-value-total"><b><?php echo $seg->valor_total_dos_dias_com_desconto_calculadora ?></b></span></td>
                    <td><span class="font-value-total"><?php echo $seg->forma_parcelamento ?></span></td>
                    <td><?php echo $seg->id_maxima ?></td>
                  </tr>
                 <?php } ?>
                <?php } ?>
              </table>
            </div>
        
            <div class="box-footer">
              <input type="hidden" name='destino' value="<?php echo $filtro->destino ?>">	
              <input type="hidden" name='data_ida' value="<?php echo $filtro->data_ida ?>">	
              <input type="hidden" name='data_volta' value="<?php echo $filtro->data_volta ?>">	
              <input type="hidden" name='passageiros' value="<?php echo $filtro->passageiros ?>">	
              <input type="hidden" name='desconto_aplicado' value="<?php echo $filtro->desconto_aplicado ?>">	

              <button type="submit" class="btn btn-primary pull-right">Enviar</button>
            </div>
          </div>
    </form>  
</section>
  