
<section class="content-header">
	<?php
	if (isset($resp) && !empty($resp)) {
		?>
		<div class="row">
			<div class="col-md-12">
				<div class="<?php echo $class ?>">
					<button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
					<h5><i class="icon fa fa-check"></i><?php echo $mensagem ?></h5>
				</div>
			</div>
			<br />
		</div>
	<?php } ?>
</section>

<section class="content">
	<div class="row">
        <div class="col-xs-12">
			<div class="box">

				<div class="box-header">
					</br>
					<div class="box-body table-responsive no-padding">
						<table id="tabela-datatable" class="table table-hover">
							<thead>
								<tr>
								<th>Id Cotação</th>
									<th>Cliente</th>
									<th>Email</th>
									<th><PERSON><PERSON></th>
									<th>Data Ida / Volta / Dias</th>
									<th>Enviado <PERSON></th>
									<th>Enviado Parcelamento</th>
									
									<th>Ações</th>
								</tr>
							</thead>
							<tbody>
								<?php foreach ($cotacoes as $c) { ?>
									<tr>
										<td><?php echo $c->id ?></td>
										<td><?php echo $c->nome_cliente ?></td>
										<td><?php echo $c->email_cliente ?></td>
										<td><?php echo $c->destino ?></td>
										<td><?php echo date_mysql_to_human($c->data_ida).'  a  '.date_mysql_to_human($c->data_volta).'  -  '.$c->dias ?> dias</td>
										<td>
											<?php if ($c->enviado_desconto == '1') { ?>
													<a class="btn btn-success btn-xs">SIM ( <?php echo $c->porcentagem_desconto ?> %) </a>
											<?php } else { ?>
													<a class="btn btn-danger btn-xs">NÃO</a>
											<?php }	?>	
										</td>
	
										<td>
											<?php if ($c->enviado_parcelamento == '1') { ?>
													<a class="btn btn-success btn-xs">SIM</a>
											<?php } else { ?>
													<a class="btn btn-danger btn-xs">NÃO</a>
											<?php }	?>	
										</td>
									
									
										<td>
											<a class="link-modal-dados btn btn-primary btn-xs" href="<?php echo base_url('detalhes-cotacao/'.$c->id) ?>">  Detalhes</a>
											<a href="<?php echo base_url('excluir-cotacao/' . $c->id) ?>" class="btn btn-danger btn-xs">Excluir</a>
										</td>
									</tr>
								<?php } ?>
							</tbody>
						</table>
					</div>
				</div>

			</div>
        </div>
	</div>

	<div class="modal fade" id="modal-dados" tabindex="-1">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
				<!-- <form action="<?php echo base_url() ?>salvar-informacoes-compra" method="POST" enctype="multipart/form-data"> -->
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal">
							<span aria-hidden="true">&times;</span>
						</button>
						<p class="modal-title" id="myModalLabel"><b>Planos enviados na cotação</b></p>
					</div>
					<div class="col-md-12">
						<div class="modal-body"></div>

					</div>
					<div class="modal-footer">
						<!-- <button type="submit" class="btn btn-primary"> Salvar Informações</button> -->
						<button type="button" class="btn btn-gray" data-dismiss="modal">Sair</button>
					</div>
				<!-- </form> -->
            </div>
		</div>
	</div>

</section>

