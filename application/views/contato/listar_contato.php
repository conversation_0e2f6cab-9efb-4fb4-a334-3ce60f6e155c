<section class="content-header">
	<?php if (isset($resp) && !empty($resp)) { ?>
		<div class="row">
			<div class="col-md-12">
				<div class="<?php echo $class ?>">
					<button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
					<h5><i class="icon fa fa-check"></i><?php echo $mensagem ?></h5>
				</div>
			</div>
			<br />
		</div>
	<?php } ?>
</section>

<section class="content">
	<div class="row">
        <div class="col-xs-12">
			<div class="box">

				<div class="box-header">
					</br>
					<div class="box-body table-responsive no-padding">
						<table id="tabela-datatable" class="table table-hover">
							<thead>
								<tr>
									<th>#</th>
									<th>Nome</th>
									<th>E-mail</th>
									<th>Telefone</th>
									<th>Data de cadastro</th>
									<th>Ações</th>
								</tr>
							</thead>
							<tbody>
								<?php foreach ($contatos as $s) { ?>
									<tr>
										<td>
											<?php echo $s->id ?>
										</td>
										<td>
											<!-- <?php echo $s->lida == '1' ? '<i class="fa fa-envelope-open-o"></i>' : '<i class="fa fa-envelope-o"></i>' ?> -->
											<?php echo $s->nome ?>
										</td>
										<td><?php echo $s->email ?></td>
										<td><?php echo $s->telefone ?></td>
										<td><?php echo date_mysql_to_human($s->data_cadastro) ?></td>
										<td>
											<!-- <a href="<?php echo base_url('visualizar-contato/' . $s->id) ?>" class="btn btn-primary btn-xs">visualizar</a> -->
											<a href="<?php echo base_url('excluir-contato/' . $s->id) ?>" class="btn btn-danger btn-xs">Excluir</a>
										</td>
									</tr>
								<?php } ?>

							</tbody>

						</table>

					</div>
				</div>

			</div>
        </div>
	</div>

</section>