<script>
	var base = location.protocol + '//' + location.hostname;
	$(function() {
		$('.mask-cep').blur(function() {
			var area = $(this).attr('id').split('_')[1];
			var cep = $('#cep' + '_' + area).val();
			if (cep !== '') {
				$.ajax({
					url: base + '/admin/configs/web-service/' + cep,
					type: 'get',
					async: true,
					beforeSend: desabilita(area),
					success: function(resposta) {
						$('#rua_' + area).val(resposta.split('|')[0]);
						$('#bairro_' + area).val(resposta.split('|')[1]);
						$('#cidade_' + area).val(resposta.split('|')[2]);
						$('#uf_' + area).val(resposta.split('|')[3]);
						habilita(area);
					}
				});
			}
		});

		$('.ajax-loading_representante').hide('fast');
	});

	function desabilita(area) {
		$('.ajax-loading' + '_' + area).show('fast');
	}

	function habilita(area) {
		$('.ajax-loading' + '_' + area).hide('fast');
	}
</script>

<section class="content">

	<div class="row">
		<div class="col-md-12">
			<div class="nav-tabs-custom">
				<ul class="nav nav-tabs">
					<li class="active"><a href="#senha" data-toggle="tab">Configurações do site - Informações de contato</a></li>
				</ul>

				<div class="tab-content">
					<div class="active tab-pane" id="senha">
						<form class="form-horizontal" role="form" method="post" enctype="multipart/form-data">
							<input type="hidden" name="system" value="true">
							<input type="hidden" name="codigo" value="1">

							<div class="form-group">
								<label for="nome_site" class="col-sm-2 control-label">Nome do site</label>
								<div class="col-sm-2">
									<input type="text" class="form-control" id="nome_site" value="<?= $config[0]->nome_site; ?>" name="nome_site">
								</div>
							</div>
							<div class="form-group">
								<label for="cnpj" class="col-sm-2 control-label">CNPJ</label>
								<div class="col-sm-2">
									<input type="text" class="form-control mask-cnpj" id="cnpj" value="<?= $config[0]->cnpj; ?>" name="cnpj">
								</div>
							</div>

							<div class="form-group">
								<label for="cep_representante" class="col-sm-2 control-label">CEP</label>
								<div class="col-sm-2">
									<input type="text" class="form-control mask-cep" id="cep_representante" value="<?= $config[0]->cep; ?>" name="cep" onkeypress="mascara(this, mcep);">
								</div>
								<label class="col-sm-1 ajax-loading_representante" style="display: none;"><i class="fa fa-spinner fa-pulse"></i></label>
							</div>

							<div class="form-group">
								<label for="rua_representante" class="col-sm-2 control-label">Endereço</label>
								<div class="col-sm-4">
									<input type="text" class="form-control" id="rua_representante" value="<?= $config[0]->rua; ?>" name="rua">
								</div>
								<label for="numero_representante" class="col-sm-1 control-label">Nº</label>
								<div class="col-sm-1">
									<input type="text" class="form-control" id="numero_representante" value="<?= $config[0]->numero; ?>" name="numero">
								</div>
							</div>

							<div class="form-group">
								<label for="complemento_representante" class="col-sm-2 control-label">Complemento</label>
								<div class="col-sm-3">
									<input type="text" class="form-control" id="complemento_representante" value="<?= $config[0]->complemento; ?>" name="complemento">
								</div>
							</div>

							<div class="form-group">
								<label for="bairro_representante" class="col-sm-2 control-label">Bairro</label>
								<div class="col-sm-3">
									<input type="text" class="form-control" id="bairro_representante" value="<?= $config[0]->bairro; ?>" name="bairro">
								</div>
							</div>

							<div class="form-group">
								<label for="cidade_representante" class="col-sm-2 control-label">Cidade</label>
								<div class="col-sm-4">
									<input type="text" class="form-control" id="cidade_representante" value="<?= $config[0]->cidade; ?>" name="cidade">
								</div>

								<label for="uf_representante" class="col-sm-1 control-label">Estado</label>
								<div class="col-sm-2">
									<input type="text" class="form-control" id="uf_representante" value="<?= $config[0]->uf; ?>" name="uf" readonly="readonly">
								</div>
							</div>

							<div class="form-group">
								<label for="link" class="col-sm-2 control-label">E-mail</label>
								<div class="col-sm-4">
									<input type="email" class="form-control" id="email" value="<?= $config[0]->email; ?>" name="email">
								</div>
							</div>

							<div class="form-group">
								<label for="telefone1" class="col-sm-2 control-label">Telefone</label>
								<div class="col-sm-2">
									<input type="text" class="form-control mask-telefone" id="telefone" value="<?= $config[0]->telefone; ?>" name="telefone">
								</div>
								<label for="whatsapp" class="col-sm-1 control-label">WhatsApp</label>
								<div class="col-sm-2">
									<input type="text" class="form-control mask-telefone" id="whatsapp" value="<?= $config[0]->whatsapp; ?>" name="whatsapp">
								</div>
							</div>

							<hr>

							<h3>IUGU</h3>

							<div class="form-group">
								<label for="token_iugu_dev" class="col-sm-2 control-label">Token IUGU - DEV</label>
								<div class="col-sm-4">
									<input type="text" class="form-control mask-telefone" id="token_iugu_dev" value="<?= $config[0]->token_iugu_dev; ?>" name="token_iugu_dev">
								</div>
								<label for="token_iugu_prd" class="col-sm-2 control-label">Token IUGU - PRD</label>
								<div class="col-sm-4">
									<input type="text" class="form-control mask-telefone" id="token_iugu_prd" value="<?= $config[0]->token_iugu_prd; ?>" name="token_iugu_prd">
								</div>
							</div>

							<div class="form-group">
								<label for="iugu_conf" class="col-sm-2 control-label">Modo IUGU</label>
								<div class="col-sm-4">
									<select class="form-control" name="iugu_conf">
										<option value="dev" <?php if (isset($config[0]->iugu_conf) && $config[0]->iugu_conf == 'dev') echo 'selected'; ?>>Desenvolvimento</option>
										<option value="prd" <?php if (isset($config[0]->iugu_conf) && $config[0]->iugu_conf == 'prd') echo 'selected'; ?>>Produção</option>
									</select>
								</div>
							</div>

							<div class="form-group form-footer">
								<div class="text-center">
									<button type="submit" class="btn btn-primary">Enviar</button>
								</div>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>