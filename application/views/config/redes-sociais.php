<?php $i = 1; ?>
<script language="javascript" type="text/javascript" src="../assets/js/cadastrar-rede.js"></script>
<script>
    $(function () {
        jQuery(document).delegate('.js-alt-icone', "change", function () {
            var _icone = jQuery(this).val();
            var _pos = jQuery(this).attr('rel');
            jQuery('#show_icone' + _pos).html('<i class="fa fa-2x ' + _icone + '"></i>');
        });
    });
</script>

<section class="content">

	<div class="row">
        <div class="col-md-10">
			<div class="nav-tabs-custom">
				<ul class="nav nav-tabs">
					<li class="active"><a href="#senha" data-toggle="tab">Configurações do site - Redes sociais</a></li>
				</ul>

				<div class="tab-content">
					<div class="active tab-pane" id="senha">
						<form class="form-horizontal" role="form" method="post" enctype="multipart/form-data">
							<input type="hidden" name="system" value="true" />

							<!-- LOOP ITEM -->
							<?php
							if (count($redes) > 0) {
								foreach ($redes as $rede) {
									?>
									<div class="row dev_<?= $rede->codigo; ?>">
										<div class="col-xs-12 col-md-3" style="margin-top: 5px;">
											<input id="item" type="text" class="form-control" placeholder="Nome da rede social <?= $i; ?>" value="<?= $rede->item; ?>" name="red[ <?= $i; ?>][item]" required>
										</div>
										<div class="col-xs-12 col-md-4" style="margin-top: 5px;">
											<input type="text" class="form-control mask-link" placeholder="Link para o perfil <?= $i; ?>" value="<?= $rede->link; ?>" name="red[ <?= $i; ?>][link]" required>
										</div>
										<div class="col-xs-12 col-md-3" style="margin-top: 5px;">
											<select class="form-control js-alt-icone" rel="_<?= $i; ?>" name="red[ <?= $i; ?>][icone]" required>
												<option value="">-- Ícone</option>
												<?php
												foreach ($icones as $icone) {
													$iconeEscolhido = $rede->icone;

													$selected = '';
													if ($icone->icone == $rede->icone) {
														$selected = 'selected="selected"';
													}
													?>
													<option value="<?= $icone->icone; ?>" <?= $selected; ?>><?= $icone->nome; ?></option>
													<?php
												}
												?>
											</select>
										</div>
										<div class="col-sm-1 col-xs-12 show_icon text-center" id="show_icone_<?= $i; ?>">
											<i class="fa fa-2x <?= $iconeEscolhido; ?>" style="margin-top: 5px;" id="js-icone_<?= $i; ?>"></i>
											&nbsp;&nbsp;
										</div>
										<div class="col-sm-1 col-xs-12 show_icon text-center" id="show_icone_<?= $i; ?>">
											<button href="javascript:void(0);" class="btn btn-danger js-excluir" id="excluir_<?= $rede->codigo; ?>" style="margin-top: 3px;">
												<i class="fa fa-remove"></i>
											</button>
										</div>
									</div>
									<?php
									$i++;
								}
							} else {
								?>
								<div class="row dev_1">
									<div class="col-xs-12 col-md-3" style="margin-top: 5px;">
										<input type="text" class="form-control" placeholder="Nome da rede social 1" value="" name="red[1][item]" required>
									</div>
									<div class="col-xs-12 col-md-4" style="margin-top: 5px;">
										<input type="text" class="form-control mask-link" placeholder="Link para o perfil 1" value="" name="red[1][link]" required>
									</div>
									<div class="col-xs-12 col-md-3" style="margin-top: 5px;">
										<select class="form-control js-alt-icone" rel="_1" name="red[1][icone]" required>
											<option value="">-- Ícone</option>
											<?php
											foreach ($icones as $icone) {
												$iconeEscolhido = $rede->icone;
												?>
												<option value="<?= $icone->icone; ?>"><?= $icone->nome; ?></option>
												<?php
											}
											?>
										</select>
									</div>
									<div class="col-sm-2 col-xs-12 show_icon text-center" id="show_icone_1">
										<i class="fa fa-2x <?= $iconeEscolhido; ?>" style="margin-top: 5px;" id="js-icone_1"></i>
										&nbsp;&nbsp;
									</div>
								</div>
								<?php
							}
							?>



							<!--// END ITEMS -->

							<!--ADICAO DE REDE SOCIAL -->
							<div class="form-group">
								<div id="redes" class="col-md-12">
									<input type="hidden" value="<?= $i; ?>" id="n_redes" class="" />
								</div>
							</div>

							<div class="form-group">
								<div class="col-sm-11 text-right">
									<button type="button" class="btn btn-primary" id="addRede">
										<i class="fa fa-plus"></i>&nbsp; Adicionar rede
									</button>
								</div>
							</div>

							<div class="form-group form-footer">
								<div class="col-sm-12 text-center">
									<button type="submit" class="btn btn-primary">Enviar</button>
								</div>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>