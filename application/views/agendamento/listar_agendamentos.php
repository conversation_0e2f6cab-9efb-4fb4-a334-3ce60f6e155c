<div class="row wrapper border-bottom white-bg page-heading">
	<div class="col-lg-8">
		<h2> Listar Agendamentos</h2>
		<ol class="breadcrumb">
			<li class="breadcrumb-item">
				<a href="<?php echo base_url(); ?>">Home</a>
			</li>
			<li class="breadcrumb-item">
				Agendamentos
			</li>
			<li class="breadcrumb-item active">
				<strong>Listar Agendamentos</strong>
			</li>
		</ol>
	</div>
</div>
<div class="wrapper wrapper-content animated fadeInRight">
	<div class="row">
		<div class="col-lg-12">
			<div class="ibox ">
				<div class="ibox-title">
					<h5>Lista de todos os agendamentos</h5>
				</div>
				<div class="ibox-content">

					<div class="table-responsive">
						<table class="table table-striped table-bordered table-hover dataTables-example">
							<thead>
								<tr>
									<th>Nome</th>
									<th>Fone</th>
									<th>Data</th>
									<th>Hora</th>
									<th>Serviço</th>
									<th>Atendente</th>
									<th>Ações</th>
								</tr>
							</thead>
							<tbody>
								<?php foreach ($agendamentos as $agend) { ?>
									<tr class="gradeX">
										<td><?php echo $agend->nome_cliente ?></td>
										<td><?php echo $agend->fone ?></td>
										<td><?php echo date_mysql_to_human($agend->data) ?></td>
										<td><?php echo $agend->hora ?></td>
										<td class="center"><?php echo $agend->nome_servico ?></td>
										<td class="center">
											<i class="fa fa-circle text-warning" style="color: <?php echo $agend->cor_profissional ?> !important; margin-right: 3px;"></i>
											<?php echo $agend->nome_profissional . ' ( ' . $agend->cod_profissional . ' )' ?>
										</td>
										<td>
											<!-- <a class="link-modal-dados btn btn-info btn-xs" href="<?php echo base_url('clientes/dados/' . $agend->cod_agendamento); ?>"><small><i class="fa fa-search"></i></small> Detalhes</a> -->
											<a href="<?php echo base_url('edit-scheduling/' . $agend->cod_agendamento) ?>" class="btn btn-primary btn-xs">Editar</a>
											<a href="<?php echo base_url('delete-sheduling/' . $agend->cod_agendamento) ?>" class="btn btn-danger btn-xs">Excluir</a>
										</td>
									</tr>
								<?php } ?>
							</tbody>
						</table>
					</div>

				</div>
			</div>
		</div>
	</div>
</div>

<div class="modal fade" id="modal-dados" tabindex="-1">
	<div class="modal-dialog  modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal">
					<span aria-hidden="true">&times;</span>
				</button>
				<p class="modal-title" id="myModalLabel"><b>Detalhes do Agendamento</b></p>
			</div>
			<div class="col-md-12">
				<div class="modal-body"></div>
			</div>
			<div class="modal-footer">
				<!--  <button type="submit" class="btn btn-info" onclick="enviar_agendamento(event);" > Salvar Agendamento</button> -->
				<button type="button" class="btn btn-gray" data-dismiss="modal">Sair</button>
			</div>
		</div>
	</div>
</div>