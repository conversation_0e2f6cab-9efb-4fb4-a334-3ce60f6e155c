<div class="row wrapper border-bottom white-bg page-heading">
    <div class="col-lg-9">
        <h2> Lembre<PERSON></h2>
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="<?php echo base_url(); ?>">Home</a>
            </li>
            <li class="breadcrumb-item">
                Agendamentos
            </li>
            <li class="breadcrumb-item active">
                <strong>Lembretes</strong>
            </li>
        </ol>
    </div>
</div>

<div class="row">
    <div class="col-lg-4 mt-4">
        <div class="ibox ">
            <div class="ibox-title">
                <h5>Lembretes ( Confirmados / Cancelados )</h5>
            </div>
            <div class="ibox-content table-responsive">
                <?php if (!empty($alllembretes)) { ?>
                    <table class="table table-hover no-margins">
                        <thead>
                            <tr>
                                <th>Status</th>
                                <th>Titulo</th>
                                <th>Data Status</th>
                            </tr>
                        </thead>
                        <tbody>

                            <?php foreach ($alllembretes as $lemb) { ?>
                                <tr>
                                    <td>
                                        <?php if ($lemb->is_deleted == '1') { ?>
                                            <span class="label label-danger">Cancelado</span>
                                        <?php } ?>
                                        <?php if ($lemb->is_checked == '1') { ?>
                                            <span class="label label-primary">Confirmado</span>
                                        <?php } ?>
                                    </td>
                                    <td><?php echo $lemb->titulo ?></td>
                                    <td><i class="fa fa-clock-o"></i><?php echo $lemb->update_at ?></td>
                                </tr>
                            <?php } ?>
                        </tbody>
                    </table>
                <?php
                } else {
                    echo 'Sem cancelados e sem confirmados.';
                } ?>
            </div>
        </div>
    </div>

    <div class="col-lg-8">
        <div class="wrapper wrapper-content animated fadeInUp">
            <ul class="notes">
                <?php if (!empty($lembretes)) {
                    //for ($i = 0; $i < 10; $i++) {
                        foreach ($lembretes as $lembrete) { ?>
                            <li>
                                <div>
                                    <small><?php echo date_mysql_to_human($lembrete->data_lembrete) ?></small>
                                    <h4><?php echo $lembrete->titulo ?></h4>
                                    <p><?php echo $lembrete->lembrete ?></p>
                                    <a class="align-check-remove" href="<?php echo base_url('delete-sticky-notes/' . $lembrete->cod_lembrete) ?>" data-toggle="tooltip" data-placement="top" title="" data-original-title="Tooltip on top"><i class="fa fa-trash-o"></i></a>
                                    <a href="<?php echo base_url('check-sticky-notes/' . $lembrete->cod_lembrete) ?>"><i class="fa fa-check"></i></a>
                                </div>
                            </li>
                <?php }
                  //  }
                } else {
                    echo 'Não existem lembretes configurados.';
                } ?>
            </ul>
        </div>
    </div>

</div>