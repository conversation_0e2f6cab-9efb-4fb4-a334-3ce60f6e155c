<?php
if (isset($no_agendamento)) { ?>
    <div class="row wrapper border-bottom white-bg page-heading">
        <div class="col-lg-8">
            <h2> Configurações</h2>
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="<?php echo base_url(); ?>">Home</a>
                </li>
                <li class="breadcrumb-item">
                    Agendamento
                </li>
                <li class="breadcrumb-item active">
                    Agendamentos não encontrado
                </li>
            </ol>
        </div>
    </div>
<?php } else { ?>

    <?php
    if (isset($agendamento) && !empty($agendamento)) {
        $chamada = 'save-edit-scheduling';
        $titulo = 'Reagendamento';
        $text_button = 'Reagendar';
    } else {
        $chamada = 'save-scheduling';
        $titulo = 'Novo agendamento';
        $text_button = 'Agendar';
    }
    ?>
    <div class="row wrapper border-bottom white-bg page-heading">
        <div class="col-lg-8">
            <h2><?php echo $titulo ?></h2>
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="<?php echo base_url(); ?>">Home</a>
                </li>
                <li class="breadcrumb-item">
                    Agendamentos
                </li>
                <li class="breadcrumb-item active">
                    <strong><?php echo $titulo ?></strong>
                </li>
            </ol>
        </div>
    </div>

    <div class="wrapper wrapper-content">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox ">
                    <div class="ibox-title">
                        <h5>Novo Agendamento</h5>
                    </div>
                    <div class="ibox-content">
                        <form name="formulario" action="<?php echo base_url($chamada) ?>" method="POST">
                            <div class="box-body">
                                <div class="row">
                                    <div class="col-md-12">
                                        <!-- Custom Tabs -->
                                        <div class="nav-tabs-custom">
                                            <div class="tab-content">
                                                <div class="row">
                                                    <div class="col-md-2">
                                                        <div class="form-group">
                                                            <label>Tipo de agendamento</label>
                                                            <select class="form-control" style="width: 100%;" name="tipo_agendamento" id="tipo_agendamento" onChange="tipodeagendamento();">
                                                                <option value="1" <?php if (isset($agendamento) && $agendamento->tipo_agendamento == 1) echo 'selected'; ?>>Profissional</option>
                                                                <option value="2" <?php if (isset($agendamento) && $agendamento->tipo_agendamento == 2) echo 'selected'; ?>>Pessoal</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row bloco-agendamentos-profissional">

                                                    <div class="col-md-3">
                                                        <div class="form-group">
                                                            <label for="exampleInputEmail1">Cliente</label>
                                                            <select class="select2_cliente form-control"  name="cod_cliente">
                                                            <?php if (!empty($clientes)) {
                                                                    foreach ($clientes as $cliente) : ?>
                                                                        <option <?php if (isset($agendamento) && $agendamento->codcliente == $cliente->codcliente) echo 'selected'; ?> value="<?php echo $cliente->codcliente ?>"><?php echo $cliente->nome ?> - <?php if(isset($cliente->fone)){ echo $cliente->fone; }?></option>
                                                                    <?php endforeach;
                                                                } else { ?>
                                                                    <option>Não existem clientes cadastrados</option>
                                                                <?php } ?>
                                                            </select>
                                                            
                                                        </div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <div class="form-group">
                                                            <label>Serviço</label>
                                                            <select name="servico" id="servico" class="chosen-select" tabindex="2">
                                                                <?php if (!empty($profissionais)) {
                                                                    foreach ($servicos as $servico) : ?>
                                                                        <option <?php if (isset($agendamento) && $agendamento->id_servico == $servico->id) echo 'selected'; ?> value="<?php echo $servico->id ?>"><?php echo $servico->nome ?>
                                                                        </option>
                                                                    <?php endforeach;
                                                                } else { ?>
                                                                    <option>Não existem serviços cadastrados</option>
                                                                <?php } ?>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-2">

                                                        <div class="form-group">
                                                            <label>Profissional do Atendimento</label>
                                                            <select name="profissional" id="profissional" class="chosen-select" tabindex="2">
                                                                <?php if (!empty($profissionais)) {
                                                                    foreach ($profissionais as $profissional) : ?>
                                                                        <option <?php if (isset($agendamento) && $agendamento->cod_profissional == $profissional->cod_profissional) echo 'selected'; ?> value="<?php echo $profissional->cod_profissional ?>"> <?php echo $profissional->nome ?> - <?php echo $profissional->tipo_profissional ?>
                                                                        </option>
                                                                    <?php endforeach;
                                                                } else { ?>
                                                                    <option>Não existem profissionais cadastrados</option>
                                                                <?php } ?>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <div class="form-group" id="calendarform">
                                                            <label class="font-normal">Data Atendimento</label>
                                                            <div class="input-group date">
                                                                <span class="input-group-addon"><i class="fa fa-calendar"></i></span><input type="text" class="form-control" name="data_profissional" value="<?php echo date_mysql_to_human(date('Y-m-d')); ?>">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <label>Hora Atendimento</label>
                                                        <div class="input-group clockpicker" data-autoclose="true">
                                                            <input type="text" class="form-control" name="hora_profissional" value="09:30">
                                                            <span class="input-group-addon">
                                                                <span class="fa fa-clock-o"></span>
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>


                                                <div class="row bloco-agendamentos-pessoal esconder">

                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label for="exampleInputEmail1">Compromisso</label>
                                                            <input type="text" class="form-control" id="compromisso" name="compromisso" value="<?php if (isset($cliente)) echo $cliente->email ?>" placeholder="Compromisso">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="form-group" id="calendarform">
                                                            <label class="font-normal">Data</label>
                                                            <div class="input-group date">
                                                                <span class="input-group-addon"><i class="fa fa-calendar"></i></span><input type="text" name="data_pessoal" class="form-control" value="<?php echo date_mysql_to_human(date('Y-m-d')); ?>">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <label>Hora</label>
                                                        <div class="input-group clockpicker" data-autoclose="true">
                                                            <input type="text" class="form-control" name="hora_pessoal" value="09:30">
                                                            <span class="input-group-addon">
                                                                <span class="fa fa-clock-o"></span>
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>

                                                <hr>

                                                <div class="row">
                                                    <div class="col-md-10">
                                                        <div class="form-group">
                                                            <label for="exampleInputEmail1">Observações</label>
                                                            <input type="text" class="form-control" name="observacoes_internas" value="<?php if (isset($cliente)) echo $cliente->obs_interna ?>">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <div class="form-group">
                                                            <label>Status Agendamento</label>
                                                            <select class="form-control" style="width: 100%;" name="status_agendamento">
                                                                <option value="1" <?php if (isset($cliente) && $cliente->status == '1') echo 'selected'; ?>>Ativo</option>
                                                                <option value="0" <?php if (isset($cliente) && $cliente->status == '0') echo 'selected'; ?>>Inativo</option>
                                                            </select>
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </div>

                            </div>
                            <hr>
                            <!-- <input type="hidden" name="idAgen" value="<?php if (isset($cliente)) echo $cliente->cod_agendamento ?>"> -->
                            <!--<input type="hidden" name="hash" value="<?php echo $hash ?>"> -->
                            <div class="box-footer mb-5">
                                <button type="submit" class="btn btn-primary pull-right"><?php echo $text_button ?></button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php } ?>