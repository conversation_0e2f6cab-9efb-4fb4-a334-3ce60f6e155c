<?php
if (isset($cliente) && !empty($cliente)) {
	$chamada = 'editar-cliente';
	$titulo = 'Editar cliente - ' . $cliente->razao;
} else {
	$chamada = 'salvar-cliente';
	$titulo = 'Dados novo cliente';
}
?>

<section class="content">
	<?php if (isset($resp) && !empty($resp)) { ?>
		<div class="<?php echo $class ?>">
			<button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
			<h5><i class="icon fa fa-check"></i><?php echo $mensagem ?></h5>
		</div>
	<?php } ?>

    <div class="box box-info">
        <div class="box-header with-border">
			<h3 class="box-title"><?php echo $titulo ?></h3>

			<div class="box-tools pull-right">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
				<button type="button" class="btn btn-box-tool" data-widget="remove"><i class="fa fa-remove"></i></button>
			</div>
		</div>
		
        <!-- /.box-header -->
        <form id="formCadastroPlano" name="formulario" action="<?php echo base_url($chamada) ?>" method="POST">
			<div class="box-body">
				<div class="row">
					<div class="col-md-12">
						<!-- Custom Tabs -->
						<div class="nav-tabs-custom">
							<ul class="nav nav-tabs">
								<li class="active"><a href="#tab_1" data-toggle="tab">Cliente</a></li>
								<li><a href="#tab_2" data-toggle="tab">Dependentes</a></li>
								<li><a href="#tab_3" data-toggle="tab">Cartões</a></li>
								<li><a href="#tab_4" data-toggle="tab">Observações</a></li>
							</ul>
							<div class="tab-content">
								<div class="tab-pane active" id="tab_1">
									<div class="row">
										<div class="col-md-2">
											<div class="form-group">
												<label for="exampleInputEmail1">Data Cadastro</label>
												<input type="text" class="form-control" id="data_cadastro" name="data_cadastro" 
													   value="<?php if (isset($cliente)) echo date_mysql_to_human($cliente->data_cadastro) ?>" 
													   placeholder="<?php echo date_mysql_to_human(date('Y-m-d')) ?>" disabled>
											</div>
										</div>
										<div class="col-md-2">
											<!-- <div class="form-group">
												<label for="exampleInputEmail1">Cadastrado por</label>
												<input type="text" class="form-control" id="id_cadastrante" name="id_cadastrante" value="<?php if (isset($cliente)) echo $cliente->cadastrado_por ?>" placeholder="<?php echo $nome_usuario ?>" disabled>
											</div> -->
										</div>
										<div class="col-md-3">
											<div class="form-group">
												<label for="exampleInputEmail1">CPF</label>
												<input type="text" class="form-control" id="cpf" name="cpf" onblur="getClienteCpf()" value="<?php if (isset($cliente)) echo $cliente->cpf ?>">
											</div>
										</div>

										<div class="col-md-5">
											<div class="form-group">
												<label for="exampleInputEmail1">Nome</label>
												<input type="text" class="form-control" id="nome" name="nome" 
												value="<?php if (isset($cliente)) echo $cliente->razao ?>" 
												placeholder="Nome"  
												<?php if (!isset($cliente)) echo 'disabled' ?>>
											</div>
										</div>
									</div>

									<div class="row">
										
										<div class="col-md-3">
											<div class="form-group">
												<label for="exampleInputEmail1">Email</label>
												<input type="text" class="form-control" id="email" name="email" onblur="getClienteEmail()" 
												value="<?php if (isset($cliente)) echo $cliente->email ?>" 
												placeholder="Email" 
												<?php if (!isset($cliente)) echo 'disabled' ?>>
											</div>
										</div>
										<div class="col-md-3">
											<div class="form-group">
												<label for="exampleInputEmail1">Telefone</label>
												<input type="text" class="form-control" id="telefone" name="telefone" onkeypress="mascara(this, mtel);" 
												value="<?php if (isset($cliente)) echo $cliente->fone ?>" 
												placeholder="Telefone" 
												<?php if (!isset($cliente)) echo 'disabled' ?>>
											</div>
										</div>
										<div class="col-md-3">
											<div class="form-group">
												<label for="exampleInputEmail1">Data nascimento</label>
												<input type="text" class="form-control" id="data_nascimento" name="data_nascimento" onkeypress="mascara(this, mdata);" 
												value="<?php if (isset($cliente)) echo date_mysql_to_human($cliente->nascimento) ?>" 
												placeholder="Data Nascimento" 
												<?php if (!isset($cliente)) echo 'disabled' ?>>
											</div>
										</div>
										<div class="col-md-3">
											<div class="form-group">
												<label>Genero</label>
												<select class="form-control" style="width: 100%;" name="genero" id="genero"  	<?php if (!isset($cliente)) echo 'disabled' ?>>>
													<option value="M"        <?php if (isset($cliente) && $cliente->genero == 'F') echo 'selected'; ?>>Feminino</option>
													<option value="F"    <?php if (isset($cliente) && $cliente->genero == 'M') echo 'selected'; ?>>Masculino</option>
													<option value="O" <?php if (isset($cliente) && $cliente->genero == 'O') echo 'selected'; ?>>Outros</option>
												</select>
											</div>
										</div>
									</div>
									<hr>
									<div class="row">
										<div class="col-md-2">
											<div class="form-group">
												<label for="exampleInputEmail1">CEP</label>
												<input type="text" class="form-control" id="cep" name="cep" onblur="pesquisacep(this.value);" onkeypress="mascara(this, mcep);" 
												value="<?php if (isset($cliente)) echo $cliente->cep ?>" 
												placeholder="0000-000" 
												<?php if (!isset($cliente)) echo 'disabled' ?>>
											</div>
										</div>

										<div class="col-md-5">
											<div class="form-group">
												<label for="exampleInputEmail1">Endereço</label>
												<input type="text" class="form-control" id="endereco" name="endereco" 
												value="<?php if (isset($cliente)) echo $cliente->endereco ?>" 
												placeholder="Endereço" 
												<?php if (!isset($cliente)) echo 'disabled' ?>>
											</div>
										</div>

										<div class="col-md-3">
											<div class="form-group">
												<label for="exampleInputEmail1">Bairro</label>
												<input type="text" class="form-control" id="bairro" name="bairro" 
												value="<?php if (isset($cliente)) echo $cliente->bairro ?>" 
												placeholder="Bairro" 
												<?php if (!isset($cliente)) echo 'disabled' ?>>
											</div>
										</div>

										<div class="col-md-2">
											<div class="form-group">
												<label for="exampleInputEmail1">Número</label>
												<input type="text" class="form-control" id="numero" name="numero" 
												value="<?php if (isset($cliente)) echo $cliente->numero ?>" 
												placeholder="Número" 
												<?php if (!isset($cliente)) echo 'disabled' ?>>
											</div>
										</div>

										
									</div>


									<div class="row">

										<div class="col-md-3">
											<div class="form-group">
												<label for="exampleInputEmail1">Complemento </label>
												<input type="text" class="form-control" id="complemento" name="complemento" 
												value="<?php if (isset($cliente)) echo $cliente->complemento ?>" 
												placeholder="Complemento" 
												<?php if (!isset($cliente)) echo 'disabled' ?>>
											</div>
										</div>

										<div class="col-md-2">
											<div class="form-group">
												<label for="exampleInputEmail1">Cidade</label>
												<input type="text" class="form-control" id="cidade" name="cidade" 
												value="<?php if (isset($cliente)) echo $cliente->cidade ?>" 
												placeholder="Cidade" 
												<?php if (!isset($cliente)) echo 'disabled' ?>>
											</div>
										</div>

										<div class="col-md-5">
											<div class="form-group">
												<label for="exampleInputEmail1">UF</label>
												<input type="text" class="form-control" id="uf" name="uf"
												value="<?php if (isset($cliente)) echo $cliente->uf ?>" 
												placeholder="UF" 
												<?php if (!isset($cliente)) echo 'disabled' ?>>
											</div>
										</div>
										

									</div>

									<hr>

									<div class="row">
										<div class="col-md-7">
											
										</div>

										<div class="col-md-3">
											<div class="form-group">
												<label for="exampleInputEmail1">Promotor</label>
												<input type="text" class="form-control" id="promotor" name="promotor" value="<?php if (isset($cliente)) echo $cliente->promotor ?>" placeholder="Promotor">
											</div>
										</div>
										<div class="col-md-2">
											<div class="form-group">
												<label>Status cliente</label>
												<select class="form-control" style="width: 100%;" name="status_cliente">
													<option value="1" <?php if (isset($cliente) && $cliente->status == '1') echo 'selected'; ?> >Ativo</option>
													<option value="0" <?php if (isset($cliente) && $cliente->status == '0') echo 'selected'; ?>>Inativo</option>
												</select>
											</div>
										</div>

									</div>


								</div>
								<!-- /.tab-pane -->
								<div class="tab-pane" id="tab_2">
									<div class="row">
										<div class="col-md-6">
											<div class="form-group">
												<label for="exampleInputEmail1">DEPENDENTES</label>
											</div>
										</div>
									</div>
								</div>

								<div class="tab-pane" id="tab_3">
									<div class="row">
										<div class="col-md-12">
											<div class="form-group">
												<label for="exampleInputEmail1">CARTÕES</label>
											</div>
										</div>
									</div>
								</div>


								<div class="tab-pane" id="tab_4">
									<div class="row">
										<div class="col-md-12">
											<div class="form-group">
												<label for="exampleInputEmail1">OBSERVACÕES</label>
												<input type="text" class="form-control" id="observacoes_internas" name="observacoes_internas" value="<?php if (isset($cliente)) echo $cliente->obs_interna ?>" >
											</div>
										</div>
									</div>
								</div>
							</div>

						</div>
					</div>
				</div>

			</div>

			<input type="hidden" name="id_cliente" value="<?php if (isset($cliente)) echo $cliente->id ?>">
			<div class="box-footer">
				Para efetuar o novo cadastro apenas preencha os dados e clique em salvar.
				<button type="submit" class="btn btn-primary pull-right">Salvar Dados</button>
			</div>
        </form>

	</div>

</section>