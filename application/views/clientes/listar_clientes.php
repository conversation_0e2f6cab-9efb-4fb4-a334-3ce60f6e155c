<div class="container-fluid py-4">
	<div class="row mt-4">
		<div class="col-12">
			<div class="card">
				<!-- Card header -->
				<div class="card-header">
					<h5 class="mb-0">Listar Clientes</h5>
					<p class="text-sm mb-0">
						Abaixo a lista de todos os clientes ativos e por ativar.
					</p>
				</div>
				<div class="table-responsive" style="padding: 9px 20px 20px 20px;">
					<table class="table table-flush" id="datatable-clients">
						<thead class="thead-light">
							<tr>
								<th>User ID</th>
								<th>Nome</th>
								<th>Email</th>
								<th>Contato</th>
								<th>Data Cadastro</th>
								<th>Ativo</th>
								<th>Impersonate / Timeline</th>
							</tr>
						</thead>
						<tbody>
							<?php foreach ($clients as $client) { #var_dump($client); die();
							?>
								<tr>
									<td class="text-sm font-weight-normal"><?php echo $client->id ?></td>
									<td class="text-sm font-weight-normal"><?php echo $client->name ?></td>
									<td class="text-sm font-weight-normal"><?php echo $client->email ?></td>
									<td class="text-sm font-weight-normal"><?php echo $client->prefix_number . ' ' . $client->phone_number ?></td>
									<td class="text-sm font-weight-normal"><?php echo date("d/m/Y", strtotime($client->create_at))  ?></td>
									<td class="text-sm font-weight-normal">
										<?php if ($client->user_active == 1) { ?>
											<span class="badge-success badge-sm">Ativo</span>
											<a class="btn btn-primary btn-sm new-button" onclick="updateStatusUser('<?= $client->user_token ?>', 'inactive')">Inativar</a>
										<?php } else { ?>
											<span id='user_<?= $client->user_token ?>' class="badge-sm badge-secondary">Por ativar</span>
											<a class="btn btn-warning btn-sm new-button" onclick="updateStatusUser('<?= $client->user_token ?>', 'active')">Ativar</a>
										<?php } ?>
									</td>

									<td class="text-sm font-weight-normal">
										<a class="btn btn-info btn-sm new-button" onclick="chamarCurlImpersonate('<?= $client->user_token ?>')">Impersonate</a>
										<a class="btn btn-dark btn-sm new-button" href="<?= base_url('customer-timelime?user='.$client->id)?>">Timeline</a>
									</td>
								</tr>
							<?php } ?>
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="modal inmodal fade" id="modal-dados" tabindex="-1" role="dialog" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
				<h4 class="modal-title">Dados do Cliente</h4>
				<small class="font-bold">Abaixo seguem os dados do cliente</small>
			</div>
			<div class="modal-body"></div>

			<div class="modal-footer">
				<button type="button" class="btn btn-white" data-dismiss="modal">Sair</button>
			</div>
		</div>
	</div>
</div>



