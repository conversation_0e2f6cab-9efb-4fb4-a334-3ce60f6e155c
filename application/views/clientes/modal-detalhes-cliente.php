
<div class="row">
    <div class="col-md-12">
             <h3> <?php if(isset($dados_cliente->name) && !empty($dados_cliente->name)) echo $dados_cliente->name ?></h3>
    </div>
</div>
<hr>
<div class="row">
    <div class="col-md-4">
        <h5><b>Data Cadastro: </b>  <?php if(isset($dados_cliente->create_at) && !empty($dados_cliente->create_at)) echo $dados_cliente->create_at ?></h5>
        <h5><b>Nome: </b>  <?php if(isset($dados_cliente->name) && !empty($dados_cliente->name)) echo $dados_cliente->name ?></h5>
        <h5><b>Email: </b>  <?php if(isset($dados_cliente->email) && !empty($dados_cliente->email)) echo $dados_cliente->email ?></h5>
    </div>

    <div class="col-md-4">
        <h5><b>Documento: </b>  <?php if(isset($dados_cliente->nif) && !empty($dados_cliente->nif)) echo $dados_cliente->nif ?></h5>
        <h5><b>Fone: </b>  <?php if(isset($dados_cliente->prefix_number) && !empty($dados_cliente->prefix_number)) echo $dados_cliente->prefix_number.' '. $dados_cliente->phone_number ?></h5>
    </div>

    <div class="col-md-4">
        <!-- <h5><b>Data Nascimento: </b>  <?php if(isset($dados_cliente->nascimento) && !empty($dados_cliente->nascimento)) echo date_mysql_to_human($dados_cliente->nascimento) ?></h5>
        <h5><b>Idade: </b>  <?php if(isset($dados_cliente->promotor) && !empty($dados_cliente->promotor)) echo $dados_cliente->promotor ?></h5> -->
    </div>
</div>
<hr>
<div class="row">
    <div class="col-md-4">
        <!-- <h5><b>Endereço: </b>   <?php if(isset($dados_cliente->endereco) && !empty($dados_cliente->endereco)) echo $dados_cliente->endereco ?></h5>
        <h5><b>Número: </b>  <?php if(isset($dados_cliente->numero) && !empty($dados_cliente->numero)) echo $dados_cliente->numero ?></h5>
        <h5><b>Complemento: </b>  <?php if(isset($dados_cliente->complemento) && !empty($dados_cliente->complemento)) echo $dados_cliente->complemento ?></h5> -->
    </div>

    <div class="col-md-4 ">
        <!-- <h5><b>bairro_freguesia: </b>  <?php if(isset($dados_cliente->bairro_freguesia) && !empty($dados_cliente->bairro_freguesia)) echo $dados_cliente->bairro_freguesia ?></h5>
        <h5><b>cidade_concelho: </b>  <?php if(isset($dados_cliente->cidade_concelho) && !empty($dados_cliente->cidade_concelho)) echo $dados_cliente->cidade_concelho ?></h5>
        <h5><b>uf_distrito: </b>  <?php if(isset($dados_cliente->uf_distrito) && !empty($dados_cliente->uf_distrito)) echo $dados_cliente->uf_distrito ?></h5> -->
    </div>

    <div class="col-md-4">
        <!-- <h5><b>codpostal: </b>  <?php if(isset($dados_cliente->codpostal) && !empty($dados_cliente->codpostal)) echo $dados_cliente->codpostal ?></h5> -->
    </div>
</div>
<hr>
<div class="row ">
<hr>
    <div class="col-md-12">
        <!-- <h5><b>Observações Internas: </b>  <?php echo $dados_cliente->obs_interna ?></h5> -->
    </div>
</div>
