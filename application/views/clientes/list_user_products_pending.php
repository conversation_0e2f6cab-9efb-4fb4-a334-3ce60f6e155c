<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-4">
            <div class="ibox ">

                <div class="ibox-title">

                    <h5><?= $data_client->name ?> </h5>

                    <span class="label label-primary float-right">Ativo</span>

                </div>

                <div class="ibox-content">

                    <div>

                        <!-- <div class="float-right text-right">

                            <span class="bar_dashboard">5,3,9,6,5,9,7,3,5,2,4,7</span>
                            <br />
                            <small class="font-bold"><?= date('Y') ?></small>
                        </div> -->
                        <h4>
                            <small class="m-r"><b>Pacote:</b> 30 Produtos</small><br />
                            <small class="m-r"><b>Documento:</b> <?= $data_client->nif ?></small><br />
                            <small class="m-r"><?= $data_client->email ?></small><br />
                            <small class="m-r">(<?= $data_client->prefix_number ?>) <?= $data_client->phone_number ?></small>

                        </h4>

                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-2">
            <div class="widget style1 navy-bg">
                <div class="row">
                    <div class="col-12 text-right">
                        <span> À venda </span>
                        <h2 class="font-bold"><?= count($products_sale) ?></h2>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-2">
            <div class="widget style1 yellow-bg">
                <div class="row">
                    <div class="col-12 text-right">
                        <span>Revisão</span>
                        <h2 class="font-bold"><?= count($products_revision) ?></h2>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-2">
            <div class="widget style1 red-bg">
                <div class="row">
                    <div class="col-12 text-right">
                        <span>Pendentes</span>
                        <h2 class="font-bold"><?= count($products_pending) ?></h2>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-2">
            <div class="widget style1 red-bg product-inactive">
                <div class="row">
                    <div class="col-12 text-right">
                        <span> Inativos </span>
                        <h2 class="font-bold"><?= count($products_inactive) ?></h2>
                    </div>
                </div>
            </div>
        </div>
        <!-- <div class="col-lg-2">
            <div class="widget style1 lazur-bg">
                <div class="row">
                    <div class="col-12 text-right">
                        <span> Saldo : 20</span>
                        <h2 class="font-bold">0</h2>
                    </div>
                </div>
            </div>
        </div> -->




    </div>
    <div class="row">
        <div class="col-lg-7">

            <div class="ibox ">
                <div class="ibox-title">
                    <h5>Gestão de produtos cliente</h5>
                    <div class="ibox-tools">
                        <a class="collapse-link">
                            <i class="fa fa-chevron-up"></i>
                        </a>

                    </div>
                </div>
                <div class="ibox-content">
                    <div class="row">
                        <!-- <div class="col-sm-4 m-b-xs">
                            <div class="btn-group btn-group-toggle" data-toggle="buttons">
                                <label class="btn btn-sm btn-white lazur-bg mr-2 active">
                                    <a id="list-products-revision" onclick="get_all()"> Todos</a>
                                </label>
                                <label class="btn btn-sm btn-white yellow-bg mr-2">
                                    <a id="list-products-revision" onclick="get_products_revision()"> Revisão</a>
                                </label>
                                <label class="btn btn-sm btn-white red-bg mr-2">
                                    <a id="list-products-revision" onclick="get_products_approval()"> Aprovação</a>
                                </label>
                                <label class="btn btn-sm btn-white navy-bg mr-2">
                                    <a id="list-products-revision" onclick="get_products_revision()"> À venda</a>
                                </label>

                            </div>
                        </div> -->
                        <!-- <div class="col-sm-3">
                            <div class="input-group"><input placeholder="Search" type="text" class="form-control form-control-sm"> <span class="input-group-append"> <button type="button" class="btn btn-sm btn-primary">Go!
                                    </button> </span></div>

                        </div> -->
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped dataTables-user-products">
                            <thead>
                                <tr>
                                    <th>Img</th>
                                    <th>EAN </th>
                                    <th>Titulo </th>
                                    <th>Status</th>
                                    <th>Ação</th>

                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($list_products_client as $product) { ?>
                                    <tr id="line_<?= $product->id ?>" class="linetable">
                                        <td> <img id="img-table-<?= $product->id ?>" src="<?= $product->images ?>" width="40px"></td>
                                        <!-- <td><input type="checkbox" class="i-checks" name="input[]"></td> -->
                                        <td><?= $product->barCode ?></td>
                                        <td><?= $product->title ?></td>
                                        <td><?php
                                            $color = 'label-warning';
                                            if ($product->product_session == 1) {
                                                $color = 'label-danger';
                                            }
                                            if ($product->product_session == 2) {
                                                $color = 'label-primary';
                                            }
                                            if ($product->product_session == 3) {
                                                $color = 'label-warning';
                                            }
                                            if ($product->product_session == 4) {
                                                $color = 'label-warning';
                                            }
                                            if ($product->product_session == 9) {
                                                $color = 'product-inactive';
                                            }
                                            ?>
                                            <span class="label <?= $color ?>"><?= $product->status_label ?></span>
                                        </td>
                                        <td>
                                            <button type="button" onclick="get_product_detais(<?= $product->id ?>)" class="btn btn-success btn-xs">Detalhes produto - <?= $product->id ?></button>
                                        </td>
                                    </tr>
                                <?php } ?>
                            </tbody>
                        </table>
                    </div>
                </div>

            </div>

        </div>
        <div class="col-lg-5">
            <!-- <div class="ibox ">
                <div class="ibox-title">
                    <span class="label label-red float-right red-bg">Pendente</span>
                    <h5>Informações do produto</h5>
                </div>

            </div> -->

            <div class="tabs-container">
                <ul class="nav nav-tabs" role="tablist">
                    <li><a class="nav-link active" data-toggle="tab" href="#tab-1"> Detalhes</a></li>
                    <li><a class="nav-link" data-toggle="tab" href="#tab-2">Descrição</a></li>
                    <li><a class="nav-link" data-toggle="tab" href="#tab-3">Marketplaces</a></li>
                </ul>
                <div class="tab-content">
                    <div role="tabpanel" id="tab-1" class="tab-pane active">
                        <div class="panel-body">
                            <span id="label_mkt" class="label"></span>
                            <p class="mt-2 mb-0" id="cod-mkt">MKT:</p><br>
                            <div class="ibox-content">
                                <div class="row">
                                    <div class="col-4">
                                        <div id="loading-img-product" class="row mb-1 esconder">
                                            <div class="sk-spinner sk-spinner-wave">
                                                <div class="sk-rect1"></div>
                                                <div class="sk-rect2"></div>
                                                <div class="sk-rect3"></div>
                                                <div class="sk-rect4"></div>
                                                <div class="sk-rect5"></div>
                                            </div>
                                        </div>
                                        <img id="img-product-detail" class="mt-2" src="<?= base_url() ?>assets/vip/img/bighub/logo_black.png" width="140px">
                                    </div>
                                    <div class="col-8">
                                        <small class="stats-label"><b>Nome produto</b></small>
                                        <input id='title_product' type="text" class="form-control mt-1 title_product" name=''>

                                        <br>
                                        <small class="stats-label"><b>Ean produto</b></small>
                                        <div class="ean_product" id="ean_product">
                                            <b>...</b>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="ibox-content">
                                <div class="row">
                                    <div class="col-3">
                                        <small class="stats-label"><b>Brand</b></small>
                                        <div class="brand_product" id="brand_product">
                                            <b>...</b>
                                        </div>
                                    </div>


                                    <div class="col-3">
                                        <small class="stats-label"><b>SKU</b></small>
                                        <div class="sku_product" id="sku_product">
                                            <b>...</b>
                                        </div>
                                    </div>

                                    <div class="col-3">
                                        <small class="stats-label"><b>Stock</b></small>
                                        <div class="stock_product" id="stock_product">
                                            <b>...</b>
                                        </div>
                                    </div>

                                    <div class="col-3">
                                        <small class="stats-label"><b>Quantidade</b></small>
                                        <div class="quantity_products" id="quantity_products">
                                            <b>...</b>
                                        </div>
                                    </div>

                                </div>
                            </div>

                            <div class="ibox-content">
                                <div class="row">
                                    <div class="col-12">
                                        <small class="stats-label"><b>Descrição Simples</b></small>
                                        <textarea class="descriptionsoon_product textarea-description" id="descriptionsoon_product" name="descriptionsoon_product" rows="5" cols="27">
                                        </textarea>
                                        <!-- <div class="ibox-content no-padding">
                                            <div class="summernote descriptionsoon_product" id="descriptionsoon_product">

                                            </div>
                                        </div> -->
                                    </div>


                                </div>
                            </div>

                            <div class="ibox-content">
                                <div class="row">
                                    Ao colocar para revisão por favor preencher campo com informação para cliente
                                    <input id='msg-client-revision' type="text" class="form-control mt-1">
                                    <span id='msg-alert-revision' class="form-text m-b-none color-msg-alert esconder ">Por favor preencher o campo acima para produtos em Revisão / Inativar</span>
                                </div>
                            </div>

                            <div id="actions-buttons" class="user-button">
                                <div id="loading-products" class="row mb-1 esconder">
                                    <div class="sk-spinner sk-spinner-wave">
                                        <div class="sk-rect1"></div>
                                        <div class="sk-rect2"></div>
                                        <div class="sk-rect3"></div>
                                        <div class="sk-rect4"></div>
                                        <div class="sk-rect5"></div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <button type="button" id="button-inactive" class="btn btn-yellow btn-sm btn-block product-inactive" disabled>Inativar produto</button>
                                    </div>
                                    <div class="col-md-4">
                                        <button type="button" id="button-revision" class="btn btn-yellow btn-sm btn-block yellow-bg" disabled>Enviar para revisão</button>
                                    </div>
                                    <div class="col-md-4">
                                        <button type="button" id="button-aprove" class="btn btn-primary btn-sm btn-block" disabled>Aprovar produto</button>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                    <div role="tabpanel" id="tab-2" class="tab-pane">
                        <div class="panel-body">
                            <div class="ibox-content">
                                <div class="row">
                                    <div class="col-12">
                                        <small class="stats-label">Descrição</small>
                                        <textarea class="description textarea-description" id="description_product" name="description_product" rows="10" cols="47">
                                        </textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div role="tabpanel" id="tab-3" class="tab-pane">
                        <div class="panel-body">
                            <div id="tabela-products-marketplaces" class="table-responsive-marketplaces">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <div class="row">

        <div class="col-lg-2">
            <div class="border-bottom white-bg dashboard-header">
                <p style="margin-bottom: -10px;"><b>Marktplaces</b></p>
                <ul class="list-group clear-list m-t">
                    <li class="list-group-item fist-item">
                        <span class="label label-danger">1</span> Worten PT
                    </li>
                    <li class="list-group-item">
                        <span class="label label-danger">2</span> Worten ES
                    </li>
                    <li class="list-group-item">
                        <span class="label label-success">0</span> Amazon
                    </li>
                    <li class="list-group-item">
                        <span class="label label-default">0</span> Novo
                    </li>
                </ul>

            </div>
        </div>


    </div>

    <!-- <div class="row">

        <div ng-app="ngAppDemo" class="col-lg-12">
            <div ng-controller="ngAppDemoController">

                <table class="table table-striped dataTables-user-products">
                    <thead>
                        <tr>
                            <th></th>
                            <th>EAN </th>
                            <th>Titulo </th>
                            <th>Status</th>
                            <th>Ação</th>

                        </tr>
                    </thead>
                    <tbody>
                        <tr ng-repeat="friend in friends">
                            <td ng-click="get_products(2)">{{friend.name}} is {{friend.age}} years old.</td>
                            <td ng-click="get_products(2)"><a href="google.com.br" class="i-checks">Teste</a></td>
                        </tr>
                    </tbody>
                </table>
            </div>

        </div>


    </div> -->



    <script src="https://ajax.googleapis.com/ajax/libs/angularjs/1.8.2/angular.min.js"></script>
    <script>
        angular.module('ngAppDemo', []).controller('ngAppDemoController', function($scope) {
            $scope.a = 1;
            $scope.b = 2;

            function successCallback() {

            }

            $scope.get_products = function(test) {
                //alert('teste gil'+test);
                $http.post('/someUrl', data, config).then(successCallback, errorCallback);
            }


            $scope.friends = [{
                    name: 'John',
                    age: 25
                },
                {
                    name: 'Mary',
                    age: 40
                },
                {
                    name: 'Peter',
                    age: 85
                }
            ];
        });
    </script>