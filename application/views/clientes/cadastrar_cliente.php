<?php
if (isset($no_client)) { ?>
	<div class="row wrapper border-bottom white-bg page-heading">
		<div class="col-lg-8">
			<h2> Configurações</h2>
			<ol class="breadcrumb">
				<li class="breadcrumb-item">
					<a href="<?php echo base_url(); ?>">Home</a>
				</li>
				<li class="breadcrumb-item">
					Cliente
				</li>
				<li class="breadcrumb-item active">
					Cliente não existe ou não encontrado
				</li>
			</ol>
		</div>
	</div>
<?php } else { ?>


	<?php if (isset($cliente) && !empty($cliente)) {
		$chamada = 'save-edit-client';
		$titulo = 'Editar Cliente - ' . $cliente->nome;
		$button = 'Salvar Edição';
	} else {
		$chamada = 'save-client';
		$titulo = 'Novo Cliente';
		$button = 'Salvar';
	}
	?>

	<div class="row wrapper border-bottom white-bg page-heading">
		<div class="col-lg-8">
			<h2> Configurações</h2>
			<ol class="breadcrumb">
				<li class="breadcrumb-item">
					<a href="<?php echo base_url(); ?>">Home</a>
				</li>
				<li class="breadcrumb-item">
					Cliente
				</li>
				<li class="breadcrumb-item active">
					<strong><?php echo $titulo ?></strong>
				</li>
			</ol>
		</div>
	</div>

	<div class="wrapper wrapper-content animated fadeIn">

		<div class="row">
			<div class="col-lg-12">
				<div class="tabs-container">
					<ul class="nav nav-tabs" role="tablist">
						<li><a class="nav-link active" data-toggle="tab" href="#tab-1">Cliente</a></li>
						<!-- <li><a class="nav-link" data-toggle="tab" href="#tab-2">Histórico</a></li> -->
					</ul>
					<div class="tab-content">
						<div role="tabpanel" id="tab-1" class="tab-pane active">
							<form action="<?php echo base_url($chamada) ?>" method="POST">
								<div class="panel-body">
									<div class="row">
										<div class="col-md-2">
											<div class="form-group">
												<label for="exampleInputEmail1"><?php echo $language['register_client_data_cadastro']; ?></label>
												<input type="text" class="form-control" id="data_cadastro" name="data_cadastro" value="<?php if (isset($cliente)) echo date_mysql_to_human($cliente->data_cadastro) ?>" placeholder="<?php echo date_mysql_to_human(date('Y-m-d')) ?>" disabled>
											</div>
										</div>
										<div class="col-md-2">
										</div>
										<div class="col-md-3">
											<div class="form-group">
												<label for="exampleInputEmail1"><?php echo $language['register_client_documento']; ?></label>
												<input type="text" class="form-control" id="documento" name="documento" value="<?php if (isset($cliente)) echo $cliente->documento ?>">
											</div>
										</div>

										<div class="col-md-5">
											<div class="form-group">
												<label for="exampleInputEmail1"><?php echo $language['register_client_nome']; ?></label>
												<input type="text" class="form-control" id="nome" name="nome" value="<?php if (isset($cliente)) echo $cliente->nome ?>" placeholder="Nome">
											</div>
										</div>
									</div>

									<div class="row">

										<div class="col-md-3">
											<div class="form-group">
												<label for="exampleInputEmail1"><?php echo $language['register_client_email']; ?></label>
												<input type="text" class="form-control" id="email" name="email" value="<?php if (isset($cliente)) echo $cliente->email ?>" placeholder="Email">
											</div>
										</div>
										<div class="col-md-3">
											<div class="form-group">
												<label for="exampleInputEmail1"><?php echo $language['register_client_fone']; ?></label>
												<input type="text" class="form-control" id="telefone" name="telefone" value="<?php if (isset($cliente)) echo $cliente->fone ?>" placeholder="Telefone">
											</div>
										</div>
										<div class="col-md-3">
											<div class="form-group">
												<label for="exampleInputEmail1"><?php echo $language['register_client_nascimento']; ?></label>
												<input type="text" class="form-control" id="data_nascimento" name="data_nascimento" onkeypress="mascara(this, mdata);" value="<?php if (isset($cliente)) echo date_mysql_to_human($cliente->nascimento) ?>" placeholder="Data Nascimento">
											</div>
										</div>
										<div class="col-md-3">
											<div class="form-group">
												<label><?php echo $language['register_client_genero']; ?></label>
												<select class="form-control" style="width: 100%;" name="genero" id="genero">
													<option value="M" <?php if (isset($cliente) && $cliente->genero == 'M') echo 'selected'; ?>>Masculino</option>
													<option value="F" <?php if (isset($cliente) && $cliente->genero == 'F') echo 'selected'; ?>>Feminino</option>
													<option value="O" <?php if (isset($cliente) && $cliente->genero == 'O') echo 'selected'; ?>>Outros</option>
												</select>
											</div>
										</div>
									</div>
									<hr>
									<div class="row">
										<div class="col-md-2">
											<div class="form-group">
												<label for="exampleInputEmail1"><?php echo $language['register_client_codigo_postal']; ?></label>
												<input type="text" class="form-control" id="codpostal" name="codpostal" onblur="pesquisacep(this.value);" onkeypress="mascara(this, mcep);" value="<?php if (isset($cliente)) echo $cliente->codpostal ?>" placeholder="0000-000">
											</div>
										</div>

										<div class="col-md-5">
											<div class="form-group">
												<label for="exampleInputEmail1"><?php echo $language['register_client_endereco']; ?></label>
												<input type="text" class="form-control" id="endereco_morada" name="endereco_morada" value="<?php if (isset($cliente)) echo $cliente->endereco_morada ?>" placeholder="Endereço">
											</div>
										</div>

										<div class="col-md-2">
											<div class="form-group">
												<label for="exampleInputEmail1"><?php echo $language['register_client_numero']; ?></label>
												<input type="text" class="form-control" id="numero" name="numero" value="<?php if (isset($cliente)) echo $cliente->numero ?>" placeholder="Número">
											</div>
										</div>


										<div class="col-md-3">
											<div class="form-group">
												<label for="exampleInputEmail1"><?php echo $language['register_client_complemento']; ?></label>
												<input type="text" class="form-control" id="complemento" name="complemento" value="<?php if (isset($cliente)) echo $cliente->complemento ?>" placeholder="Complemento">
											</div>
										</div>

									</div>


									<div class="row">

										<div class="col-md-3">
											<div class="form-group">
												<label for="exampleInputEmail1"><?php echo $language['register_client_bairro']; ?></label>
												<input type="text" class="form-control" id="bairro_freguesia" name="bairro_freguesia" value="<?php if (isset($cliente)) echo $cliente->bairro_freguesia ?>" placeholder="Bairro">
											</div>
										</div>


										<div class="col-md-2">
											<div class="form-group">
												<label for="exampleInputEmail1"><?php echo $language['register_client_cidade']; ?></label>
												<input type="text" class="form-control" id="cidade_concelho" name="cidade_concelho" value="<?php if (isset($cliente)) echo $cliente->cidade_concelho ?>" placeholder="Cidade">
											</div>
										</div>

										<div class="col-md-3">
											<div class="form-group">
												<label for="exampleInputEmail1"><?php echo $language['register_client_uf']; ?></label>
												<input type="text" class="form-control" id="uf_distrito" name="uf_distrito" value="<?php if (isset($cliente)) echo $cliente->uf_distrito ?>" placeholder="UF">
											</div>
										</div>

										<div class="col-md-2"></div>

										<div class="col-md-2">
											<label><?php echo $language['register_client_status']; ?></label>
											<select class="form-control" style="width: 100%;" name="status_cliente">
												<option value="1" <?php if (isset($cliente) && $cliente->status == '1') echo 'selected'; ?>>Ativo</option>
												<option value="0" <?php if (isset($cliente) && $cliente->status == '0') echo 'selected'; ?>>Inativo</option>
											</select>
										</div>


									</div>
									<div class="row">
										<div class="col-md-12">
											<div class="form-group">
												<label for="exampleInputEmail1"><?php echo $language['register_client_obs']; ?></label>
												<input type="text" class="form-control" name="observacoes_internas" value="<?php if (isset($cliente)) echo $cliente->obs_interna ?>">
											</div>
										</div>
									</div>

									<hr>
									<input type="hidden" name="cli" value="<?php if (isset($cliente)) echo $cliente->codcliente ?>">
									<div class="box-footer mb-5">
										<button type="submit" class="btn btn-primary pull-right"><?php echo $button ?></button>
									</div>

								</div>
							</form>
						</div>
						<!-- <div role="tabpanel" id="tab-2" class="tab-pane">
						<div class="panel-body">
							<label>Histórico Atendimento</label>
							<div class="row">
								<div class="col-lg-12">
									<div class="ibox ">
										<div class="ibox-content">

											<table class="table">
												<thead>
													<tr>
														<th>Data Atendimento</th>
														<th>Hora Atendimento</th>
														<th>Procedimento</th>
														<th>Valor</th>

													</tr>
												</thead>
												<tbody>
													<tr>
														<td>1</td>
														<td>Mark</td>
														<td>Otto</td>
														<td>@mdo</td>
													</tr>
													<tr>
														<td>2</td>
														<td>Jacob</td>
														<td>Thornton</td>
														<td>@fat</td>
													</tr>
													<tr>
														<td>3</td>
														<td>Larry</td>
														<td>the Bird</td>
														<td>@twitter</td>
													</tr>
												</tbody>
											</table>

										</div>
									</div>
								</div>
							</div>
						</div>
					</div> -->
					</div>


				</div>
			</div>
		</div>

	</div>
<?php } ?>