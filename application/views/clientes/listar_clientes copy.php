<div class="row wrapper border-bottom white-bg page-heading">
	<div class="col-lg-8">
		<h2> Listar Clientes</h2>
		<ol class="breadcrumb">
			<li class="breadcrumb-item">
				<a href="<?php echo base_url(); ?>">Home</a>
			</li>
			<li class="breadcrumb-item">
				Clientes
			</li>
			<li class="breadcrumb-item active">
				<strong>Listar Clientes </strong>
			</li>
		</ol>
	</div>
</div>
<div class="wrapper wrapper-content animated fadeInRight">
	<div class="row">
		<div class="col-lg-12">
			<div class="ibox ">
				<div class="ibox-title">
					<h5>Lista com todos os seus clientes</h5>
				</div>
				<div class="ibox-content">

					<div class="table-responsive">
						<table class="table table-striped table-bordered table-hover dataTables-example">
							<thead>
								<tr>
									
									<th>Nome</th>
									<th>Email</th>
									<th>Contato</th>
									<th>Ações</th>
								</tr>
							</thead>
							<tbody>
								<?php foreach ($clients as $client) { ?>
									<tr>
						
										<td>
										<?php echo $client->name ?>
										</td>
										<td>
											<?php echo $client->email ?>
										</td>
										<td>
											<?php echo $client->prefix_number.' '.$client->phone_number ?>
										</td>

										<td>
											<div class="" id="td_default_<?php echo $client->id ?>">
												<a class="link-modal-dados btn btn-info btn-xs" href="<?php echo base_url('data-client/' . $client->id); ?>"><small><i class="fa fa-search"></i></small> Detalhes</a>
												<!-- <button onclick="excluir_cliente('<?php echo $client->id ?>');"  class="btn btn-danger btn-xs">Excluir</button> -->
												
											</div>
											<!-- onclick="return confirm('Ao excluir esse cliente, TODOS os agendamentos vinculados a ele também serão excluidos');" -->
											<div class="esconder" id="td_conf_delete_<?php echo $client->id ?>">
												<h5>Ao excluir esse cliente, TODOS os agendamentos vinculados a ele também serão excluidos.</h5>
												<a href="<?php echo base_url('delete-client/'. $client->id)?>" class="btn btn-danger btn-xs">Eu quero excluir</a>
												<button onclick="reverter_excluir_cliente('<?php echo $client->id ?>');"  class="btn btn-alert btn-xs">Cancelar exclusão</button>
											</div>
											
										</td> 
									</tr>
								<?php } ?>

							</tbody>
						</table>
					</div>

				</div>
			</div>
		</div>
	</div>
</div>

<div class="modal inmodal fade" id="modal-dados" tabindex="-1" role="dialog" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
				<h4 class="modal-title">Dados do Cliente</h4>
				<small class="font-bold">Abaixo seguem os dados do cliente</small>
			</div>
			<div class="modal-body"></div>

			<div class="modal-footer">
				<button type="button" class="btn btn-white" data-dismiss="modal">Sair</button>
			</div>
		</div>
	</div>
</div>



<div class="container-fluid py-4">
	<div class="row mt-4">
		<div class="col-12">
			<div class="card">
				<!-- Card header -->
				<div class="card-header">
					<h5 class="mb-0">Clientes</h5>
					<p class="text-sm mb-0">
					</p>
				</div>
				<div class="table-responsive">
					<table class="table table-flush" id="datatable-clients">
						<thead class="thead-light">
							<tr>
								<th>Nome</th>
								<th>Email</th>
								<th>Office</th>
								<th>Age</th>
								<th>Start date</th>
								<th>Salary</th>
							</tr>
						</thead>
						<tbody>
							<tr>
								<td class="text-sm font-weight-normal">Tiger Nixon</td>
								<td class="text-sm font-weight-normal">System Architect</td>
								<td class="text-sm font-weight-normal">Edinburgh</td>
								<td class="text-sm font-weight-normal">61</td>
								<td class="text-sm font-weight-normal">2011/04/25</td>
								<td class="text-sm font-weight-normal">$320,800</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
</div>