<div class="row">
    <div class="col-md-12">
        <h3> <?php if (isset($cliente) && !empty($cliente)) echo $cliente ?></h3>
    </div>
</div>

<?php if (isset($historico) && !empty($historico)) { ?>
    <?php foreach ($historico as $histo) { ?>
        <hr>
        <div class="row">
            <div class="col-md-4">
                <h5><b>Atendente: </b> <?php if (isset($histo->nome_profissional) && !empty($histo->nome_profissional)) echo $histo->nome_profissional ?></h5>
                <h5><b>Serviço: </b> <?php if (isset($histo->nome_servico) && !empty($histo->nome_servico)) echo $histo->nome_servico ?></h5>
            </div>

            <div class="col-md-4">
                <h5><b>Data: </b> <?php if (isset($histo->data) && !empty($histo->data)) echo date_mysql_to_human($histo->data) ?></h5>
                <h5><b>Hora: </b> <?php if (isset($histo->hora) && !empty($histo->hora)) echo $histo->hora ?></h5>
            </div>

            <div class="col-md-4">
                <?php if ($histo->tipo_cancelamento == 0 && $histo->atendido == 0) { ?>
                    <h5><b>Status atendimento:</b></br>Aguardando Atendimento</h5>
                <?php } else if ($histo->tipo_cancelamento == 1) { ?>
                    <h5><b>Status atendimento:</b></br>Cancelado pelo Salão</h5>
                <?php } else if ($histo->tipo_cancelamento == 2) { ?>
                    <h5><b>Status atendimento:</b></br>Cancelado pelo Cliente</h5>
                <?php } else if ($histo->tipo_cancelamento == 5) { ?>
                    <h5><b>Status atendimento:</b></br>Não cancelou, Não compareceu, Não atendeu ligações</h5>
                <?php } if ($histo->atendido == 1) { ?>
                    <h5><b>Status atendimento:</b></br>Atendido</h5>
                <?php } ?>
            </div>
        </div>
        <div class="row">
            <?php foreach($histo->imagens as $img) { ?>
                <div class="col-md-4">
                    <img src="<?php echo $img->link_img ?>" width="250px">
                </div>
            <?php } ?>    
        </div>
        <hr>
    <?php } ?>
<?php } else { ?>

    <hr>
    <div class="row">
        <div class="col-md-12">
            <h5><b>Ainda não á historico para o cliente selecionado.</b></h5>
        </div>
    </div>
    <hr>
<?php } ?>