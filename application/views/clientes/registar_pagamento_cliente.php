<div class="container-fluid my-3 py-3">
    <div class="row mb-5">
        <div class="col-lg-4 mt-lg-0">

            <div class="card" id="basic-info">
                <div class="card-header">
                    <h5>Recebimentos dos Clientes</h5>
                </div>
                <form id="filterForm" action="<?= base_url('insert-payment-customer') ?>" method="POST">
                    <div class="card-body pt-0">
                      

                        <div class="row">
                            <div class="col-12 align-self-center">
                                <label class="form-label mt-4 ms-0">Cliente</label>
                                <select class="form-control" name="seller" id="choices-seller">
                                    <option value="">Selecione o seller</option>
                                    <?php foreach ($clientes as $cliente) { ?>
                                        <option value="<?= $cliente->id ?>"><?= $cliente->id ?> - <?= $cliente->name ?></option>
                                    <?php } ?>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12 align-self-center">
                                <label class="form-label mt-4 ms-0">Pacote</label>
                                <select class="form-control" name="package" id="choices-package">
                                    <option value="">Selecione o pacote</option>
                                    <?php foreach ($packages as $package) { ?>
                                        <option value="<?= $package->id ?>"><?= $package->id ?> - <?= $package->name ?> - <?= $package->price ?></option>
                                    <?php } ?>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12 align-self-center">
                                <label class="form-label mt-4 ms-0">Meses</label>
                                <select class="form-control" name="months" id="choices-months">
                                    <option value="">Selecione os meses</option>
                                        <option value="1">1 Mês - 30 Dias</option>
                                        <option value="2">2 Meses - 60 Dias</option>
                                   </select>
                            </div>
                        </div>

                        <div class="row mt-2">
                            <div class="col-12">
                                <div class="input-group input-group-dynamic">
                                    <label class="form-label">Observação sobre o pagamento Recebido</label>
                                    <textarea class="textarea mt-5" id="meuTextarea" name="observacao" rows="5" cols="55">  </textarea>
                                </div>
                            </div>
                        </div>


                        <div class="row mt-3">
                            <div class="col-md-6 align-self-center">
                            </div>
                            <div class="col-md-6 align-self-center button-filter">
                                <button type="submit" class="btn btn-primary btn-lg">Gravar Recebimento</button>
                            </div>
                        </div>

                    </div>
                </form>
            </div>
        </div>

        <div class="col-lg-8 mt-lg-0">
            <div class="card">
                <!-- Card header -->
                <div class="card-header">
                    <h5 class="mb-0">Recebimentos</h5>
                    <p class="text-sm mb-0">
                        Listagem dos recebimentos
                    </p>
                </div>
                <div class="table-responsive" style="padding: 9px 20px 20px 20px;">
                    <table class="table table-flush" id="datatable-register-payments">
                        <thead class="thead-light">
                            <tr>
                                <th>ID Registo</th>
                                <th>Data Registo</th>
                                <th>User ID</th>
                                <th>Referencia</th>                                
                                <th>Pacote</th>
                                <th>Status</th>
                                <th>Valor</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($payments as $payment) { #var_dump($payment); die();
                            ?>
                                <tr>
                                    <td class="text-sm font-weight-normal"><?= $payment->id_registo ?></td>
                                    <td class="text-sm font-weight-normal"><?= date("d/m/Y H:i:s", strtotime($payment->date_status_payment)) ?></td>
                                    <td class="text-sm font-weight-normal"><?= $payment->user_id ?></td>
                                    <td class="text-sm font-weight-normal"><?= $payment->reference ?></td>
                                    <td class="text-sm font-weight-normal"><?= $payment->name_package ?></td>
                                    <td class="text-sm font-weight-normal"><?= $payment->status_payment ?></td>
                                    <td class="text-sm font-weight-normal"><?= $payment->total_amount ?></td>

                                    <td class="text-sm font-weight-normal">
                                        <a class="link-modal-dados-order btn btn-danger btn-sm new-button" href="<?= base_url('cancel-receipt/' . $payment->id) ?>">Cancelar</a>
                                    </td>
                                </tr>
                            <?php } ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

</div>