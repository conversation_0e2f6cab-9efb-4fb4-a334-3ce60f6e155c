<?php
$codigo_blog = '';
if (isset($blog) && count($blog) > 0) {
	$blog = $blog[0];
	$codigo_blog = $blog->id;
}
?>
<meta id="dir_imgs" content="blog"/>
<meta id="codigo_cadastro" content="<?= $codigo_blog; ?>"/>
<meta id="module" content="blog" />
<section class="content-header">
	<?php if (isset($resp) && !empty($resp)) { ?>
		<div class="row">
			<div class="col-md-12">
				<div class="<?php echo $class ?>">
					<button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
					<h5><i class="icon fa fa-check"></i><?php echo $mensagem ?></h5>
				</div>
			</div>
			<br />
		</div>
	<?php } ?>
</section>

<section class="content">
    <div class="box box-info">
        <div class="box-header with-border">
			<h3 class="box-title">Cadastro de Postagens</h3>
        </div>

        <form id="formCadastro" name="formulario" action="<?= base_url() ?>cadastrar-post" method="POST" enctype="multipart/form-data">
			<input type="hidden" name="system" value="true" />
			<input type="hidden" id="js-codigo-cadastro" name="form[id]" value="<?php echo isset($blog->id) ? $blog->id : NULL; ?>" />
			<div class="box-body">
				<div class="row">
					<div class="col-md-12">
						<!-- Custom Tabs -->
						<div class="nav-tabs-custom">
							<div class="tab-content">

								<div class="row">
									<div class="col-md-4">
										<div class="form-group">
											<label for="titulo">Título</label>
											<input type="text" class="form-control" id="titulo" name="form[titulo]" value="<?php if (isset($blog)) echo $blog->titulo ?>" placeholder="Título">
										</div>
									</div>
									<div class="col-md-4 hide">
										<div class="form-group">
											<label for="slug">Slug</label>
											<input type="text" class="form-control" id="slug" rel="titulo" name="form[slug]" value="<?php if (isset($blog)) echo $blog->slug ?>" placeholder="">
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-md-4">
										<div class="form-group">
											<label for="categoria">Categoria</label>
											<select class="chosen-select chosen-transparent form-control" id="categoria" name="form[categoria]" >
												<option value="">--</option>
												<?php
												foreach ($categorias as $categoria) {
													$selected = "";
													if ($categoria->id == $blog->categoria) {
														$selected = "selected='selected'";
													}
													?>
													<option value="<?= $categoria->id; ?>" <?= $selected; ?>><?= $categoria->categoria; ?></option>
													<?php
												}
												?>

											</select>
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-md-8">
										<textarea id='editor1' name='form[descricao]' class="textarea" placeholder="Digite o conteúdo aqui"
												  style="width: 100%; height: 200px; font-size: 14px; line-height: 18px; border: 1px solid #dddddd; padding: 10px;">
													  <?= isset($blog->descricao) ? $blog->descricao : NULL; ?>
										</textarea>
									</div>
								</div>

								<div class="row">
									<div class="col-md-8">
										<div class="form-group">
											<label class="control-label">Adicionar imagens</label>
											<!--div id="imagem_up" orakuploader="on"></div-->
											<input type="file" name="img_blog">
											<span class="help-block">Recomendado 800 x 600 px</span>
										</div>
									</div>
									<?php
									if (!empty($blog->img_blog)) {
										?>
										<div class="col-sm-3 col-md-3">
											<div class="row">
												<div class="col-md-12">
													<img class="img-responsive" src="<?php echo base_url('..' . DIRECTORY_SEPARATOR . 'assets' . DIRECTORY_SEPARATOR . 'images' . DIRECTORY_SEPARATOR . 'blog' . DIRECTORY_SEPARATOR . $blog->img_blog); ?>">
												</div>
											</div>
										</div>
										<?php
									}
									?>
								</div>

								<div class="row">
									<div class="col-md-3">
										<div class="form-group">
											<label>Status</label>
											<select class="form-control" style="width: 100%;" name="form[status]">
												<option value="1" <?php if (isset($blog) && $blog->status == '1') echo 'selected'; ?> >Ativo</option>
												<option value="0" <?php if (isset($blog) && $blog->status == '0') echo 'selected'; ?>>Inativo</option>
											</select>
										</div>
									</div>
								</div>
							</div>

						</div>
					</div>
				</div>

			</div>

			<input type="hidden" name="id_plano" value="<?php if (isset($blog)) echo $blog->id ?>">
			<div class="box-footer">
				Para efetuar o cadastro do Item apenas preencha os dados e clique em salvar.
				<button type="submit" class="btn btn-primary pull-right">Salvar Dados</button>
			</div>
		</form>

	</div>
</section>

<link type="text/css" href="<?= base_url('assets/plugins/orakuploader/orakuploader.css" rel="stylesheet'); ?>"/>
<script type="text/javascript" src="<?= base_url('assets/plugins/orakuploader/jquery-ui.min.js'); ?>"></script>
<script type="text/javascript" src="<?= base_url('assets/plugins/orakuploader/orakuploader.js'); ?>"></script>
<script type="text/javascript" src="<?= base_url('assets/plugins/orakuploader/scripts.js'); ?>"></script>