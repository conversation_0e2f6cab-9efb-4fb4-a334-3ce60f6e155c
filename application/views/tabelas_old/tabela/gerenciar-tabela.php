<section class="content-header">
	<?php
	if (isset($resp) && !empty($resp)) {
		?>
		<div class="row">
			<div class="col-md-12">
				<div class="<?= $class ?>">
					<button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
					<h5><i class="icon fa fa-check"></i><?= $mensagem ?></h5>
				</div>
			</div>
			<br />
		</div>
	<?php } ?>
</section>

<section class="content">
	<div class="row">
        <div class="col-xs-12">
			<div class="box">

				<div class="box-header">
					<div class="box-body table-responsive no-padding">
						<div class="row">
							<div class="col-md-6 margin-bottom">
								<a class="btn btn-primary" href="<?= base_url('cadastrar-categoria'); ?>">
									<i class="fa fa-plus"></i> novo cadastro
								</a>
							</div>
						</div>
						<?php
						if (count($categorias) > 0) {
							?>
							<table id="tabela-datatable" class="table table-hover">
								<thead>
									<tr>
										<th>#</th>
										<th>Categoria</th>
										<th>Data de cadastro</th>
										<th>Ações</th>
									</tr>
								</thead>
								<tbody>
									<?php foreach ($categorias as $item) { ?>
										<tr>
											<td><?= $item->id; ?></td>
											<td><?= $item->categoria; ?></td>
											<td><?= $item->data_cadastro_formatada; ?></td>
											<td>
												<a href="<?= base_url('cadastrar-categoria/' . $item->id) ?>" class="btn btn-primary btn-xs">Editar</a>
												<a href="<?= base_url('excluir-categoria/' . $item->id) ?>" class="btn btn-danger btn-xs">Excluir</a>
											</td>
										</tr>
									<?php }
									?>
								</tbody>
							</table>
						<?php } else { ?>
							<table class="table table-hover">
								<thead>
									<tr>
										<th>#</th>
										<th>Categoria</th>
										<th>Data de cadastro</th>
										<th>Ações</th>
									</tr>
								</thead>
								<tbody>
									<tr>
										<td colspan="4" class="text-center">
											Nenhum item cadastrado
										</td>
									</tr>
								</tbody>
							</table>
							<?php
						}
						?>
					</div>
				</div>

			</div>
		</div>
	</div>
</section>
