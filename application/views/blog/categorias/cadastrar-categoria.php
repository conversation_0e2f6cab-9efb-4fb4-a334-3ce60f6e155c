<?php
if (isset($categoria) && count($categoria) > 0) {
	$categoria = $categoria[0];
}
?>
<section class="content-header">
	<?php if (isset($resp) && !empty($resp)) { ?>
		<div class="row">
			<div class="col-md-12">
				<div class="<?php echo $class ?>">
					<button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
					<h5><i class="icon fa fa-check"></i><?php echo $mensagem ?></h5>
				</div>
			</div>
			<br />
		</div>
	<?php } ?>
</section>

<section class="content">
    <div class="box box-info">
        <div class="box-header with-border">
			<h3 class="box-title">Cadastro de Categoria</h3>
        </div>

        <form id="formCadastroPlano" name="formulario" action="<?= base_url('cadastrar-categoria') ?>" method="POST">
			<input type="hidden" name="system" value="true" />
			<input type="hidden" name="form[id]" value="<?php echo isset($categoria->id) ? $categoria->id : NULL; ?>" />
			<div class="box-body">
				<div class="row">
					<div class="col-md-12">
						<!-- Custom Tabs -->
						<div class="nav-tabs-custom">
							<div class="tab-content">

								<div class="row">
									<div class="col-md-4">
										<div class="form-group">
											<label for="categoria">Categoria</label>
											<input type="text" class="form-control" id="categoria" name="form[categoria]" value="<?php if (isset($categoria)) echo $categoria->categoria ?>" placeholder="Nome da categoria">
										</div>
									</div>
									<div class="col-md-4 hide">
										<div class="form-group">
											<label for="slug">Slug</label>
											<input type="text" class="form-control" id="slug" rel="categoria" name="form[slug]" value="<?php if (isset($categoria)) echo $categoria->slug ?>" placeholder="">
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-md-3">
										<div class="form-group">
											<label>Status</label>
											<select class="form-control" style="width: 100%;" name="form[status]">
												<option value="1" <?php if (isset($categoria) && $categoria->status == '1') echo 'selected'; ?> >Ativo</option>
												<option value="0" <?php if (isset($categoria) && $categoria->status == '0') echo 'selected'; ?>>Inativo</option>
											</select>
										</div>
									</div>
								</div>
							</div>

						</div>
					</div>
				</div>

			</div>

			<input type="hidden" name="id_plano" value="<?php if (isset($categoria)) echo $categoria->id ?>">
			<div class="box-footer">
				Para efetuar o cadastro do Item apenas preencha os dados e clique em salvar.
				<button type="submit" class="btn btn-primary pull-right">Salvar dados</button>
			</div>
		</form>

	</div>


</section>