<section class="content-header">
	<?php
	if (isset($resp) && !empty($resp)) {
		?>
		<div class="row">
			<div class="col-md-12">
				<div class="<?php
				if (isset($class)) {
					echo $class;
				}
				?> ?>">
					<button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
					<h5><i class="icon fa fa-check"></i><?php
						if (isset($mensagem)) {
							echo $mensagem;
						}
						?></h5>
				</div>
			</div>
			<br />
		</div>
<?php } ?>
</section>

<section class="content">
	<div class="row">
		<div class="col-xs-12">
			<div class="box">

				<div class="box-header">
					<div class="row">
						<div class="col-md-6 margin-bottom">
							<a class="btn btn-primary" href="<?= base_url('cadastrar-post'); ?>">
								<i class="fa fa-plus"></i> novo cadastro
							</a>
						</div>
					</div>
					<div class="box-body table-responsive no-padding">
						<?php
						if (count($blogs) > 0) {
							?>
							<table id="tabela-datatable" class="table table-hover">
								<thead>
									<tr>
										<th>#</th>
										<th>Título</th>
										<th>Alt Img</th>
										<th>Nome da categoria</th>
										<th><i class="fa fa-eye"></i> Views</th>
										<!-- <th><i class="fa fa-thumbs-o-up"></i> Likes</th> -->
										<th>Data de cadastro</th>
										<th>Ações</th>
									</tr>
								</thead>
								<tbody>
	<?php foreach ($blogs as $item) { ?>
										<tr>
											<td><?= $item->id; ?></td>
											<td><?= $item->titulo; ?></td>
											<td><?= $item->alt_img; ?></td>
											<td><?= $item->nome_categoria; ?></td>
											<td><?= $item->view; ?></td>
											<!-- <td>&nbsp;</td> -->
											<td><?= $item->date; ?></td>
											<td>
												<a href="<?= base_url('cadastrar-post/' . $item->id) ?>" class="btn btn-primary btn-xs">Editar</a>
												<a href="<?= base_url('excluir-post/' . $item->id) ?>" class="btn btn-danger btn-xs">Excluir</a>
											</td>
										</tr>
									<?php }
									?>
								</tbody>
							</table>
<?php } else { ?>
							<table class="table table-hover">
								<thead>
									<tr>
										<th>#</th>
										<th>Título</th>
										<th>Slug</th>
										<th><i class="fa fa-eye"></i> Views</th>
										<th><i class="fa fa-thumbs-o-up"></i> Likes</th>
										<th>Data de cadastro</th>
										<th>Ações</th>
									</tr>
								</thead>
								<tbody>
									<tr>
										<td colspan="7" class="text-center">
											Nenhum item cadastrado
										</td>
									</tr>
								</tbody>
							</table>
							<?php
						}
						?>
					</div>
				</div>

			</div>
		</div>
	</div>
</section>
