<section class="content-header">
	<?php if (isset($resp) && !empty($resp)) { ?>
		<div class="row">
			<div class="col-md-1">
				<?php if (isset($_SERVER['HTTP_REFERER'])): ?>
					<a  class="btn btn-primary" href="<?php echo $_SERVER['HTTP_REFERER']; ?>"><i class="fa fa-fw fa-arrow-left"></i> </a>
				<?php endif ?>
				<small></small>
				<br />
			</div>

			<div class="col-md-11">
				<div class="<?php echo $class ?>">
					<button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
					<h5><i class="icon fa fa-check"></i><?php echo $mensagem ?></h5>
				</div>
			</div>
			<br />
		</div>
	<?php }else { ?>
		<?php if (isset($_SERVER['HTTP_REFERER'])): ?>
			<a  class="btn btn-primary" href="<?php echo $_SERVER['HTTP_REFERER']; ?>"><i class="fa fa-fw fa-arrow-left"></i> </a>
		<?php endif ?>
		<small></small>
		<br />
	<?php } ?>
</section>

<section class="content">
	<div class="row">
		<div class="col-md-12">

			<div class="box">
				<div class="box-header">
					<h3 class="box-title">Quem somos
						<small> | Cadastro de conteúdo</small>
					</h3>
				</div>
				<div class="box-body pad">
					<form id="formCadastroPlano" name="formulario"
						  action="<?php echo base_url() ?>Empresa/salvar_conteudo" method="POST">
						<textarea id='editor1' name='conteudo' class="textarea" placeholder="Digite o conteúdo aqui"
								  style="width: 100%; height: 200px; font-size: 14px; line-height: 18px; border: 1px solid #dddddd; padding: 10px;">
									  <?= $conteudo; ?>
						</textarea>
						<input type="hidden" name="system" value="true" />
						<input type="hidden" name="id" value="<?php if (isset($id)) echo $id ?>">
						<div class="box-footer">
							Para efetuar o cadastro do conteúdo apenas preencha os dados e clique em salvar.
							<button type="submit" class="btn btn-primary pull-right">Salvar</button>
						</div>
					</form>
				</div>
			</div>

		</div>
	</div>
</section>

<script src="//cdn.ckeditor.com/4.9.2/full-all/ckeditor.js"></script>
<script>
    CKEDITOR.replace('editor1', {
        height: 300,
        toolbarGroups: [
            {"name": "basicstyles", "groups": ["basicstyles"]},
            {"name": "links", "groups": ["links"]},
            {"name": "paragraph", "groups": ["list", "blocks", "align"]},
            {"name": "document", "groups": ["mode"]},
            {"name": "styles", "groups": ["styles"]}
        ]
                //removeButtons: 'Underline,Strike,Subscript,Superscript,Anchor,Styles,Specialchar'
    });
</script>