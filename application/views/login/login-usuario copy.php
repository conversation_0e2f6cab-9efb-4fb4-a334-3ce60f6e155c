<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<title>Painel Administrativo - Otripulante</title>
		<link rel="shortcut icon" href="<?php echo base_url() ?>assets/images/favicon.png">
		<!-- Tell the browser to be responsive to screen width -->
		<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
		<!-- Bootstrap 3.3.6 -->
		<link rel="stylesheet" href="<?php echo base_url() ?>assets/bootstrap/css/bootstrap.css">
		<!-- Font Awesome -->
		<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.5.0/css/font-awesome.min.css">
		<!-- Ionicons -->
		<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/ionicons/2.0.1/css/ionicons.min.css">
		<!-- Theme style -->
		<link rel="stylesheet" href="<?php echo base_url() ?>assets/dist/css/AdminLTE.min.css">
		<!-- iCheck -->
		<link rel="stylesheet" href="<?php echo base_url() ?>assets/plugins/iCheck/square/blue.css">

		<!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
		<!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
		<!--[if lt IE 9]>
		<script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
		<script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
		<![endif]-->
	</head>
	<body class="hold-transition login-page">
		<div class="login-box">

			<div class="login-box-body">
				<div class="login-logo">
				   <!-- <a href="<?php echo base_url() ?>index2.html"><b>PAINEL </b>ADM</a> -->
					<img src="<?= base_url() ?>assets/images/logo_otrip_always.png" width="300"/>
				</div>
			<!-- <img class="login-box-msg" src="<?= base_url() ?>assets/images/logo.png" width="230"/> -->
			  <!-- <p class="login-box-msg">Usuário e Senha fornecidos</p> -->

				<form action="<?php echo base_url() ?>login/logar" method="POST">
					<div class="form-group has-feedback">
						<input type="text" class="form-control" name="login" placeholder="Email">
						<span class="glyphicon glyphicon-envelope form-control-feedback"></span>
					</div>
					<div class="form-group has-feedback">
						<input type="password" class="form-control" name="senha" placeholder="Senha">
						<?php if (isset($alerta)) { ?>
							<small><?= $alerta; ?></small>
						<?php } ?>
						<span class="glyphicon glyphicon-lock form-control-feedback"></span>
					</div>
					<div class="row">
						<div class="col-xs-8">

						</div>
						<div class="col-xs-4">
							<button type="submit" class="btn btn-primary btn-block btn-flat">Logar</button>
						</div>
					</div>
					<div class="row">

						<div class="col-xs-6">
							<h5>
								<b>Otripulante </b></br>
								<a href="http://www.otripulante.com">www.otripulante.com</a>
							</h5>
						</div>
						<div class="col-xs-4">
						</div>
					</div>
				</form>
				<!-- <a href="#">Esqueci minha senha</a><br> -->
			</div>
		</div>

		<script src="<?php echo base_url() ?>assets/plugins/jQuery/jQuery-2.2.0.min.js"></script>
		<script src="<?php echo base_url() ?>assets/bootstrap/js/bootstrap.js"></script>
		<script src="<?php echo base_url() ?>assets/plugins/iCheck/icheck.min.js"></script>
		<script>
            $(function () {
                $('input').iCheck({
                    checkboxClass: 'icheckbox_square-blue',
                    radioClass: 'iradio_square-blue',
                    increaseArea: '20%' // optional
                });
            });
		</script>
	</body>
</html>
