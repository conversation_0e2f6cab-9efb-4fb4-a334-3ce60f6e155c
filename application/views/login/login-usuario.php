<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <link rel="apple-touch-icon" sizes="76x76" href="<?= base_url() ?>assets/v02/img/apple-icon.png">
  <link rel="icon" type="image/png" href="<?= base_url() ?>assets/v02/img/favicon.png">
  <title>
    BIGhub - Admin
  </title>
  <!--     Fonts and icons     -->
  <link rel="stylesheet" type="text/css" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700,900|Roboto+Slab:400,700" />
  <!-- Nucleo Icons -->
  <link href="<?= base_url() ?>assets/v02/css/nucleo-icons.css" rel="stylesheet" />
  <link href="<?= base_url() ?>assets/v02/css/nucleo-svg.css" rel="stylesheet" />
  <!-- Font Awesome Icons -->
  <script src="https://kit.fontawesome.com/42d5adcbca.js" crossorigin="anonymous"></script>
  <!-- Material Icons -->
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Round" rel="stylesheet">
  <!-- CSS Files -->
  <link id="pagestyle" href="<?= base_url() ?>assets/v02/css/material-dashboard.css?v=3.0.6" rel="stylesheet" />
</head>

<body class="bg-gray-200">

  <main class="main-content  mt-0">
    <div class="page-header align-items-start min-height-300 m-3 border-radius-xl" style="background-image: url('https://images.unsplash.com/photo-1491466424936-e304919aada7?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1949&q=80');">
      <span class="mask bg-gradient-dark opacity-6"></span>
    </div>
    <div class="container mb-4">
      <div class="row mt-lg-n12 mt-md-n12 mt-n12 justify-content-center">
        <div class="col-xl-4 col-lg-5 col-md-7 mx-auto">
          <div class="card mt-8">
            <div class="card-header p-0 position-relative mt-n4 mx-3 z-index-2">
              <div class="bg-gradient-success shadow-success border-radius-lg py-3 pe-1 text-center py-4">
              <img alt="image" src="<?= base_url() ?>assets/vip/img/bighub/logo_black.png" />
                <h4 class="font-weight-bolder text-white mt-1">Sign In</h4>
              </div>
            </div>
            <div class="card-body">
              <form class="m-t text-start" role="form" action="<?php echo base_url() ?>login/logar" method="POST">
                <div class="input-group input-group-static mb-4">
                  <label>Email</label>
                  <input type="email" class="form-control" placeholder="<EMAIL>" name='login'>
                </div>
                <div class="input-group input-group-static mb-4">
                  <label>Password</label>
                  <input type="password" class="form-control" placeholder="•••••••••••••" name='password'>
                </div>
                <div class="form-check form-switch d-flex align-items-center mb-3">
                    <?php if (isset($alerta)) { ?>
						<small><?= $alerta; ?></small>
					<?php } ?>
                  <!-- <input class="form-check-input" type="checkbox" id="rememberMe" checked="">
                  <label class="form-check-label mb-0 ms-3" for="rememberMe">Remember me</label> -->
                </div>
                <div class="text-center">
                  <button type="submit" class="btn bg-gradient-dark w-100 mt-3 mb-0">Sign in</button>
                  <!-- <button type="submit" class="btn btn-primary  btn-red-bh block full-width m-b"><?php echo $login?></button> -->
                </div>
              </form>
            </div>
          
          </div>
        </div>
      </div>
    </div>
    <footer class="footer position-absolute bottom-2 py-2 w-100">
      <div class="container">
        <div class="row align-items-center justify-content-lg-between">
          <div class="col-12 col-md-6 my-auto">
           
          </div>
          <div class="col-12 col-md-6">
            <ul class="nav nav-footer justify-content-center justify-content-lg-end">
              <li class="nav-item">
               
              </li>
            </ul>
          </div>
        </div>
      </div>
    </footer>
  </main>
  <!--   Core JS Files   -->
  <script src="<?= base_url() ?>assets/v02/js/core/popper.min.js"></script>
  <script src="<?= base_url() ?>assets/v02/js/core/bootstrap.min.js"></script>
  <script src="<?= base_url() ?>assets/v02/js/plugins/perfect-scrollbar.min.js"></script>
  <script src="<?= base_url() ?>assets/v02/js/plugins/smooth-scrollbar.min.js"></script>
  <!-- Kanban scripts -->
  <script src="<?= base_url() ?>assets/v02/js/plugins/dragula/dragula.min.js"></script>
  <script src="<?= base_url() ?>assets/v02/js/plugins/jkanban/jkanban.js"></script>
  <script>
    var win = navigator.platform.indexOf('Win') > -1;
    if (win && document.querySelector('#sidenav-scrollbar')) {
      var options = {
        damping: '0.5'
      }
      Scrollbar.init(document.querySelector('#sidenav-scrollbar'), options);
    }
  </script>
  <!-- Github buttons -->
  <script async defer src="https://buttons.github.io/buttons.js"></script>
  <!-- Control Center for Material Dashboard: parallax effects, scripts for the example pages etc -->
  <script src="<?= base_url() ?>assets/v02/js/material-dashboard.min.js?v=3.0.6"></script>
</body>

</html>