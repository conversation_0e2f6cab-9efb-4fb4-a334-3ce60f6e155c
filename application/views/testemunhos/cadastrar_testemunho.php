<?php
if (isset($testemunho) && !empty($testemunho)) {
	$chamada = 'editar-testemunho';
	$titulo = 'Editar Testemunho';
} else {
	$chamada = 'salvar-testemunho';
	$titulo = 'Cadastrar Testemunho';
}
?>
<section class="content-header">
	<?php if (isset($resp) && !empty($resp)) { ?>
		<div class="row">
			<div class="col-md-12">
				<div class="<?php echo $class ?>">
					<button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
					<h5><i class="icon fa fa-check"></i><?php echo $mensagem ?></h5>
				</div>
			</div>
			<br />
		</div>
	<?php } ?>
</section>

<section class="content">
    <div class="box box-info">
        <div class="box-header with-border">
			<h3 class="box-title"><?php echo $titulo ?></h3>
        </div>

		<form id="formCadastroPlano" name="formulario" action="<?php echo base_url($chamada) ?>" method="POST">
			<input type="hidden" name="system" value="true" />
			<div class="box-body">
				<div class="row">
					<div class="col-md-12">
						<!-- Custom Tabs -->
						<div class="nav-tabs-custom">
							<div class="tab-content">

								<div class="row">
									<div class="col-md-4">
										<div class="form-group">
											<label for="cliente">Nome Cliente</label>
											<input type="text" class="form-control" id="cliente" name="cliente" value="<?php if (isset($testemunho)) echo $testemunho->cliente ?>" placeholder="Nome Cliente" required >
										</div>
									</div>
								</div>

								<div class="row">
									<div class="col-md-7">
										<div class="form-group">
											<label for="msg">Testemunho </label>
												<textarea type="text" class="form-control" id="msg" name="msg" value="<?php if (isset($testemunho)) echo $testemunho->msg; ?>" placeholder="Mensagem enviada pelo cliente." required ></textarea>
											<span>Maximo 255 Carcteres</span>
										</div>
									</div>
								</div>
							</div>

						</div>
					</div>
				</div>

			</div>
			<input type="hidden" name="id_testemunho" value="<?php if (isset($testemunho)) echo $testemunho->id ?>">
			<div class="box-footer">
				Para efetuar o cadastro do testemunho apenas preencha os dados e clique em salvar.
				<button type="submit" class="btn btn-primary pull-right">Salvar Dados</button>
			</div>
		</form>

	</div>


</section>