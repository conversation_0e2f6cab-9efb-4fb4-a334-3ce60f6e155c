<section class="content-header">
	<?php if (isset($resp) && !empty($resp)) { ?>
		<div class="row">
			<div class="col-md-1">
				<?php if (isset($_SERVER['HTTP_REFERER'])): ?>
					<a  class="btn btn-primary" href="<?php echo $_SERVER['HTTP_REFERER']; ?>"><i class="fa fa-fw fa-arrow-left"></i> </a>
				<?php endif ?>
				<small></small>
				<br />
			</div>

			<div class="col-md-11">
				<div class="<?php echo $class ?>">
					<button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
					<h5><i class="icon fa fa-check"></i><?php echo $mensagem ?></h5>
				</div>
			</div>
			<br />
		</div>
	<?php }else { ?>
		<?php if (isset($_SERVER['HTTP_REFERER'])): ?>
			<a  class="btn btn-primary" href="<?php echo $_SERVER['HTTP_REFERER']; ?>"><i class="fa fa-fw fa-arrow-left"></i> </a>
		<?php endif ?>
		<small></small>
		<br />
	<?php } ?>
</section>

<section class="content">
	<div class="row">
        <div class="col-xs-12">
			<div class="box">

				<div class="box-header">
					<br>
					<div class="box-body table-responsive no-padding">
						<table id="tabela-datatable" class="table table-hover">
							<thead>
								<tr>
									<th>Cliente</th>
									<th>Testemunho</th>
									<th>Ações</th>
								</tr>
							</thead>
							<tbody>
								<?php foreach ($testemunhos as $t) { ?>
									<tr>
										<td>
											<?php echo $t->cliente ?>
										</td>
										<td>
											<?php echo $t->msg?>
										</td>
										<td>
											
											<a href="<?php echo base_url('testemunhos/editar_testemunho/' . $t->id) ?>" class="btn btn-primary btn-xs">Editar</a>
											<a href="<?php echo base_url('testemunhos/excluir_testemunho/' . $t->id) ?>" class="btn btn-danger btn-xs">Excluir</a>
										</td>
									</tr>
<?php } ?>

							</tbody>

						</table>

					</div>
				</div>
				

			</div>
        </div>
	</div>

</section>

