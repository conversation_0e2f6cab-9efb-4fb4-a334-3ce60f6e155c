<div class="container-fluid py-4">
    <div class="row mt-4">
        <!-- Primeira Tabela -->
        <div class="col-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">List Orders Without Seller</h5>
                    <p class="text-sm mb-0">No seller was found for the orders below</p>
                </div>
                <div class="table-responsive" style="padding: 9px 20px 20px 20px;">
                    <table class="table table-flush" id="datatable-clients">
                        <thead class="thead-light">
                            <tr>
                                <th>Action</th>
                                <th>ID</th>
                                <th>Channel</th>
                                <th>Order ID</th>
                                <th>Quantity</th>
                                <th>SKU</th>
                                <th>Price</th>
                                <th>Order Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($ordernouser as $order) { ?>
                                <tr data-order-id="<?= $order->id ?>">
                                    <td>
                                        <button class="btn btn-dark btn-sm new-button" onclick="openForm('<?= $order->id ?>', '<?= $order->marketplace ?>', '<?= $order->order_id ?>', '<?= $order->price ?>', 'standard', this)">Forward</button>
                                    </td>
                                    <td><?= $order->id ?></td>
                                    <td><?= $order->marketplace ?></td>
                                    <td><?= $order->order_id ?></td>
                                    <td><?= $order->products ?></td>
                                    <td><?= $order->offer_sku ?></td>
                                    <td><?= $order->price ?></td>
                                    <td><?= date("d/m/Y", strtotime($order->order_created_at)) ?></td>
                                </tr>
                            <?php } ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Formulário -->
        <div class="col-4">
            <div class="card" id="form-container" style="display: none;">
                <div class="card-header">
                    <h5>Enter User ID</h5>
                </div>
                <div class="card-body pt-0">
                    <p><strong>Channel:</strong> <span id="order_channel_display"></span></p>
                    <p><strong>Order ID:</strong> <span id="order_id_display"></span></p>
                    <p><strong>Price:</strong> <span id="order_price_display"></span></p>

                    <form id="userForm" onsubmit="sendData(event)">
                        <div class="input-group input-group-dynamic mb-3">
                            <input type="text" class="form-control" id="user_id" placeholder="Enter User ID" required>
                        </div>
                        <input type="hidden" id="order_id">
                        <input type="hidden" id="order_channel">
                        <input type="hidden" id="order_type">
                        <button type="submit" class="btn btn-primary btn-lg">Submit</button>
                        <div class="spinner-border esconder" role="status" id="loading-send-order">
                            <span class="sr-only">Loading...</span>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Segunda Tabela -->
        <div class="col-8 mt-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">List Orders Without Seller - New Forward</h5>
                    <p class="text-sm mb-0">No seller was found for the orders below</p>
                </div>
                <div class="table-responsive" style="padding: 9px 20px 20px 20px;">
                    <table class="table table-flush" id="datatable-clients-new">
                        <thead class="thead-light">
                            <tr>
                                <th>Action</th>
                                <th>ID</th>
                                <th>Channel</th>
                                <th>Order ID</th>
                                <th>Quantity</th>
                                <th>SKU</th>
                                <th>Price</th>
                                <th>Order Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($ordernouser_new_forward as $order) { ?>
                                <tr data-order-id="<?= $order->id ?>">
                                    <td>
                                        <button class="btn btn-dark btn-sm new-button" onclick="openForm('<?= $order->id ?>', '<?= $order->marketplace ?>', '<?= $order->order_id ?>', '<?= $order->price ?>', 'new', this)">Forward</button>
                                    </td>
                                    <td><?= $order->id ?></td>
                                    <td><?= $order->marketplace ?></td>
                                    <td><?= $order->order_id ?></td>
                                    <td><?= $order->products ?? '1' ?></td>
                                    <td><?= $order->product_sku ?></td>
                                    <td><?= $order->price ?></td>
                                    <td><?= date("d/m/Y", strtotime($order->created_at)) ?></td>
                                </tr>
                            <?php } ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    const id_usuario = <?= $id_usuario ?>;
    window.currentRowButton = null;

    function openForm(orderId, orderChannel, orderOrderId, orderPrice, orderType, btn = null) {
        window.currentRowButton = btn;
        window.scrollTo({ top: 0, behavior: 'smooth' });

        document.getElementById("form-container").style.display = "block";
        document.getElementById("order_channel_display").textContent = orderChannel;
        document.getElementById("order_id_display").textContent = orderOrderId;
        document.getElementById("order_price_display").textContent = orderPrice;
        document.getElementById("order_id").value = orderId;
        document.getElementById("order_channel").value = orderChannel;
        document.getElementById("order_type").value = orderType;

        const formHeader = document.querySelector("#form-container .card-header h5");
        formHeader.textContent = orderType === 'new' ? "Enter User ID - New Forward" : "Enter User ID";
    }

    function sendData(event) {
        const loadingSpinner = document.getElementById("loading-send-order");
        loadingSpinner.classList.remove("esconder");

        event.preventDefault();

        const userId = document.getElementById("user_id").value;
        const orderId = document.getElementById("order_id").value;
        const orderChannel = document.getElementById("order_channel").value;
        const orderType = document.getElementById("order_type").value;

        if (!id_usuario) {
            alert("Error: id_usuario is not defined.");
            loadingSpinner.classList.add("esconder");
            return;
        }

        const endpoint = "<?= base_url('forward-order') ?>";

        const requestData = {
            order_id: orderId,
            channel: orderChannel,
            client_id: userId,
            id_usuario: id_usuario
        };

        if (orderType === 'new') {
            requestData.order_type = "new_forward";
        }

        fetch(endpoint, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "Accept": "application/json",
                "X-Requested-With": "XMLHttpRequest"
            },
            body: JSON.stringify(requestData)
        })
        .then(response => {
            if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
            return response.json();
        })
        .then(data => {
            loadingSpinner.classList.add("esconder");

            if (data.status === 'success') {
                alert(data.message);
                document.getElementById("userForm").reset();
                document.getElementById("form-container").style.display = "none";

                if (window.currentRowButton) {
                    const row = window.currentRowButton.closest('tr');
                    if (row) row.remove();
                }
            } else {
                alert(data.message || "Error sending data. Please try again.");
            }
        })
        .catch(error => {
            console.error("Error:", error.message);
            alert("An error occurred while processing your request. Please try again.");
            loadingSpinner.classList.add("esconder");
        });
    }
</script>
