<div class="container-fluid py-4">
	<div class="row mt-4">
		<div class="col-12">
			<div class="card">
				<!-- Card header -->
				<div class="card-header">
					<h5 class="mb-0">Items para compra - <?= $quantity_orders ?></h5>
					<!-- <p class="text-sm mb-0">
						Total de Items : <?= $quantity_orders ?>
					</p> -->
				</div>
				<div class="table-responsive">
					<table class="table table-flush" id="datatable-clients">
						<thead class="thead-light">
							<tr>
								
								<th>Pedido</th>
								<th>Data do pedido</th>
								<th>SKU</th>
								<th>EAN</th>
								<th>Descrição</th>
								<th>Quantidade</th>
								<th>Preço UN</th>
								<th>Preço total</th>
								<th>Status</th>
								<th>Data da compra</th>
								<th>Fornecedor</th>
								<th>Fatura</th>
							</tr>
						</thead>
						<tbody>
							<?php foreach ($items as $item) { #var_dump($item); die();
							?>
								<tr>
									
									<!-- <td>
										<div class="d-flex">
											<div class="form-check my-auto">
												<input class="form-check-input check_<?= $item->id ?>" type="checkbox" id="customCheck1" onclick="checkOrder(<?= $order->id ?>, <?= $order->purchased == 1 ? 0: 1 ?>)" <?= $order->purchased ? 'checked' : '' ?>>
											</div>
										</div>
									</td> -->
									<td class="text-sm font-weight-normal"><?php echo $item->order_id ?></td>
									<td class="text-sm font-weight-normal"><?php echo date("d/m/Y", strtotime($item->data_compra)) ?></td>
									
									<td class="text-sm font-weight-normal"><?php echo $item->sku ?></td>
									<td class="text-sm font-weight-normal"><?php echo $item->ean ?></td>
									<td class="text-sm font-weight-normal"><?php echo $item->title ?></td>
									<td class="text-sm font-weight-normal"><?php echo $item->quantity ?></td>
									<td class="text-sm font-weight-normal"><?php echo $item->price_unit ?></td>
									<td class="text-sm font-weight-normal"><?php echo $item->total_price ?></td>
									<td class="text-sm font-weight-normal"><?php echo $item->state ?></td>

									<td class="text-sm font-weight-normal">-------</td>
									<td class="text-sm font-weight-normal">Fornecedor</td>
									<td class="text-sm font-weight-normal">Fatura</td>
									<!-- <td class="text-sm font-weight-normal">
										<a class="link-modal-dados-order btn btn-info btn-sm new-button" onclick="setId(<?= $order->id ?>)" href="<?= base_url()?>order-details/<?php echo $order->id ?>" data-bs-toggle="modal" data-bs-target="#modal-dados-order">Detalhes</a>
									</td> -->
								</tr>
							<?php } ?>
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="modal fade" id="modal-dados-order" tabindex="-1" role="dialog" aria-labelledby="modal-default" aria-hidden="true">
	<div class="modal-dialog modal-lg modal-dialog-centered modal-" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h6 class="modal-title font-weight-normal" id="modal-title-default">Order Detail</h6>
				<button type="button" class="btn-close text-dark" data-bs-dismiss="modal" aria-label="Close">
					<span aria-hidden="true">×</span>
				</button>
			</div>
			<div class="modal-body">

			</div>

			<div class="modal-footer">
				<button type="button" class="btn btn-link  ml-auto" data-bs-dismiss="modal">Close</button>
			</div>
		</div>
	</div>

</div>