<div class="container-fluid py-4">
	<div class="row mt-4">
		<div class="col-12">
			<div class="card">
				<!-- Card header -->
				<div class="card-header">
					<h5 class="mb-0">Orders</h5>
					<p class="text-sm mb-0">
						Listagem das ordens
					</p>
				</div>
				<div class="table-responsive">
					<table class="table table-flush" id="datatable-clients">
						<thead class="thead-light">
							<tr>
								<th></th>
								<th>Order Id</th>
								<th>Data compra</th>
								
								<th>Itens</th>
								<th>Channel</th>
								<th>User</th>
								<th>State</th>
								<th>Amount</th>
								<th>Actions</th>
							</tr>
						</thead>
						<tbody>
							<?php foreach ($orders as $order) { #var_dump($order->purchased);;
							?>
								<tr>
									
									<td>
										<div class="d-flex">
											<div class="form-check my-auto">
												<input class="form-check-input check_<?= $order->id ?>" type="checkbox" id="customCheck1" onclick="checkOrder(<?= $order->id ?>, <?= $order->purchased == 1 ? 0: 1 ?>)" <?= $order->purchased ? 'checked' : '' ?>>
											</div>
										</div>
									</td>
									<td class="text-sm font-weight-normal"><?php echo $order->reference ?></td>
									<td class="text-sm font-weight-normal"><?php echo date("d/m/Y", strtotime($order->created_date)) ?></td>
									
									<td class="text-sm font-weight-normal"><?php echo count(json_decode($order->items)) ?></td>
									<td class="text-sm font-weight-normal"><?php echo $order->channel ?>/<?php echo $order->country ?></td>
									<td class="text-sm font-weight-normal"><?php echo $order->user_id ?></td>
									<td class="text-sm font-weight-normal">
										<?php
										$state_neuter = ['SHIPPING'];
										$state_positive = ['RECEIVED', 'TO_COLLECT', 'SHIPPED'];
										$state_negative = ['WAITING_ACCEPTANCE', 'WAITING_DEBIT', 'WAITING_DEBIT_PAYMENT', 'CLOSED', 'REFUNDED', 'CANCELED', 'REFUSED', 'AUTO_REFUSED', 'AUTO_CLOSED'];
										if (in_array($order->state, $state_positive)) { ?>
											<span class="badge-success badge-sm"><?= $order->state ?></span>
										<?php } elseif (in_array($order->state, $state_negative)) { ?>
											<span class="badge-sm badge-negative"><?= $order->state ?></span>
										<?php } elseif (in_array($order->state, $state_neuter)) { ?>
											<span class="badge-sm badge-neuter"><?= $order->state ?></span>
										<?php } ?>	
									</td>
									<td class="text-sm font-weight-normal"><?php echo $order->total_price ?></td>

									<td class="text-sm font-weight-normal">
										<a class="link-modal-dados-order btn btn-info btn-sm new-button" onclick="setId(<?= $order->id ?>)" href="<?= base_url()?>order-details/<?php echo $order->id ?>" data-bs-toggle="modal" data-bs-target="#modal-dados-order">Detalhes</a>
										<?php if (in_array($order->channel, ['pccomp', 'pccompes'])) {?>
											<?php if($order->isinvoice){ ?>
												<a target="_blank" class="link-modal-dados-order btn btn-success btn-sm new-button" style="font-size: 10px;" href="https://prd-mkp.bighub.store/invoices/<?= $order->id_invoice ?>.pdf">Ver Fatura</a>
											<?php }else{ ?>
												<div id="invoice-loading-<?= $order->id ?>" class="spinner-border esconder" role="status" style="width: 15px; height: 15px;">
													<span class="sr-only">Loading...</span>
												</div>
												<a id="see-invoice-<?= $order->id ?>" target="_blank" class="link-modal-dados-order btn btn-success btn-sm new-button esconder" style="font-size: 10px;">Ver Fatura</a>
												<button id="generate-invoice-<?= $order->id ?>" class="link-modal-dados-order btn btn-primary btn-sm new-button" onclick="generateInvoice(<?= $order->id ?>)" style="font-size: 10px;">Gerar Fatura</button>
											<?php } ?>
										<?php } ?>
									</td>
								</tr>
							<?php } ?>
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="modal fade" id="modal-dados-order" tabindex="-1" role="dialog" aria-labelledby="modal-default" aria-hidden="true">
	<div class="modal-dialog modal-lg modal-dialog-centered modal-" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h6 class="modal-title font-weight-normal" id="modal-title-default">Order Detail</h6>
				<button type="button" class="btn-close text-dark" data-bs-dismiss="modal" aria-label="Close">
					<span aria-hidden="true">×</span>
				</button>
			</div>
			<div class="modal-body">

			</div>

			<div class="modal-footer">
				<button type="button" class="btn btn-link  ml-auto" data-bs-dismiss="modal">Close</button>
			</div>
		</div>
	</div>

</div>