<div class="container-fluid">
	<div class="row">
		<div class="card-body p-3 pt-0">
			<hr class="horizontal dark mt-0 mb-4">
			<div class="row mt-2">
				<div class="col-lg-12 col-md-6 col-12">
					<ul class="list-group">
						<li class="list-group-item border-0 d-flex p-4 mb-2 bg-gray-100 border-radius-lg">
							<div class="d-flex flex-column">
								<h6 class="mb-3 text-sm">Shipping Information</h6>
								<?php if ($shipping_address != null) { ?>
									<span class="mb-2 text-xs">Name: <span class="text-dark font-weight-bold ms-2"><?= $shipping_address['first_name'] . ' ' . $shipping_address['last_name'] ?></span></span>
									<span class="mb-2 text-xs">Phone: <span class="text-dark ms-2 font-weight-bold"><?= $shipping_address['phone'] ?></span></span>
									<span class="mb-2 text-xs">Address: <span class="text-dark ms-2 font-weight-bold"><?= $shipping_address['street_1'] ?> - <?= $shipping_address['zip_code'] ?> / <?= $shipping_address['city'] ?></span></span>
									<span class="text-xs">Country: <span class="text-dark ms-2 font-weight-bold"><?= $shipping_address['country'] ?></span></span>
								<?php } else { ?>
									<span class="badge badge-sm bg-gradient-success state-negative"><?= $item->state ?></span>
								<?php } ?>
							</div>
						</li>
					</ul>
					<div class="form-group mt-3">
						<label class="form-label">Tracking info (if exists)</label>
						<p id="trackingInfo"></p>
					</div>
					<div class="form-group mt-3">
						<label for="packageWeightInfo" required class="form-label">Package Weight *</label>
						<input type="text" required class="form-control" id="packageWeightInfo" placeholder="Enter package weight info">
					</div>
					<!-- Select Combo -->
					<div class="form-group mt-3">
						<label for="originSelect" class="form-label">Select Origin *</label>
						<select class="form-select form-select-lg mb-3" id="originSelect">
							<option selected disabled>Select an origin</option>
							<!-- <option value="543415">Bighub DE</option>
							<option value="540854">Bighub PT</option>
							<option value="539514">Bighub ES</option> -->
						</select>
					</div>
					<div class="form-group mt-3">
						<label for="contractSelect" class="form-label">Select Contract</label>
						<select class="form-select form-select-lg mb-3" id="contractSelect">
							<option selected disabled>Select an contract</option>
							<option value=90832>Bighub MRW</option>
							<option value=90168>Bighub Correos Express</option>
							<option value=90164>Bighub UPS</option>
							<option value="sendcloud">SendCloud</option>
						</select>
					</div>
					<div class="form-group mt-3">
						<label for="carrierSelect" class="form-label">Select Carrier *</label>
						<select class="form-select form-select-lg mb-3" id="carrierSelect" disabled>
							<option selected disabled>Select a carrier</option>
						</select>
					</div>
					<button type="button" class="btn btn-primary mt-3" id="makeLabelButton">Make Label</button>
					<div id="feedback" class="mt-3"></div>
				</div>
				<div class="col-lg-2 col-12 ml-5"></div>
			</div>
		</div>
	</div>
</div>

<script>
	document.getElementById('makeLabelButton').addEventListener('click', function() {
		const username = 'bighub_logistic';
		const password = 'fWx%Gk@3rP!z';
		const headers = new Headers();
		headers.append('Authorization', 'Basic ' + btoa(username + ':' + password));
		headers.append('Content-Type', 'application/json');

		const originSelect = document.getElementById('originSelect').value;
		const contractSelect = document.getElementById('contractSelect').value;
		const carrierSelect = document.getElementById('carrierSelect');
		const selectedCarrier = JSON.parse(carrierSelect.value);
		const packageWeightInfo = document.getElementById('packageWeightInfo').value;
		console.log('aquuiu');
		console.log(selectedCarrier.carrier);
		const body = {
			"name": "<?= $shipping_address['first_name'] . ' ' . $shipping_address['last_name'] ?>",
			"address": "<?= $shipping_address['street_1'] ?>",
			"city": "<?= $shipping_address['city'] ?>",
			"postal_code": "<?= $shipping_address['zip_code'] ?>",
			"country": "<?= $shipping_address['country'] ?>",
			"order_number": "<?= $order_number_in_logistic ?>",
			"total_items": "<?= $total_items ?>",
			"weight": packageWeightInfo,
			"carrier": selectedCarrier,
			"contract": contractSelect,
			"telephone": "<?= $shipping_address['phone'] ?>",
			"sender_address": originSelect,
			"total_price": "<?= $total_price ?>"
		};

		getLabels(JSON.stringify(body));

		const bodyPostTracking = {
			"country": "<?= $country ?>",
			"channel": "<?= $channel ?>",
			"order_number": "<?= $order_number ?>",
			"order_number_in_logistic": "<?= $order_number_in_logistic ?>",
			"carrier": selectedCarrier.carrier,
		};

		postTrackingInMarketplaces(JSON.stringify(bodyPostTracking));
	});

	function populateSelect(shippingMethods) {
		const selectElement = document.getElementById('carrierSelect');
		selectElement.innerHTML = '<option selected disabled>Select a carrier</option>'; // Limpa o select
		shippingMethods.forEach(method => {
			const option = document.createElement('option');
			const carrierInfo = {
				carrier_code: method.id,
				carrier_name: method.name,
				carrier: method.carrier,
			};
			option.value = JSON.stringify(carrierInfo);
			option.textContent = `${method.name} ${method.min_weight}-${method.max_weight}kg (Price: ${method.countries[0].price})`;
			selectElement.appendChild(option);
		});
		selectElement.disabled = false; // Habilita o select após preencher
	}

	document.getElementById('originSelect').addEventListener('change', function() {
		const selectedOrigin = this.value;
		const country = "<?= $shipping_address['country'] ?>";
		const zipCode = "<?= $shipping_address['zip_code'] ?>";
		fetchShippingMethods(selectedOrigin, country, zipCode);
	});

	getTracking("<?= $order_number ?>").then(response => {
		const trackingField = $('#trackingInfo');
		trackingField.show();
		trackingField.html('<a href="' + response + '" target="_blank">' + response + '</a>');
	}).catch(error => {
		const trackingField = $('#trackingInfo');
		trackingField.hide();
		console.error('Error:', error);
	});

	function populateOriginSelect(origins) {
		const selectElement = document.getElementById('originSelect');
		selectElement.innerHTML = '<option selected disabled>Select an Origin</option>'; // Limpa o select
		origins.sender_addresses.forEach(origin => {
			const option = document.createElement('option');
			option.value = origin.id
			option.textContent = `${origin.company_name} (${origin.label})`;
			selectElement.appendChild(option);
		});
		selectElement.disabled = false;
	}

	fetchOrigins();
</script>