<div class="container-fluid">
	<div class="row">
		
		<div class="card-header p-3 pb-0">
			<div class="d-flex justify-content-between align-items-center">
				<div class="w-100">
					<h6>Order : <?= $order_data->reference ?> </h6>
					<h6>Seller : <?= $seller->name ?> -  <?= $seller->email ?>- <?= $seller->phone_number ?></h6>
					
					<p class="text-sm mb-0">
						From <b> <?= $order_data->created_at ?></b> - Channel <b> <?= $order_data->channel ?> - <?= $order_data->country ?></b>
					</p>
					<p class="text-sm">
						Order Items: <b><?= $order_items ?></b>
					</p>
				</div>
				<!-- <a href="javascript:;" class="btn bg-gradient-dark ms-auto mb-0">Invoice</a> -->
			</div>
		</div>
		<div class="card-body p-3 pt-0">
			<hr class="horizontal dark mt-0 mb-4">
			<?php foreach($items as $item){  #var_dump($item->taxes);?>
			<div class="row">
				<div class="col-lg-10 col-md-9 col-12">
					<div class="d-flex">
						<div>
							<img src="<?= $item->image?>" class="avatar avatar-xl me-3" alt="product image">
						</div>
						<div>
							<h6 class="text-lg font-16 mb-0 mt-2"><?= $item->title?> x <?= $item->quantity ?></h6>
							<p class="text-sm mb-1"><b>SKU Interno:</b> <?= $item->sku?></p>
							<p class="text-sm"><b>EAN:</b> <?= $item->ean?></p>
						</div>
					</div>
				</div>
				<div class="col-lg-2 col-md-3 col-12">
					<div class="d-flex">
						
						<div>
							<span class="badge badge-sm bg-gradient-success <?= $item->class_state ?>"><?= $item->state?></span>
							<p class="text-sm mt-2"><b>Price:</b> <?= $item->total_price_no_taxe ?></p>
							<p class="text-sm margin-negativa-15"><b>Taxes:</b> <?= $item->taxe_amount ?></p>
							<p class="text-sm margin-negativa-15"><b>Total:</b> <?= $item->total_price ?></p>
						</div>
					</div>
				</div>
				<hr class="horizontal dark mt-0 mb-3">
			</div>
			<?php } ?>
		
			
			<div class="row mt-2">
				<!-- <div class="col-lg-3 col-md-6 col-12">
					<h6 class="mb-3">Track order</h6>
					<div class="timeline timeline-one-side">
						<div class="timeline-block mb-3">
							<span class="timeline-step">
								<i class="material-icons text-secondary text-lg">notifications</i>
							</span>
							<div class="timeline-content">
								<h6 class="text-dark text-sm font-weight-bold mb-0">Order received</h6>
								<p class="text-secondary font-weight-normal text-xs mt-1 mb-0">22 DEC 7:20 AM</p>
							</div>
						</div>
						<div class="timeline-block mb-3">
							<span class="timeline-step">
								<i class="material-icons text-secondary text-lg">code</i>
							</span>
							<div class="timeline-content">
								<h6 class="text-dark text-sm font-weight-bold mb-0">Generate order id #1832412</h6>
								<p class="text-secondary font-weight-normal text-xs mt-1 mb-0">22 DEC 7:21 AM</p>
							</div>
						</div>
						<div class="timeline-block mb-3">
							<span class="timeline-step">
								<i class="material-icons text-secondary text-lg">shopping_cart</i>
							</span>
							<div class="timeline-content">
								<h6 class="text-dark text-sm font-weight-bold mb-0">Order transmited to courier</h6>
								<p class="text-secondary font-weight-normal text-xs mt-1 mb-0">22 DEC 8:10 AM</p>
							</div>
						</div>
						<div class="timeline-block mb-3">
							<span class="timeline-step">
								<i class="material-icons text-success text-gradient text-lg">done</i>
							</span>
							<div class="timeline-content">
								<h6 class="text-dark text-sm font-weight-bold mb-0">Order delivered</h6>
								<p class="text-secondary font-weight-normal text-xs mt-1 mb-0">22 DEC 4:54 PM</p>
							</div>
						</div>
					</div>
				</div> -->
				<div class="col-lg-5 col-md-6 col-12">
					<h6 class="mb-3">Invoice Information</h6>
					<ul class="list-group">
						<li class="list-group-item border-0 d-flex p-4 mb-2 bg-gray-100 border-radius-lg">
							<div class="d-flex flex-column">
								<h6 class="mb-3 text-sm">Billing Information</h6>
								<?php if($billing_address != null){ ?>
									<span class="mb-2 text-xs">Name: <span class="text-dark font-weight-bold ms-2"><?= $billing_address['first_name'].' '.$billing_address['last_name'] ?></span></span>
									<span class="mb-2 text-xs">Address: <span class="text-dark ms-2 font-weight-bold"><?= $billing_address['street_1'].' - '.$billing_address['street_2'].' - '.$billing_address['zip_code'].' - '.$billing_address['city']?></span></span>
									<span class="mb-2 text-xs">Country: <span class="text-dark ms-2 font-weight-bold"><?= $billing_address['country']?></span></span>
									<?php
										$billing_phone = '';
										if (!empty($billing_address['phone'])) {
											$billing_phone = $billing_address['phone'];
										} elseif (!empty($billing_address['phone_secondary'])) {
											$billing_phone = $billing_address['phone_secondary'];
										}
									?>
									<?php if (!empty($billing_phone)): ?>
										<span class="text-xs">Phone: <span class="text-dark ms-2 font-weight-bold"><?= $billing_phone ?></span></span>
									<?php endif; ?>
								<?php }else{ ?>	
									<span class="badge badge-sm bg-gradient-success state-negative"><?= $item->state?></span>
								<?php } ?>	
								</div>
						</li>
					</ul>
					<ul class="list-group">
						<li class="list-group-item border-0 d-flex p-4 mb-2 bg-gray-100 border-radius-lg">
							<div class="d-flex flex-column">
								<h6 class="mb-3 text-sm">Shipping Information</h6>
								<?php if($shipping_address != null){ ?>
									<span class="mb-2 text-xs">Name: <span class="text-dark font-weight-bold ms-2"><?= $shipping_address['first_name'].' '.$shipping_address['last_name'] ?></span></span>
									<span class="mb-2 text-xs">Address: <span class="text-dark ms-2 font-weight-bold"><?= $shipping_address['street_1']?> - <?= $shipping_address['street_2']?> - <?= $shipping_address['zip_code']?> / <?= $shipping_address['city']?></span></span>
									<span class="mb-2 text-xs">Country: <span class="text-dark ms-2 font-weight-bold"><?= $shipping_address['country']?></span></span>
									<?php
										$shipping_phone = '';
										if (!empty($shipping_address['phone'])) {
											$shipping_phone = $shipping_address['phone'];
										} elseif (!empty($shipping_address['phone_secondary'])) {
											$shipping_phone = $shipping_address['phone_secondary'];
										}
									?>
									<?php if (!empty($shipping_phone)): ?>
										<span class="text-xs">Phone: <span class="text-dark ms-2 font-weight-bold"><?= $shipping_phone ?></span></span>
									<?php endif; ?>
									<?php }else{ ?>	
									<span class="badge badge-sm bg-gradient-success state-negative"><?= $item->state?></span>
								<?php } ?>	
								</div>
						</li>
					</ul>
					
					
				</div>
				<div class="col-lg-2 col-12 ml-5">
					
				</div>
				<div class="col-lg-5 col-md-6 col-12">
					<h6 class="mb-3">Payment details</h6>
					<div class="card card-body border card-plain border-radius-lg d-flex align-items-center flex-row">
						<!-- <img class="w-10 me-3 mb-0" src="<?= base_url() ?>assets/v02/img/logos/mastercard.png" alt="logo"> -->
						<h6 class="mb-0"><?= $payment ?></h6> 
						<button type="button" class="btn btn-icon-only btn-rounded btn-outline-secondary mb-0 ms-2 btn-sm d-flex align-items-center justify-content-center ms-auto" data-bs-toggle="tooltip" data-bs-placement="bottom" title="" data-bs-original-title="We do not store card details">
							<i class="material-icons text-sm" aria-hidden="true">priority_high</i>
						</button>
					</div>
					<h6 class="mb-3 mt-3">Order Summary</h6>
					<div class="d-flex justify-content-between">
			
						<span class="mb-2 text-sm">
							Products Price:
						</span>
						<span class="text-dark font-weight-bold ms-2">€ <?= $order_data->total_price - $total_taxes?></span>
					</div>
					<div class="d-flex justify-content-between">
						<span class="text-sm">
							Taxes:
						</span>
						<span class="text-dark ms-2 font-weight-bold">€ <?= round($total_taxes, 2) ?></span>
					</div>
					
					<div class="d-flex justify-content-between mt-2">
						<span class="mb-2 text-lg">
							Total:
						</span>
						<span class="text-dark text-lg ms-2 font-weight-bold">€ <?= round($order_data->total_price, 2)?></span>
					</div>
					<hr class="color-hr">
					<!-- <h6 class="mb-3 mt-3">Marketplace Receipt</h6>
					<div class="d-flex justify-content-between">
						<span class="mb-2 text-sm">
							Total Price:
						</span>
						<span class="text-dark font-weight-bold ms-2">€ <?= round($order_data->total_price, 2)?></span>
					</div>
					
					<div class="d-flex justify-content-between">
						<span class="mb-2 text-sm">
							Comission:
						</span>
						<span class="text-dark ms-2 font-weight-bold">€ <?= $order_data->total_commission?></span>
					</div>
					
					<div class="d-flex justify-content-between mt-4">
						<span class="mb-2 text-lg">
							Total:
						</span>
						<span class="text-dark text-lg ms-2 font-weight-bold">€ <?= round(($order_data->total_price + ($order_data->total_price * 0.23)) - $order_data->total_commission, 2) ?></span>
					</div> -->
				</div>
				
			</div>
		</div>


	</div>

</div>