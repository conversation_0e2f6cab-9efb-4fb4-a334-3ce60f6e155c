<section>
	<div class="row">
		<div class="col-md-12">
			<label for="ibox-title">Marcar <b><?php echo $dados_atendimento['cliente']; ?></b> como atendido</label>
		</div>
	</div>
	<hr>
	<div class="row">
		<div class="col-md-6">
			<label for="exampleInputEmail1">Recebimento</label>
			<select class="form-control" name="tipo_pagamento">
				<option value="MW">MbWay</option>
				<option value="D">Dinheiro</option>
				<option value="MB">MultiBanco</option>
			</select>
		</div>
		<div class="col-md-6">
			<label for="exampleInputEmail1">Valor</label>
			<div class="input-group m-b">
				<div class="input-group-prepend">
					<span class="input-group-addon">€</span>
				</div>
				<input type="text" class="form-control" name="valor_pagamento" required>
				<div class="input-group-append">
					<span class="input-group-addon">.00</span>
				</div>
			</div>
		</div>

	</div>
	<hr>
	<div class="row">
		<div class="col-lg-12">
			<input type="file" id="img" name="files[]" multiple="multiple" />
			<div class="mt-3" style="text-align: center;" id="resultados"></div>
		</div>
	</div>

	<input type="hidden" name="cod_agendamento" value="<?php if (isset($dados_atendimento)) echo $dados_atendimento['cod_atendimento'] ?>" />
	<!-- /.row -->

</section>
<script>
	function readURL(evt) {
		var files = evt.target.files;

		for (var i = 0, f; f = files[i]; i++) {

			if (!f.type.match('image.*')) {
				continue;
			} //verifica se os arquivos são imagens

			var reader = new FileReader();

			reader.onload = (function(filei) {
				return function(e) {

					var tag = document.createElement('span');

					tag.innerHTML = ['<img width="210px"; style="padding: 15px;" src="', e.target.result,
						'" title="', escape(filei.name), '"/>'
					].join('');
					document.getElementById('resultados').insertBefore(tag, null);
				};
			})(f);
			reader.readAsDataURL(f);
		}
	}

	document.getElementById('img').addEventListener('change', readURL, false);
</script>