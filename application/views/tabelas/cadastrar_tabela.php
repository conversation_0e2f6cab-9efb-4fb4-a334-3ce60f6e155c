<?php
if (isset($testemunho) && !empty($testemunho)) {
	$chamada = 'editar-tabela';
	$titulo = 'Editar Tabela';
} else {
	$chamada = 'salvar-tabela';
	$titulo = 'Cadastrar Tabela';
}
?>
<section class="content-header">
	<?php if (isset($resp) && !empty($resp)) { ?>
		<div class="row">
			<div class="col-md-12">
				<div class="<?php echo $class ?>">
					<button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
					<h5><i class="icon fa fa-check"></i><?php echo $mensagem ?></h5>
				</div>
			</div>
			<br />
		</div>
	<?php } ?>
</section>

<section class="content">
    <div class="box box-info">
        <div class="box-header with-border">
			<h3 class="box-title"><?php echo $titulo ?></h3>
        </div>

		<form action="<?php echo base_url($chamada) ?>" method="POST">
			<input type="hidden" name="system" value="true" />
			<div class="box-body">
				<div class="row">
					<div class="col-md-6">
						<!-- Custom Tabs -->
						<div class="nav-tabs-custom">
							<div class="tab-content">

								<div class="row">
									<div class="col-md-8">
										<div class="form-group">
											<label for="cliente">Nome Tabela</label>
											<input type="text" class="form-control"  name="nome_tabela" value="<?php if (isset($tabela)) echo $tabela->nome_tabela ?>" placeholder="Nome tabela" required >
										</div>
									</div>
									<div class="col-md-3">
										<div class="form-group">
											<label>Status</label>
											<select class="form-control" style="width: 100%;" name="status">
												<option value="1" <?php if (isset($tabela) && $tabela->status == '1') echo 'selected'; ?> >Ativo</option>
												<option value="0" <?php if (isset($tabela) && $tabela->status == '0') echo 'selected'; ?>>Inativo</option>
											</select>
										</div>
									</div>
								</div>
							</div>
						</div>
						<button type="submit" class="btn btn-primary pull-right">Salvar Dados</button>
					</div>
				</form>
					<div class="col-md-6">
					
						<div class="box-header">
						<h3 class="box-title">Tabelas existentes</h3>
						</div>
						<!-- /.box-header -->
						<div class="box-body no-padding">
							<table class="table table-striped">

								<tr>
								<th style="width: 10px">ID</th>
								<th>Nome</th>
								<th>Status:</th>
								<th style="width: 40px">Label</th>
								</tr>
							
								<?php foreach ($tabelas as $t) { ?>
									<tr>
										<td><?php echo $t->id_tabela ?></td>
										<td><?php echo $t->nome_tabela ?></td>
										<td><?php echo $t->status  == 0 ? 'Inativo' : 'Ativo'; ?></td>
										<!-- <td>
											<div class="progress progress-xs">
												<div class="progress-bar progress-bar-yellow" style="width: 70%"></div>
											</div>
										</td> -->
										<td>
											<a href="<?php echo base_url('excluir-tabela/' . $t->id_tabela) ?>" class="btn btn-danger btn-xs"><b>X</b></a>
										</td>
									</tr>

								<?php } ?>
								
							</table>
						</div>
						<!-- /.box-body -->
					
					</div>
				</div>

			</div>
			<input type="hidden" name="id_tabela" value="<?php if (isset($tabela)) echo $tabela->id ?>">
			<div class="box-footer">
				Para efetuar o cadastro da tabela apenas preencha os dados e clique em salvar.
				
			</div>

	</div>


</section>