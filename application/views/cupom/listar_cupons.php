
<section class="content-header">
	<?php
	if (isset($resp) && !empty($resp)) {
		?>
		<div class="row">
			<div class="col-md-12">
				<div class="<?php echo $class ?>">
					<button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
					<h5><i class="icon fa fa-check"></i><?php echo $mensagem ?></h5>
				</div>
			</div>
			<br />
		</div>
	<?php } ?>
</section>

<section class="content">
	<div class="row">
        <div class="col-xs-12">
			<div class="box">

				<div class="box-header">
					</br>
					<div class="box-body table-responsive no-padding">
						<table id="tabela-datatable" class="table table-hover">
							<thead>
								<tr>
									<th>Criado</th>
									<th>Cupom</th>
									
									<th>Desconto</th>
									<th>Tipo</th>
									<th>Validade</th>
									<th>Status</th>
									<th>A<PERSON><PERSON>es</th>
								</tr>
							</thead>
							<tbody>
								<?php foreach ($cupons as $c) { ?>
									<tr>
										<td><?php echo date_mysql_to_human($c->data_criado) ?></td>
										<td><?php echo $c->nome_cupom ?></td>
										
										<td><?php echo $c->valor_desconto ?></td>
										<td>
											<?php if ($c->tipo_desconto == 'V') { ?>
													<a class="btn btn-success btn-xs">R$</a>
											<?php } elseif ($c->tipo_desconto == 'P') { ?>
													<a class="btn btn-success btn-xs">%</a>
											<?php }	?>	
										</td>
										<td><?php echo date_mysql_to_human($c->data_expira) ?></td>
										<td>
											<?php if ($c->status == '0') { ?>
													<a class="btn btn-success btn-xs">Ativo</a>
											<?php } elseif ($c->status == '1') { ?>
													<a class="btn btn-danger btn-xs">Inativo</a>
											<?php }	?>	
										</td>
									
										<td>
											<a href="<?php echo base_url('cadastrar-cupom/' . $c->id_cupom) ?>" class="btn btn-primary btn-xs">Editar</a>
											<a href="<?php echo base_url('excluir-cupom/' . $c->id_cupom) ?>" class="btn btn-danger btn-xs">Excluir</a>
										</td>
									</tr>
								<?php } ?>
							</tbody>
						</table>
					</div>
				</div>

			</div>
        </div>
	</div>
</section>

