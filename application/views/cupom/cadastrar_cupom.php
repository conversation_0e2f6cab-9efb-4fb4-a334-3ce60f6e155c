<?php
if (isset($cupom) && !empty($cupom)) {
	$chamada = 'editar-cupom';
	$titulo = 'Editar Cupom - ' . $cupom->nome_cupom;
} else {
	$chamada = 'salvar-cupom';
	$titulo = 'Cadastrar Cupom';
}
?>

<section class="content">
	<?php if (isset($resp) && !empty($resp)) { ?>
		<div class="<?php echo $class ?>">
			<button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
			<h5><i class="icon fa fa-check"></i><?php echo $mensagem ?></h5>
		</div>
	<?php } ?>
    <div class="box box-info">
        <div class="box-header with-border">
			<h3 class="box-title"><?php echo $titulo ?></h3>

			<div class="box-tools pull-right">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
				<button type="button" class="btn btn-box-tool" data-widget="remove"><i class="fa fa-remove"></i></button>
			</div>
        </div>
        <!-- /.box-header -->
        <form id="formCadastroPlano" name="formulario" action="<?php echo base_url($chamada) ?>" method="POST">
			<div class="box-body">
				<div class="row">
					<div class="col-md-12">
						<!-- Custom Tabs -->
						<div class="nav-tabs-custom">
							<div class="tab-content">
								<div class="tab-pane active">
									<div class="row">
										<div class="col-md-2">
											<div class="form-group">
												<label for="exampleInputEmail1">Data Cadastro</label>
												<input type="text" class="form-control" id="data_criado" name="data_criado" value="<?php if (isset($cupom)) echo date_mysql_to_human($cupom->data_criado) ?>" placeholder="<?php echo date_mysql_to_human(date('Y-m-d')) ?>" disabled>
											</div>
										</div>
									</div>

									<div class="row">
										<div class="col-md-3">
											<div class="form-group">
												<label for="exampleInputEmail1">Cupom</label>
												<input type="text" class="form-control" id="nome_cupom" name="nome_cupom" value="<?php if (isset($cupom)) echo $cupom->nome_cupom ?>" >
											</div>
										</div>

										<div class="col-md-3">
											<div class="form-group">
												<label>Tipo de Desconto</label>
												<select class="form-control" style="width: 100%;" name="tipo_desconto">
													<option value="V"   <?php if (isset($cupom) && $cupom->tipo_desconto == 'V') echo 'selected'; ?>>Valor</option>
													<option value="P"   <?php if (isset($cupom) && $cupom->tipo_desconto == 'P') echo 'selected'; ?>>Porcentagem</option>
												</select>
											</div>
										</div>
										<div class="col-md-2">
											<div class="form-group">
												<label>Status</label>
												<select class="form-control" style="width: 100%;" name="status">
													<option value="0" <?php if (isset($cupom) && $cupom->status == '0') echo 'selected'; ?> >Ativo</option>
													<option value="1" <?php if (isset($cupom) && $cupom->status == '1') echo 'selected'; ?>>Inativo</option>
												</select>
											</div>
										</div>
									</div>

									<div class="row">
										<div class="col-md-3">
											<div class="form-group">
												<label for="exampleInputEmail1">Valor </label>
												<input type="text" class="form-control" id="valor" name="valor" value="<?php if (isset($cupom)) echo $cupom->valor_desconto ?>" placeholder="Valor Cupom">
											</div>
										</div>
										<div class="col-md-3">
											<div class="form-group">
											<label for="exampleInputEmail1">Data Expira </label>	
											<div class="input-group">
												<div class="input-group-addon">
													<i class="fa fa-calendar"></i>
												</div>
												<input type="text" class="form-control" data-inputmask="'alias': 'dd/mm/yyyy'" data-mask="" name="data_expira" value="<?php if (isset($cupom)) echo date_mysql_to_human($cupom->data_expira) ?>" placeholder="Data Expira" >
											</div>
											</div>
										</div>

										
									</div>
									

									<div class="row">


										<div class="col-md-8">
											<div class="form-group">
												<label for="exampleInputEmail1">Observação</label>
												<input type="text" class="form-control" id="obs"  name="obs" value="<?php if (isset($cupom)) echo $cupom->obs ?>" placeholder="Observações">
											</div>
										</div>

									</div>


								</div>
							</div>
						</div>
					</div>
				</div>

			</div>

			<input type="hidden" name="id_cupom" value="<?php if (isset($cupom)) echo $cupom->id_cupom ?>">
			<div class="box-footer">
				Para efetuar o cadastro do Cupom apenas preencha os dados e clique em salvar.
				<button type="submit" class="btn btn-primary pull-right">Salvar Dados</button>
			</div>
        </form>

	</div>

</section>