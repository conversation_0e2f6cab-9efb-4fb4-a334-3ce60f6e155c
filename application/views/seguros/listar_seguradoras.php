

<section class="content-header">
	<?php
	if (isset($resp) && !empty($resp)) {
		?>
		<div class="row">
			<div class="col-md-12">
				<div class="<?php echo $class ?>">
					<button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
					<h5><i class="icon fa fa-check"></i><?php echo $mensagem ?></h5>
				</div>
			</div>
			<br />
		</div>
	<?php } ?>
</section>

<section class="content">
	<div class="row">
        <div class="col-xs-12">
			<div class="box">

				<div class="box-header">
					</br>
					<div class="box-body table-responsive no-padding">
						<table id="tabela-datatable" class="table table-hover">
							<thead>
								<tr>
									<th>Data Cadastro</th>
									<th>Nome da Seguradora</th>
									<th>Parcelamento</th>
									<th>Co<PERSON>ção Dolar</th>
									<th>Ações</th>
								</tr>
							</thead>
							<tbody>
								<?php foreach ($seguradoras as $s) { ?>
									<tr>
										<td><?php echo date_mysql_to_human($s->data_cadastro) ?></td>
										<td><?php echo $s->nome_seguradora ?></td>
										<td><?php echo $s->parcelamento ?></td>
										<td>
										<?php
											if (isset($s->cotacao_dolar) && !empty($s->cotacao_dolar)) {
												echo '$ ' . FormatarValor($s->cotacao_dolar);
											} else {
												echo 'Sem valor';
											}
										?>
										</td>
										<td>
											<a href="<?php echo base_url('cadastrar-seguradora/' . $s->id) ?>" class="btn btn-primary btn-xs">Editar</a>
											<a href="<?php echo base_url('excluir-seguradora/' . $s->id) ?>" class="btn btn-danger btn-xs">Excluir</a>
										</td>
									</tr>
								<?php } ?>
							</tbody>
						</table>
					</div>
				</div>

			</div>
        </div>
	</div>
</section>

