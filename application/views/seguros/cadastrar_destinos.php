<section class="content-header">
	<?php if (isset($resp) && !empty($resp)) { ?>
		<div class="row">
			<div class="col-md-12">
				<div class="<?php echo $class ?>">
					<button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
					<h5><i class="icon fa fa-check"></i><?php echo $mensagem ?></h5>
				</div>
			</div>
			<br />
		</div>
	<?php } ?>
</section>

<section class="content">
    <div class="box box-info">
        <div class="box-header with-border">
			<h3 class="box-title">Cadastro de Destino</h3>
        </div>

        <form id="formCadastroPlano" name="formulario" action="<?= base_url() ?>cadastrar-destinos" method="POST">
			<input type="hidden" name="system" value="true" />
			<input type="hidden" name="form[id]" value="<?php echo isset($destino->id) ? $destino->id : NULL; ?>" />
			<div class="box-body">
				<div class="row">
					<div class="col-md-12">
						<!-- Custom Tabs -->
						<div class="nav-tabs-custom">
							<div class="tab-content">

								<div class="row">
									<div class="col-md-4">
										<div class="form-group">
											<label for="destino">Destino</label>
											<input type="text" class="form-control" id="destino" name="form[destino]" value="<?php if (isset($destino)) echo $destino->destino ?>" placeholder="Destino">
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-md-3">
										<div class="form-group">
											<label>Status</label>
											<select class="form-control" style="width: 100%;" name="form[status]">
												<option value="1" <?php if (isset($destino) && $destino->status == '1') echo 'selected'; ?> >Ativo</option>
												<option value="0" <?php if (isset($destino) && $destino->status == '0') echo 'selected'; ?>>Inativo</option>
											</select>
										</div>
									</div>
								</div>
							</div>

						</div>
					</div>
				</div>

			</div>

			<input type="hidden" name="id_plano" value="<?php if (isset($destino)) echo $destino->id ?>">
			<div class="box-footer">
				Para efetuar o cadastro do Item apenas preencha os dados e clique em salvar.
				<button type="submit" class="btn btn-primary pull-right">Salvar Dados</button>
			</div>
		</form>

	</div>


</section>