
<section class="content-header">
	<?php if (isset($resp) && !empty($resp)) { ?>
		<div class="row">
			<div class="col-md-12">
				<div class="<?php echo $class ?>">
					<button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
					<h5><i class="icon fa fa-check"></i><?php echo $mensagem ?></h5>
				</div>
			</div>
			<br />
		</div>
	<?php } ?>
</section>

<section class="content">
    <div class="box box-info">
        <div class="box-header with-border">
			<h3 class="box-title">Cadastro de Seguradora</h3>
        </div>

        <form id="formCadastroPlano" name="formulario" action="<?= base_url() ?>cadastrar-seguradora" method="POST" enctype="multipart/form-data">
			<input type="hidden" name="system" value="true" />
			<input type="hidden" name="form[id]" value="<?php echo isset($seguradora->id) ? $seguradora->id : NULL; ?>" />
			<div class="box-body">
				<div class="row">
					<div class="col-md-12">
						<!-- Custom Tabs -->
						<div class="nav-tabs-custom">
							<div class="tab-content">

								<div class="row">
									<div class="col-md-4">
										<div class="form-group">
											<label for="nome_seguradora">Nome da seguradora</label>
											<input type="text" class="form-control" id="nome_seguradora" name="form[nome_seguradora]" value="<?php if (isset($seguradora)) echo $seguradora->nome_seguradora ?>" placeholder="Nome da seguradora">
										</div>
									</div>
									<div class="col-md-2">
									</div>
									<div class="col-md-2">
										<div class="form-group">
											<label for="nome_seguradora">Cotação Dolar</label>
											<input type="text" class="form-control" id="cotacao_dolar" name="form[cotacao_dolar]" value="<?php if (isset($seguradora)) echo $seguradora->cotacao_dolar ?>" placeholder="Cotação Dolar">
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-md-8">
										<textarea id='editor1' name='form[texto_seguradora]' class="textarea" placeholder="Digite o conteúdo aqui"
												  style="width: 100%; height: 200px; font-size: 14px; line-height: 18px; border: 1px solid #dddddd; padding: 10px;">
													  <?= isset($seguradora->texto_seguradora) ? $seguradora->texto_seguradora : NULL; ?>
										</textarea>
									</div>
								</div>
								<div class="row">
									<div class="col-md-4">
										<div class="form-group">
											<label for="link">Link do website</label>
											<input type="text" class="form-control mask-link" id="link" name="form[link]" value="<?php if (isset($seguradora)) echo $seguradora->link; ?>" placeholder="http://www....">
										</div>
									</div>
								</div>

								<div class="row">
									<div class="col-md-12">
										<div class="form-group">
											<div class="btn btn-default btn-file alter">
												<i class="fa fa-paperclip"></i> Logotipo da Seguradora
												<input type="file" name="img_seguradora">
											</div>
										</div>
									</div>
									<?php
									if (!empty($seguradora->img_seguradora)) {
										?>
										<div class="col-sm-3 col-md-3">
											<div class="row">
												<div class="col-md-12">
													<img class="img-responsive" src="<?php echo base_url('..' . DIRECTORY_SEPARATOR . 'assets' . DIRECTORY_SEPARATOR . 'images' . DIRECTORY_SEPARATOR . 'seguradoras' . DIRECTORY_SEPARATOR . $seguradora->img_seguradora); ?>">
												</div>
											</div>
										</div>
										<?php
									}
									?>
								</div>

								<div class="row">
									<div class="col-md-4">
										<div class="form-group">
											<label for="parcelamento">Parcelamento</label>
											<input type="text" class="form-control" id="parcelamento" name="form[parcelamento]" value="<?php if (isset($seguradora)) echo $seguradora->parcelamento ?>" placeholder="Parcelamento disponível">
										</div>
									</div>
								</div>

								<div class="row">
									<div class="col-md-12">
										<div class="form-group">
											<div class="btn btn-default btn-file alter">
												<i class="fa fa-paperclip"></i> Termos & Condições gerais
												<input type="file" name="termos">
											</div>
											<?php
											if (isset($seguradora) && !empty($seguradora->termos)) {
												?>
												<p class="help-block">Termo cadastrado: <?php echo $seguradora->termos; ?></p>
												<?php
											}
											?>
										</div>
									</div>
								</div>

								<div class="row">
									<div class="col-md-3">
										<div class="form-group">
											<label>Status</label>
											<select class="form-control" style="width: 100%;" name="form[status]">
												<option value="1" <?php if (isset($seguradora) && $seguradora->status == '1') echo 'selected'; ?> >Ativo</option>
												<option value="0" <?php if (isset($seguradora) && $seguradora->status == '0') echo 'selected'; ?>>Inativo</option>
												<option value="2" <?php if (isset($seguradora) && $seguradora->status == '2') echo 'selected'; ?>>Não alterar</option>
											</select>
										</div>
									</div>
								</div>
							</div>

						</div>
					</div>
				</div>

			</div>

			<div class="box-footer">
				Para efetuar o cadastro do Item apenas preencha os dados e clique em salvar.
				<button type="submit" class="btn btn-primary pull-right">Salvar Dados</button>
			</div>
		</form>

	</div>


</section>