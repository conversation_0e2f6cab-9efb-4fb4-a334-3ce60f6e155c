<section class="content-header">
	<?php if (isset($resp) && !empty($resp)) { ?>
		<div class="row">
			<div class="col-md-1">
				<?php if (isset($_SERVER['HTTP_REFERER'])): ?>
					<a  class="btn btn-primary" href="<?php echo $_SERVER['HTTP_REFERER']; ?>"><i class="fa fa-fw fa-arrow-left"></i> </a>
				<?php endif ?>
				<small></small>
				<br />
			</div>

			<div class="col-md-11">
				<div class="<?php echo $class ?>">
					<button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
					<h5><i class="icon fa fa-check"></i><?php echo $mensagem ?></h5>
				</div>
			</div>
			<br />
		</div>
	<?php }else { ?>
		<?php if (isset($_SERVER['HTTP_REFERER'])): ?>
			<a  class="btn btn-primary" href="<?php echo $_SERVER['HTTP_REFERER']; ?>"><i class="fa fa-fw fa-arrow-left"></i> </a>
		<?php endif ?>
		<small></small>
		<br />
	<?php } ?>
</section>

<section class="content">
	<div class="row">
        <div class="col-xs-12">
			<div class="box">

				<div class="box-header">
					</br>
					<div class="box-body table-responsive no-padding">
						<table id="tabela-datatable" class="table table-hover">
							<thead>
								<tr>
									<th>Data Cadastro</th>
									<th>Nome Plano</th>
									<th>Seguradora</th>
									<th>Relevância</th>
									<th>Valor Diária</th>
									<th>Ações</th>
								</tr>
							</thead>
							<tbody>
								<?php foreach ($seguros as $s) { ?>
									<tr>
										<td>
											<?php echo date_mysql_to_human($s->data_cadastro) ?>
										</td>
										<td>
											<?php echo $s->nome_plano ?>
										</td>
										<td>
											<?php echo nome_seguradora($s->seguradora) ?>
										</td>
									
										<td>
											<?php
											if (isset($s->relevancia) && !empty($s->relevancia)) {
												echo $s->relevancia;
											} else {
												echo '-';
											}
											?>
										</td>
										<td>
											<?php
											if (isset($s->valor_dia) && !empty($s->valor_dia)) {
												echo 'R$ ' . FormatarValor($s->valor_dia);
											} else {
												echo 'Sem valor';
											}
											?>
										</td>
								
										<td>
											<a class=<?php echo $s->status_plano  == 0 ? '"btn btn-warning btn-xs"' : '"btn btn-success btn-xs"'; ?> ><?php echo $s->status_plano  == 0 ? 'Inativo' : 'Ativo'; ?></a></h5>
											<a href="<?php echo base_url('seguros/editar/' . $s->id) ?>" class="btn btn-primary btn-xs">Editar</a>
											<a  class="link-modal-dados btn btn-info btn-xs" href="<?php echo base_url('seguros/dados/' . $s->id); ?>"><small><i class="fa fa-search"></i></small>  Detalhes</a>
											<a href="<?php echo base_url('seguros/excluir/' . $s->id) ?>" class="btn btn-danger btn-xs">Excluir</a>
										</td>
									</tr>
<?php } ?>

							</tbody>

						</table>

					</div>
				</div>
				<!-- <div class="box">
					<div class="box-header">
					<h4><b>VALOR TOTAL DAS VENDAS:</b>&nbsp; R$ <?php echo FormatarValor($total->TOTAL_VENDAS) ?></h4>
					</div>
				</div> -->

			</div>
        </div>
	</div>

	<div class="modal fade" id="modal-dados" tabindex="-1">
		<div class="modal-dialog  modal-lg">
			<div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">
						<span aria-hidden="true">&times;</span>
					</button>
					<p class="modal-title" id="myModalLabel"><b>Detalhes do Plano</b></p>
				</div>
				<div class="col-md-12">
					<div class="modal-body"></div>
				</div>
				<div class="modal-footer">
					<!--  <button type="submit" class="btn btn-info" onclick="enviar_agendamento(event);" > Salvar Agendamento</button> -->
					<button type="button" class="btn btn-gray" data-dismiss="modal">Sair</button>
				</div>
            </div>
		</div>
	</div>


</section>

