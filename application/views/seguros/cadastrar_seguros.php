<?php
if (isset($plano) && !empty($plano)) {
	$chamada = 'editar-plano';
	$titulo = 'Editar Plano - ' . $plano->nome_plano;
} else {
	$chamada = 'salvar-plano';
	$titulo = 'Cadastrar Plano';
}
?>

<section class="content">
	<?php if (isset($resp) && !empty($resp)) { ?>
		<div class="<?php echo $class ?>">
			<button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
			<h5><i class="icon fa fa-check"></i><?php echo $mensagem ?></h5>
		</div>
	<?php } ?>
    <div class="box box-info">
        <div class="box-header with-border">
			<h3 class="box-title"><?php echo $titulo ?></h3>

			<div class="box-tools pull-right">
				<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
				<button type="button" class="btn btn-box-tool" data-widget="remove"><i class="fa fa-remove"></i></button>
			</div>
        </div>
        <!-- /.box-header -->
        <form id="formCadastroPlano" name="formulario" action="<?php echo base_url($chamada) ?>" method="POST">
			<div class="box-body">
				<div class="row">
					<div class="col-md-12">
						<!-- Custom Tabs -->
						<div class="nav-tabs-custom">
							<ul class="nav nav-tabs">
								<li class="active"><a href="#tab_1" data-toggle="tab">Plano</a></li>
								<li><a href="#tab_2" data-toggle="tab">Coberturas</a></li>
								<li><a href="#tab_3" data-toggle="tab">Observações</a></li>
							</ul>
							<div class="tab-content">
								<div class="tab-pane active" id="tab_1">
									<div class="row">
										<div class="col-md-2">
											<div class="form-group">
												<label for="exampleInputEmail1">Data Cadastro</label>
												<input type="text" class="form-control" id="data_cadastro" name="data_cadastro" value="<?php if (isset($plano)) echo date_mysql_to_human($plano->data_cadastro) ?>" placeholder="<?php echo date_mysql_to_human(date('Y-m-d')) ?>" disabled>
											</div>
										</div>
										<div class="col-md-2">
											<div class="form-group">
												<label for="exampleInputEmail1">Cadastrado por</label>
												<input type="text" class="form-control" id="id_cadastrante" name="id_cadastrante" value="<?php if (isset($plano)) echo $plano->cadastrado_por ?>" placeholder="<?php echo $nome_usuario ?>" disabled>
											</div>
										</div>
										<div class="col-md-1">
											<div class="form-group">
												<label for="exampleInputEmail1">Relevância</label>
												<input type="text" class="form-control" id="relevancia" name="relevancia" value="<?php if (isset($plano)) echo $plano->relevancia ?>">
											</div>
										</div>


										<div class="col-md-2">
											<div class="form-group">
												<label>Seguradora</label>
												<select class="form-control" style="width: 100%;" name="seguradora">
													<option >Escolha uma seguradora</option>
													<?php foreach ($seguradoras as $seg): ?>
														<option
														<?php if (isset($plano) && $seg->id == $plano->seguradora) echo 'selected'; ?>
															value="<?php echo $seg->id ?>"> <?php echo $seg->nome_seguradora ?></option>
														<?php endforeach; ?>
												</select>
											</div>
										</div>

										<div class="col-md-2">
											<div class="form-group">
												<label>Tabela Seguradora</label>
												<select class="form-control" style="width: 100%;" name="tabela_seguradora">
													<option >Escolha uma tabela</option>
													<?php foreach ($tabela_seguradora as $ts):?>
														<option
														<?php if (isset($plano) && $ts->id_tabela == $plano->id_tabela) echo 'selected'; ?>
															value="<?php echo $ts->id_tabela ?>"> <?php echo $ts->nome_tabela ?></option>
														<?php endforeach; ?>
												</select>
											</div>
										</div>
<!-- 
										<div class="col-md-3">
										<div class="box box-info">
											
												<div class="box-body">
													<div class="table-responsive">
														<table class="table no-margin">
															<thead>
																<tr>
																	<th>Dias</th>
																	<th>Valor</th>
																</tr>
															</thead>
															<tbody>
																<tr>
																	<td><a href="pages/examples/invoice.html">0 a 6</a></td>
																	<td>
																	<div class="sparkbar" data-color="#00a65a" data-height="20">$ 90.00</div>
																	</td>
																</tr>
															</tbody>
														</table>
													</div>

												</div>
											</div>
										</div> -->

									</div>

									<div class="row">
										<div class="col-md-12">
											<div class="form-group">
												<?php
												$cd = count($destinos);
												$ca = 0;
												foreach ($destinos as $destino) {
													$selected = '';
													if (!empty($aux)) {
														foreach ($aux as $it) {
															if ($it == $destino->destino) {
																$ca++;
																$selected = 'checked="checked"';
															}
														}
													}
													?>
													<div class="col-md-4">
														<label for="disponibilidade_<?= $destino->id; ?>">
															<input id="disponibilidade_<?= $destino->id; ?>" type="checkbox" class="minimal-red js-checkbox" value="<?= $destino->destino; ?>" name="disponibilidade[<?= $destino->id; ?>]" <?= $selected; ?> >&nbsp;&nbsp;<?= $destino->destino; ?>
														</label>
													</div>
												<?php } ?>
											</div>
										</div>
									</div>
									<div class="row">
										<div class="col-md-12">
											<div class="form-group">

												<div class="col-md-4">
													<label id="select_all_detinos" for="all_detinos">
														<input id="all_detinos" type="checkbox" class="minimal-red" <?= $cd == $ca ? 'checked' : ''; ?> >&nbsp;&nbsp;Selecionar todos
													</label>
												</div>
											</div>
										</div>
									</div>

									<div class="row">
										<div class="col-md-4">
											<div class="form-group">
												<label for="exampleInputEmail1">Plano</label>
												<input type="text" class="form-control" id="nome_plano" name="nome_plano" value="<?php if (isset($plano)) echo $plano->nome_plano ?>" >
											</div>
										</div>
										<div class="col-md-2">
											<div class="form-group">
												<label for="exampleInputEmail1">Idade Máxima</label>
												<input type="text" class="form-control" id="id_maxima" name="id_maxima" value="<?php if (isset($plano)) echo $plano->id_maxima ?>" placeholder="Idade Máxima">
											</div>
										</div>
										<div class="col-md-3">
											<div class="form-group">
												<label>Tipo de Plano</label>
												<select class="form-control" style="width: 100%;" name="tipo_plano">
													<option value="Lazer"        <?php if (isset($plano) && $plano->tipo_plano == 'Lazer') echo 'selected'; ?>>Lazer</option>
													<option value="Estudante"    <?php if (isset($plano) && $plano->tipo_plano == 'Estudante') echo 'selected'; ?>>Estudante</option>
													<option value="Estudante J1" <?php if (isset($plano) && $plano->tipo_plano == 'Estudante J1') echo 'selected'; ?>>Estudante J1</option>
												</select>
											</div>
										</div>
										<div class="col-md-3">
											<div class="form-group">
												<label>Muti Viagem</label>
												<select class="form-control" style="width: 100%;" name="multi_viagem">
													<option value="Sim"    <?php if (isset($plano) && $plano->multi_viagem == 'Sim') echo 'selected'; ?>>Sim</option>
													<option value="Não"    <?php if (isset($plano) && $plano->multi_viagem == 'Não') echo 'selected'; ?>>Não</option>
												</select>
											</div>
										</div>
									</div>

									<div class="row">
										<div class="col-md-2">
											<div class="form-group">
												<label for="exampleInputEmail1">Valor Dia</label>
												<input type="text" class="form-control" id="valor_dia" name="valor_dia" value="<?php if (isset($plano)) echo $plano->valor_dia ?>" placeholder="Valor Dia">
											</div>
										</div>

										<div class="col-md-2">
											<div class="form-group">
												<label for="exampleInputEmail1">Máxima de Dias - Plano</label>
												<input type="text" class="form-control" id="maximo_dias" name="maximo_dias" value="<?php if (isset($plano)) echo $plano->maximo_dias ?>" placeholder="Maximo Dias">
											</div>
										</div>

										<div class="col-md-2">
											<div class="form-group">
												<label for="exampleInputEmail1">Minimo de Dias - Plano</label>
												<input type="text" class="form-control" id="minimo_dias" name="minimo_dias" value="<?php if (isset($plano)) echo $plano->minimo_dias ?>" placeholder="Minimo Dias">
											</div>
										</div>

										<div class="col-md-2">
											<div class="form-group">
												<label for="exampleInputEmail1">Idade Adcional Tarifa</label>
												<input type="text" class="form-control" id="idade_adicional_tarifa" name="idade_adicional_tarifa" value="<?php if (isset($plano)) echo $plano->idade_adicional_tarifa ?>" placeholder="Idade Adcional Tarifa">
											</div>
										</div>

										<div class="col-md-4">
											<div class="form-group">
												<label for="exampleInputEmail1">Porcentagem Adicional</label>
												<input type="text" class="form-control" id="porcentagem_adicional" name="porcentagem_adicional" value="<?php if (isset($plano)) echo $plano->porcentagem_adicional ?>" placeholder="Porcentagem Adicional">
											</div>
										</div>
									</div>
									<div class="row">


										<div class="col-md-8">

										</div>
									</div>

									<div class="row">


										<div class="col-md-8">
											<div class="form-group">
												<label for="exampleInputEmail1">Observações para cliente</label>
												<input type="text" class="form-control" id="obs_cliente"  name="obs_cliente" value="<?php if (isset($plano)) echo $plano->obs_cliente ?>" placeholder="Observações">
											</div>
										</div>


										<div class="col-md-2">
											<div class="form-group">
												<label>Status</label>
												<select class="form-control" style="width: 100%;" name="status_plano">
													<option value="1" <?php if (isset($plano) && $plano->status_plano == '1') echo 'selected'; ?> >Ativo</option>
													<option value="0" <?php if (isset($plano) && $plano->status_plano == '0') echo 'selected'; ?>>Inativo</option>
												</select>
											</div>
										</div>

										<div class="col-md-2">
											<div class="form-group">
												<label>Selo</label>
												<select class="form-control" style="width: 100%;" name="selo">
													<option value="1" <?php if (isset($plano) && $plano->selo_seguro == '1') echo 'selected'; ?> >Sim</option>
													<option value="0" <?php if (isset($plano) && $plano->selo_seguro == '0' || $plano->selo_seguro == '') echo 'selected'; ?>>Não</option>
												</select>
											</div>
										</div>

									</div>


								</div>
								<!-- /.tab-pane -->
								<div class="tab-pane" id="tab_2">
									<div class="row">
										<div class="col-md-4">
											<div class="form-group">
												<label for="exampleInputEmail1">DESPESAS MÉDICAS E HOSPITALARES</label>
												<input type="text" class="form-control" id="despesas_medicas_e_hospitalares" name="despesas_medicas_e_hospitalares" value="<?php if (isset($plano)) echo $plano->despesas_medicas_e_hospitalares ?>">
											</div>
										</div>
										<div class="col-md-4">
											<div class="form-group">
												<label for="exampleInputEmail1">DESPESAS MÉDICAS PARA DOENÇA PRÉ-EXISTENTE</label>
												<input type="text" class="form-control" id="despesas_medicas_para_doenca_pre_existente" name="despesas_medicas_para_doenca_pre_existente" value="<?php if (isset($plano)) echo $plano->despesas_medicas_para_doenca_pre_existente ?>" >
											</div>
										</div>
										<div class="col-md-4">
											<div class="form-group">
												<label for="exampleInputEmail1">DESPESAS MÉDICAS PARA ESPORTES (LAZER)</label>
												<input type="text" class="form-control" id="despesas_medicas_para_esportes_lazer" name="despesas_medicas_para_esportes_lazer" value="<?php if (isset($plano)) echo $plano->despesas_medicas_para_esportes_lazer ?>" >
											</div>
										</div>
									</div>

									<div class="row">
										<div class="col-md-4">
											<div class="form-group">
												<label for="exampleInputEmail1">DESPESAS ODONTOLÓGICAS EMERGENCIAIS</label>
												<input type="text" class="form-control" id="despesas_odontologicas" name="despesas_odontologicas" value="<?php if (isset($plano)) echo $plano->despesas_odontologicas ?>" >
											</div>
										</div>
										<div class="col-md-4">
											<div class="form-group">
												<label for="exampleInputEmail1">DESPESAS FARMACEUTICAS</label>
												<input type="text" class="form-control" id="despesas_farmaceuticas" name="despesas_farmaceuticas" value="<?php if (isset($plano)) echo $plano->despesas_farmaceuticas ?>" >
											</div>
										</div>
										<div class="col-md-4">
											<div class="form-group">
												<label for="exampleInputEmail1">REPATRIAÇÃO FUNERÁRIA</label>
												<input type="text" class="form-control" id="reparticao_sanitaria" name="reparticao_sanitaria"value="<?php if (isset($plano)) echo $plano->reparticao_sanitaria ?>" >
											</div>
										</div>
									</div>

									<div class="row">
										<div class="col-md-4">
											<div class="form-group">
												<label for="exampleInputEmail1">REPATRIAÇÃO SANITÁRIA</label>
												<input type="text" class="form-control" id="reparticao_de_morte" name="reparticao_de_morte" value="<?php if (isset($plano)) echo $plano->reparticao_de_morte ?>" >
											</div>
										</div>
										<div class="col-md-4">
											<div class="form-group">
												<label for="exampleInputEmail1">TRANSLADO MÉDICO </label>
												<input type="text" class="form-control" id="translado_medico" name="translado_medico" value="<?php if (isset($plano)) echo $plano->translado_medico ?>" >
											</div>
										</div>
										<div class="col-md-4">
											<div class="form-group">
												<label for="exampleInputEmail1">ATRASO ENTREGA DA BAGAGEM</label>
												<input type="text" class="form-control" id="reembolso_de_gastos_por_demora" name="reembolso_de_gastos_por_demora" value="<?php if (isset($plano)) echo $plano->reembolso_de_gastos_por_demora ?>" >
											</div>
										</div>
									</div>

									<div class="row">
										<div class="col-md-4">
											<div class="form-group">
												<label for="exampleInputEmail1">SEGURO POR INVALIDEZ PERMANENTE POR ACIDENTE</label>
												<input type="text" class="form-control" id="seguro_de_invalidez_permanente_acidente" name="seguro_de_invalidez_permanente_acidente" value="<?php if (isset($plano)) echo $plano->seguro_de_invalidez_permanente_acidente ?>" >
											</div>
										</div>
										<div class="col-md-4">
											<div class="form-group">
												<label for="exampleInputEmail1">SEGURO POR MORTE ACIDENTAL</label>
												<input type="text" class="form-control" id="seguro_de_morte_acidental" name="seguro_de_morte_acidental"value="<?php if (isset($plano)) echo $plano->seguro_de_morte_acidental ?>" >
											</div>
										</div>
										<!-- <div class="col-md-4">
											  <div class="form-group">
												  <label for="exampleInputEmail1">REGRESSO DE MENOR OU IDOSO DESACOMPANHADO</label>
												  <input type="text" class="form-control" id="regresso_de_menor_ou_idoso" name="regresso_de_menor_ou_idoso" value="<?php if (isset($plano)) echo $plano->regresso_de_menor_ou_idoso ?>" >
											  </div>
										</div> -->
										<div class="col-md-4">
											<div class="form-group">
												<label for="exampleInputEmail1">ATRASO DE VÔO</label>
												<input type="text" class="form-control" id="recuperacao_de_enfermidade" name="recuperacao_de_enfermidade" value="<?php if (isset($plano)) echo $plano->recuperacao_de_enfermidade ?>" >
											</div>
										</div>
									</div>

									<div class="row">
										<!-- <div class="col-md-4">
												<div class="form-group">
													<label for="exampleInputEmail1">EXTENSÃO DE INTERNAÇÃO HOSPITALAR</label>
													<input type="text" class="form-control" id="extensao_de_internacao_hospitalar" name="extensao_de_internacao_hospitalar" value="<?php if (isset($plano)) echo $plano->extensao_de_internacao_hospitalar ?>" >
												</div>
										  </div> -->

										<!-- <div class="col-md-4">
											  <div class="form-group">
												  <label for="exampleInputEmail1">DESPESAS EXTRAORDINÁRIAS POR PERMANÊNCIA FORÇADA</label>
												  <input type="text" class="form-control" id="despesas_extraordinarias_porpermanencia" name="despesas_extraordinarias_porpermanencia" value="<?php if (isset($plano)) echo $plano->despesas_extraordinarias_porpermanencia ?>" >
											  </div>
										</div> -->
									</div>

									<div class="row">
										<!-- <div class="col-md-4">
												<div class="form-group">
													<label for="exampleInputEmail1">REGRESSO ANTECIPADO</label>
													<input type="text" class="form-control" id="regresso_antecipado" name="regresso_antecipado" value="<?php if (isset($plano)) echo $plano->regresso_antecipado ?>" >
												</div>
										  </div> -->
										<div class="col-md-4">
											<div class="form-group">
												<label for="exampleInputEmail1">ACOMPANHAMENTO DE UM FAMILIAR</label>
												<input type="text" class="form-control" id="acompanhamento_familiar" name="acompanhamento_familiar" value="<?php if (isset($plano)) echo $plano->acompanhamento_familiar ?>" >
											</div>
										</div>
										<div class="col-md-4">
											<div class="form-group">
												<label for="exampleInputEmail1">CANCELAMENTO DE VIAGEM</label>
												<input type="text" class="form-control" id="cancelamento_de_viagem" name="cancelamento_de_viagem" value="<?php if (isset($plano)) echo $plano->cancelamento_de_viagem ?>" >
											</div>
										</div>

										<div class="col-md-4">
											<div class="form-group">
												<label for="exampleInputEmail1">EXTRAVIO DE BAGAGEM</label>
												<input type="text" class="form-control" id="extravio_de_bagagem" name="extravio_de_bagagem" value="<?php if (isset($plano)) echo $plano->extravio_de_bagagem ?>" >
											</div>
										</div>
									</div>

									<div class="row">

										<div class="col-md-4">
											<div class="form-group">
												<label for="exampleInputEmail1">RETORNO ANTECIPADO EMERGENCIAL</label>
												<input type="text" class="form-control" id="interrupcao_de_viagem" name="interrupcao_de_viagem" value="<?php if (isset($plano)) echo $plano->interrupcao_de_viagem ?>" >
											</div>
										</div>
										<!-- <div class="col-md-4">
											  <div class="form-group">
												  <label for="exampleInputEmail1">ORIENTAÇÃO EM CASO DE PERDA DE DOCUMENTOS</label>
												  <input type="text" class="form-control" id="orientacao_em_caso_de_perda_de_documentos" name="orientacao_em_caso_de_perda_de_documentos" value="<?php if (isset($plano)) echo $plano->orientacao_em_caso_de_perda_de_documentos ?>" >
											  </div>
										</div> -->

										<div class="col-md-4">
											<div class="form-group">
												<label for="exampleInputEmail1">ASSISTENCIA JURIDICA EM CASO DE ACIDENTE DE TRANSITO</label>
												<input type="text" class="form-control" id="assistencia_juridica_em_caso_de_acidente" name="assistencia_juridica_em_caso_de_acidente" value="<?php if (isset($plano)) echo $plano->assistencia_juridica_em_caso_de_acidente ?>" >
											</div>
										</div>
										<div class="col-md-4">
											<div class="form-group">
												<label for="exampleInputEmail1">ATENDIMENTO 24HS EM PORTUGUÊS</label>
												<input type="text" class="form-control" id="assistencia_de_fianca_em_caso_de_acidente" name="assistencia_de_fianca_em_caso_de_acidente" value="<?php if (isset($plano)) echo $plano->assistencia_de_fianca_em_caso_de_acidente ?>" >
											</div>
										</div>
									</div>

									<div class="row">
										<div class="col-md-4">
											<div class="form-group">
											<label for="exampleInputEmail1">COBERTURA PARA COVID-19</label>
												<input type="text" class="form-control" id="cobertura_covid" name="cobertura_covid" value="<?php if (isset($plano)) echo $plano->cobertura_covid ?>" >
											</div>
										</div>
									</div>
								</div>


								<div class="tab-pane" id="tab_3">
									<div class="row">
										<div class="col-md-12">
											<div class="form-group">
												<label for="exampleInputEmail1">OBSERVACÕES INTERNAS</label>
												<input type="text" class="form-control" id="observacoes_internas" name="observacoes_internas" value="<?php if (isset($plano)) echo $plano->observacoes_internas ?>" >
											</div>
										</div>
									</div>
								</div>
							</div>

						</div>
					</div>
				</div>

			</div>

			<input type="hidden" name="id_plano" value="<?php if (isset($plano)) echo $plano->id ?>">
			<div class="box-footer">
				Para efetuar o cadastro do Seguro apenas preencha os dados e clique em salvar.
				<button type="submit" class="btn btn-primary pull-right">Salvar Dados</button>
			</div>
        </form>

	</div>

</section>