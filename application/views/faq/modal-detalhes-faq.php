

<div class="row">
    <div class="col-md-3">
             <h3> <?php if(isset($dados_do_seguro->nome_plano) && !empty($dados_do_seguro->nome_plano)) echo $dados_do_seguro->nome_plano ?></h3>
    </div>
    <div class="col-md-3">
        <h5><b>Id Plano: </b>   <?php if(isset($dados_do_seguro->id) && !empty($dados_do_seguro->id)) echo $dados_do_seguro->id ?></h5>
        <h5><b>Data Cadastro: </b>  <?php if(isset($dados_do_seguro->data_cadastro) && !empty($dados_do_seguro->data_cadastro)) echo date_mysql_to_human($dados_do_seguro->data_cadastro) ?></h5>
        <h5><b>Cadastrado por: </b>  <?php if(isset($dados_do_seguro->cadastrado_por) && !empty($dados_do_seguro->cadastrado_por)) echo $dados_do_seguro->cadastrado_por ?></h5>
    </div>

    <div class="col-md-3 ">
        <h5><b>Seguradora: </b>  <?php if(isset($dados_do_seguro->seguradora) && !empty($dados_do_seguro->seguradora)) echo nome_seguradora($dados_do_seguro->seguradora) ?></h5>
        <h5><b>Idade Máxima: </b>  <?php if(isset($dados_do_seguro->id_maxima) && !empty($dados_do_seguro->id_maxima)) echo $dados_do_seguro->id_maxima ?></h5>
        <h5><b>Tipo Plano: </b>   <?php if(isset($dados_do_seguro->tipo_plano) && !empty($dados_do_seguro->tipo_plano)) echo $dados_do_seguro->tipo_plano ?></h5>
    </div>

    <div class="col-md-3 ">
        <h5><b>Multi Viagem: </b>  <?php if(isset($dados_do_seguro->multi_viagem) && !empty($dados_do_seguro->multi_viagem)) echo $dados_do_seguro->multi_viagem ?></h5>
        <h5><b>Valor do Dia: </b>  <?php if(isset($dados_do_seguro->valor_dia) && !empty($dados_do_seguro->valor_dia)) echo 'R$ '.FormatarValor($dados_do_seguro->valor_dia) ?></h5>
        <h5><b>Máximo Dias: </b>  <?php if(isset($dados_do_seguro->maximo_dias) && !empty($dados_do_seguro->maximo_dias)) echo $dados_do_seguro->maximo_dias ?></h5>
    </div>
</div>

<div class="row ">
    <div class="col-md-3">
    <h5><b>Status do Plano: </b><a class=<?php echo $dados_do_seguro->status_plano  == 0 ? '"btn btn-warning btn-xs"' : '"btn btn-success btn-xs"'; ?> ><?php echo $dados_do_seguro->status_plano  == 0 ? 'Inativo' : 'Ativo'; ?></a></h5>
    </div>
    <div class="col-md-9">
        <h5><b>Observações Cliente: </b>  <?php if(isset($dados_do_seguro->obs_cliente) && !empty($dados_do_seguro->obs_cliente)) echo $dados_do_seguro->obs_cliente ?></h5>
    </div>
</div>

<div class="row">
<hr>
    <div class="col-md-6">
        <h5><b>DESPESAS MÉDICAS E HOSPITALARES: </b>                        <?php if(isset($dados_do_seguro->despesas_medicas_e_hospitalares) && !empty($dados_do_seguro->despesas_medicas_e_hospitalares)) echo $dados_do_seguro->despesas_medicas_e_hospitalares ?></h5>
        <h5><b>DESPESAS ODONTOLÓGICAS: </b>                                 <?php if(isset($dados_do_seguro->despesas_odontologicas) && !empty($dados_do_seguro->despesas_odontologicas)) echo $dados_do_seguro->despesas_odontologicas ?></h5>
        <h5><b>REPATRIAÇÃO POR MORTE: </b>                                  <?php if(isset($dados_do_seguro->reparticao_de_morte) && !empty($dados_do_seguro->reparticao_de_morte)) echo $dados_do_seguro->reparticao_de_morte ?></h5>
        <h5><b>SEGURO DE INVALIDEZ PERMANENTE POR ACIDENTE: </b>            <?php if(isset($dados_do_seguro->seguro_de_invalidez_permanente_acidente) && !empty($dados_do_seguro->seguro_de_invalidez_permanente_acidente)) echo $dados_do_seguro->seguro_de_invalidez_permanente_acidente ?></h5>
        <!-- <h5><b>EXTENSÃO DE INTERNAÇÃO HOSPITALAR: </b>                      <?php if(isset($dados_do_seguro->extensao_de_internacao_hospitalar) && !empty($dados_do_seguro->extensao_de_internacao_hospitalar)) echo $dados_do_seguro->extensao_de_internacao_hospitalar ?></h5> -->
        <!-- <h5><b>REGRESSO ANTECIPADO: </b>                                    <?php if(isset($dados_do_seguro->regresso_antecipado) && !empty($dados_do_seguro->regresso_antecipado)) echo $dados_do_seguro->regresso_antecipado ?></h5> -->
        <h5><b>EXTRAVIO DE BAGAGEM: </b>                                    <?php if(isset($dados_do_seguro->extravio_de_bagagem) && !empty($dados_do_seguro->extravio_de_bagagem)) echo $dados_do_seguro->extravio_de_bagagem ?></h5>
        <h5><b>ASSISTENCIA JURIDICA EM CASO DE ACIDENTE DE TRANSITO: </b>   <?php if(isset($dados_do_seguro->assistencia_juridica_em_caso_de_acidente) && !empty($dados_do_seguro->assistencia_juridica_em_caso_de_acidente)) echo $dados_do_seguro->assistencia_juridica_em_caso_de_acidente ?></h5>
        <h5><b>DESPESAS MÉDICAS PARA DOENÇA PRÉ-EXISTENTE: </b>             <?php if(isset($dados_do_seguro->despesas_medicas_para_doenca_pre_existente) && !empty($dados_do_seguro->despesas_medicas_para_doenca_pre_existente)) echo $dados_do_seguro->despesas_medicas_para_doenca_pre_existente ?></h5>
        <h5><b>DESPESAS FARMACEUTICAS (medicamentos receitados): </b>       <?php if(isset($dados_do_seguro->despesas_farmaceuticas) && !empty($dados_do_seguro->despesas_farmaceuticas)) echo $dados_do_seguro->despesas_farmaceuticas ?></h5>
        <h5><b>TRANSLADO MÉDICO: </b>                                       <?php if(isset($dados_do_seguro->translado_medico) && !empty($dados_do_seguro->translado_medico)) echo $dados_do_seguro->translado_medico ?></h5>
        <h5><b>SEGURO DE MORTE ACIDENTAL: </b>                              <?php if(isset($dados_do_seguro->seguro_de_morte_acidental) && !empty($dados_do_seguro->seguro_de_morte_acidental)) echo $dados_do_seguro->seguro_de_morte_acidental ?></h5>
    </div>

    <div class="col-md-6">
        <h5><b>RECUPERAÇÃO DE ENFERMIDADE EM HOTEL: </b>                    <?php if(isset($dados_do_seguro->recuperacao_de_enfermidade) && !empty($dados_do_seguro->recuperacao_de_enfermidade)) echo $dados_do_seguro->recuperacao_de_enfermidade ?></h5>
        <h5><b>ACOMPANHAMENTO FAMILIAR (TKT AÉREO E HOTEL/DIA): </b>        <?php if(isset($dados_do_seguro->acompanhamento_familiar) && !empty($dados_do_seguro->acompanhamento_familiar)) echo $dados_do_seguro->acompanhamento_familiar ?></h5>
        <h5><b>INTERRUPÇÃO DE VIAGEM: </b>                                  <?php if(isset($dados_do_seguro->interrupcao_de_viagem) && !empty($dados_do_seguro->interrupcao_de_viagem)) echo $dados_do_seguro->interrupcao_de_viagem ?></h5>
        <h5><b>ASSISTENCIA DE FIANÇA EM CASO DE ACIDENTE DE TRANSITO: </b>  <?php if(isset($dados_do_seguro->assistencia_de_fianca_em_caso_de_acidente) && !empty($dados_do_seguro->assistencia_de_fianca_em_caso_de_acidente)) echo $dados_do_seguro->assistencia_de_fianca_em_caso_de_acidente ?></h5>
        <h5><b>DESPESAS MÉDICAS PARA ESPORTES (LAZER): </b>                 <?php if(isset($dados_do_seguro->despesas_medicas_para_esportes_lazer) && !empty($dados_do_seguro->despesas_medicas_para_esportes_lazer)) echo $dados_do_seguro->despesas_medicas_para_esportes_lazer ?></h5>
        <h5><b>REPATRIAÇÃO SANITÁRIA: </b>                                  <?php if(isset($dados_do_seguro->reparticao_sanitaria) && !empty($dados_do_seguro->reparticao_sanitaria)) echo $dados_do_seguro->reparticao_sanitaria ?></h5>
        <h5><b>REEMBOLSO DE GASTOS POR DEMORA DE BAGAGEM (6Hrs): </b>       <?php if(isset($dados_do_seguro->reembolso_de_gastos_por_demora) && !empty($dados_do_seguro->reembolso_de_gastos_por_demora)) echo $dados_do_seguro->reembolso_de_gastos_por_demora ?></h5>
        <!-- <h5><b>REGRESSO DE MENOR OU IDOSO DESACOMPANHADO: </b>              <?php if(isset($dados_do_seguro->regresso_de_menor_ou_idoso) && !empty($dados_do_seguro->regresso_de_menor_ou_idoso)) echo $dados_do_seguro->regresso_de_menor_ou_idoso ?></h5> -->
        <!-- <h5><b>DESPESAS EXTRAORDINÁRIAS POR PERMANÊNCIA FORÇADA: </b>       <?php if(isset($dados_do_seguro->despesas_extraordinarias_porpermanencia) && !empty($dados_do_seguro->despesas_extraordinarias_porpermanencia)) echo $dados_do_seguro->despesas_extraordinarias_porpermanencia ?></h5> -->
        <h5><b>CANCELAMENTO DE VIAGEM: </b>                                 <?php if(isset($dados_do_seguro->cancelamento_de_viagem) && !empty($dados_do_seguro->cancelamento_de_viagem)) echo $dados_do_seguro->cancelamento_de_viagem ?></h5>
        <!-- <h5><b>ORIENTAÇÃO EM CASO DE PERDA DE DOCUMENTOS: </b>              <?php if(isset($dados_do_seguro->orientacao_em_caso_de_perda_de_documentos) && !empty($dados_do_seguro->orientacao_em_caso_de_perda_de_documentos)) echo $dados_do_seguro->orientacao_em_caso_de_perda_de_documentos ?></h5> -->
    </div>
</div>

<div class="row ">
<hr>
    <div class="col-md-12">
        <h5><b>Observações Internas: </b>  <?php echo $dados_do_seguro->observacoes_internas ?></h5>
    </div>
</div>

<div class="row ">
    <hr>
</div>