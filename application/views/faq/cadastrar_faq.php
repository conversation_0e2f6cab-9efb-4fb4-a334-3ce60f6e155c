<section class="content-header">
	<?php if (isset($resp) && !empty($resp)) { ?>
		<div class="row">
			<div class="col-md-12">
				<div class="<?php echo $class ?>">
					<button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
					<h5><i class="icon fa fa-check"></i><?php echo $mensagem ?></h5>
				</div>
			</div>
			<br />
		</div>
	<?php } ?>
</section>

<section class="content">
    <div class="box box-info">
        <div class="box-header with-border">
			<h3 class="box-title">Cadastro de FAQ</h3>
        </div>

        <form id="formCadastroPlano" name="formulario" action="<?= base_url() ?>cadastrar-faq" method="POST">
			<input type="hidden" name="system" value="true" />
			<input type="hidden" name="form[id]" value="<?php echo isset($faq->id) ? $faq->id : NULL; ?>" />
			<div class="box-body">
				<div class="row">
					<div class="col-md-12">
						<!-- Custom Tabs -->
						<div class="nav-tabs-custom">
							<div class="tab-content">

								<div class="row">
									<div class="col-md-8">
										<div class="form-group">
											<label for="exampleInputEmail1">Pergunta</label>
											<input type="text" class="form-control" id="pergunta" name="form[pergunta]" value="<?php if (isset($faq)) echo $faq->pergunta ?>" placeholder="Pergunta">
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-md-8">
										<textarea id='editor1' name='form[resposta]' class="textarea" placeholder="Digite o conteúdo aqui"
												  style="width: 100%; height: 200px; font-size: 14px; line-height: 18px; border: 1px solid #dddddd; padding: 10px;">
													  <?= isset($faq->resposta) ? $faq->resposta : NULL; ?>
										</textarea>
									</div>
								</div>
								<div class="row">
									<div class="col-md-3">
										<div class="form-group">
											<label>Status</label>
											<select class="form-control" style="width: 100%;" name="form[status]">
												<option value="1" <?php if (isset($faq) && $faq->status == '1') echo 'selected'; ?> >Ativo</option>
												<option value="0" <?php if (isset($faq) && $faq->status == '0') echo 'selected'; ?>>Inativo</option>
											</select>
										</div>
									</div>
								</div>
							</div>

						</div>
					</div>
				</div>

			</div>

			<input type="hidden" name="id_plano" value="<?php if (isset($faq)) echo $faq->id ?>">
			<div class="box-footer">
				Para efetuar o cadastro do Item apenas preencha os dados e clique em salvar.
				<button type="submit" class="btn btn-primary pull-right">Salvar Dados</button>
			</div>
		</form>

	</div>


</section>