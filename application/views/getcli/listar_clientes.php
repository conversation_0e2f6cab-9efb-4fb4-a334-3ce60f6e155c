<!-- <section class="content-header">
	<?php if (isset($resp) && !empty($resp)) { ?>
		<div class="row">
			<div class="col-md-1">
				<?php if (isset($_SERVER['HTTP_REFERER'])): ?>
					<a  class="btn btn-primary" href="<?php echo $_SERVER['HTTP_REFERER']; ?>"><i class="fa fa-fw fa-arrow-left"></i> </a>
				<?php endif ?>
				<small></small>
				<br />
			</div>

			<div class="col-md-11">
				<div class="<?php echo $class ?>">
					<button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
					<h5><i class="icon fa fa-check"></i><?php echo $mensagem ?></h5>
				</div>
			</div>
			<br />
		</div>
	<?php }else { ?>
		<?php if (isset($_SERVER['HTTP_REFERER'])): ?>
			<a  class="btn btn-primary" href="<?php echo $_SERVER['HTTP_REFERER']; ?>"><i class="fa fa-fw fa-arrow-left"></i> </a>
		<?php endif ?>
		<small></small>
		<br />
	<?php } ?>
</section> -->

<section class="content">
	<div class="row">
        <div class="col-xs-12">
			<div class="box">
				<div class="box-header">
					</br>
					<div class="box-body table-responsive no-padding">
						<table id="tabela" class="table table-hover" data-order='[[ 0, "DESC" ]]' data-page-length='10'>
							<thead>
								<tr>
									<th>#</th>
									<th>Origem Lid</th>
									<th>Local Lid</th>
									<th>Nome</th>
									<th>Email</th>
									<th>Destino</th>
									<th>Data Ida</th>
									<th>Data Volta</th>
									<th>Passageiros</th>
									<th>Data Solicitação</th>
									<th>Ações</th>
								</tr>
							</thead>
							<tbody>
								<?php
								if (count($clientesPesca) > 0) {
									foreach ($clientesPesca as $s) {
										?>
										<tr>
											<td>
												<?php echo date_mysql_to_human($s->id) ?>
											</td>
											<td>
												<?php echo $s->origem ?>
											</td>
											<td>
												<?php echo $s->locallid ?>
											</td>
											<td>
												<?php echo $s->nome ?>
											</td>
											<td>
												<?php echo $s->email ?>
											</td>
											<td>
												<?php echo $s->destino ?>
											</td>
											<td>
												<?php echo date_mysql_to_human($s->dateida) ?>
											</td>
											<td>
												<?php echo date_mysql_to_human($s->datevolta) ?>
											</td>
											<td>
												<?php echo $s->passageiros ?>
											</td>
											<td>
												<?php echo $s->date_register ?>
											</td>

											<td>
												<?php if($s->status_atendimento == 0){ ?>
													<a href="<?php echo base_url('contactado-cliente-ancora/' . $s->id) ?>" onclick="return confirm('Deseja realmente marcar o cliente como atendido?');" class="btn btn-danger btn-xs">Por atender</a>
												<?php }else{ ?>
													<a class="btn btn-success btn-xs">Atendido</a>
												<?php } ?>	
											</td>
										</tr>
									<?php } ?>
								<?php } else { ?>
									<tr>
										<td colspan="8">
											NENHUM ITEM ENCONTRADO
										</td>
									</tr>
								<?php } ?>
							</tbody>

						</table>

					</div>
				</div>
			</div>
		</div>
	</div>

	<div class="modal fade" id="modal-dados" tabindex="-1">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
				<form action="<?php echo base_url() ?>salvar-informacoes-compra" method="POST" enctype="multipart/form-data">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal">
							<span aria-hidden="true">&times;</span>
						</button>
						<p class="modal-title" id="myModalLabel"><b>Autorização da compra</b></p>
					</div>
					<div class="col-md-12">
						<div class="modal-body"></div>

					</div>
					<div class="modal-footer">
						<button type="submit" class="btn btn-primary"> Salvar Informações</button>
						<button type="button" class="btn btn-gray" data-dismiss="modal">Sair</button>
					</div>
				</form>
            </div>
		</div>
	</div>

</section>
<!--
<style>
	.modal .modal-dialog { width: 90%; }
</style>
-->