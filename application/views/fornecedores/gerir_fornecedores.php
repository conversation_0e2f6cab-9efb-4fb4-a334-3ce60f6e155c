<section class="content-header">
	<?php if (isset($resp) && !empty($resp)) { ?>
		<div class="row">
			<div class="col-md-12">
				<div class="<?php echo $class ?>">
					<span class="text-sm"><?php echo $mensagem ?></span>
					<button type="button" class="btn-close text-lg py-3 opacity-10" data-bs-dismiss="alert" aria-label="Close">
						<span aria-hidden="true">×</span>
					</button>
				</div>
			</div>
			<br />
		</div>
	<?php } ?>
</section>

<div class="container-fluid my-3 py-3">
	<div class="row mb-5">
		<div class="col-lg-6 mt-lg-0">

			<div class="card" id="basic-info">
				<div class="card-header">
					<h5>Cadastro de Fornecedores</h5>
				</div>
				<form id="filterForm" action="<?= base_url('save-suppliers') ?>" method="POST">
					<div class="card-body pt-0">
						<div class="input-group input-group-dynamic">
							<label class="form-label">Nome Forcenedor</label>
							<input type="text" class="multisteps-form__input form-control"  name="name_supplier" required >
						</div>

						<div class="row mt-3">
							<input type="hidden" name="id_tabela" value="<?php if (isset($tabela)) echo $tabela->id ?>">
							<button type="submit" class="btn btn-primary btn-lg">Gravar Fornecedor</button>
						</div>

					</div>
				</form>
			</div>
		</div>

		<div class="col-lg-6 mt-lg-0">
			<div class="card">
				<!-- Card header -->
				<div class="card-header">
					<h5 class="mb-0">Fornecedores</h5>
					<p class="text-sm mb-0">
						Cadastrados
					</p>
				</div>
				<div class="table-responsive" style="padding: 9px 20px 20px 20px;">
					<table class="table table-flush" id="datatable-register-payments">
						<thead class="thead-light">
							<tr>
								<th style="width: 10px">ID</th>
								<th>Nome</th>
								<th>Author</th>
								<th style="width: 40px">Excluir</th>
							</tr>
						</thead>
						<tbody>
							<?php foreach ($suppliers as $t) { ?>
								<tr>
									<td><?php echo $t->id ?></td>
									<td><?php echo $t->name_supplier ?></td>
									<td><?php echo $t->author ?></td>
									<!-- <td><?php echo $t->status  == 0 ? 'Inativo' : 'Ativo'; ?></td> -->
									<td>
										<a href="<?php echo base_url('delete-suppliers/' . $t->id) ?>" style="color: #eb1f63;">Excluir</a>
									</td>
								</tr>
							<?php } ?>
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>

</div>
