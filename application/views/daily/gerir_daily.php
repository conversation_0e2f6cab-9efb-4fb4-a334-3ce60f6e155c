<section class="content-header">
	<?php if (isset($resp) && !empty($resp)) { ?>
		<div class="row">
			<div class="col-md-12">
				<div class="<?php echo $class ?>">
					<span class="text-sm"><?php echo $mensagem ?></span>
					<button type="button" class="btn-close text-lg py-3 opacity-10" data-bs-dismiss="alert" aria-label="Close">
						<span aria-hidden="true">×</span>
					</button>
				</div>
			</div>
			<br />
		</div>
	<?php } ?>
</section>

<div class="container-fluid my-3 py-3">
	<div class="row mb-5">
		<div class="col-lg-6 mt-lg-0">

			<div class="card" id="basic-info">
				<div class="card-header">
					<h5>Registro de Daily Scrum</h5>
				</div>
				<form id="filterForm" action="<?= base_url('save-daily') ?>" method="POST">
					<div class="card-body pt-0">
						<div class="input-group input-group-dynamic">
							<textarea class="multisteps-form__textarea form-control" rows="25" placeholder="Escrever aqui o resumo da Daily, esse resumo será gravado em base de dados e enviado para Ruben com cópia para todos do IT." name="text"></textarea>
						</div>

						<div class="row mt-3">
							<input type="hidden" name="id_tabela" value="<?php if (isset($tabela)) echo $tabela->id ?>">
							<button type="submit" class="btn btn-primary btn-lg">Gravar e Enviar</button>
						</div>

					</div>
				</form>
			</div>
		</div>

		<div class="col-lg-6 mt-lg-0">
			<div class="card">
				<div class="card-header pb-0">
					<h6>Timeline Dailys</h6>
				</div>
				<div class="card-body p-3">
					<div class="timeline timeline-one-side" data-timeline-axis-style="dotted">
						<?php foreach ($dailys as $d) { ?>
						<div class="timeline-block mb-3">
							<span class="timeline-step bg-info p-3">
								<i class="material-icons text-white text-sm opacity-10">
									mail
								</i>
							</span>
							<div class="timeline-content pt-1">
								<h6 class="text-dark text-sm font-weight-bold mb-0"><?= nome_user($d->author_id) ?> - <?= formatarDataHora($d->created_at) ?></h6>
							
								<p class="text-sm text-dark mt-3 mb-2">
									<?= nl2br(htmlspecialchars($d->text)) ?>
								</p>
								---------------------------------------------------------------------------
							</div>
							
						</div>
						<?php } ?>
					</div>
				</div>
			</div>
		</div>
	</div>

</div>