<section>
	<?php //var_dump($seguradora); 
	?>
	<div class="row invoice-info">
		<div class="col-sm-12 invoice-col">
			<strong>Cód Compra: &nbsp; </strong><?php if (isset($dados_compra) && !empty($dados_compra->cod_compra)) echo $dados_compra->cod_compra ?><br>
		</div>
	</div>
	<hr>

	<div class="panel box box-primary">
		<div class="box-header with-border">
			<h4 class="box-title">
				<a data-toggle="collapse" data-parent="#accordion" href="#detalhes">
					Detalhes da compra
				</a>
			</h4>
		</div>
		<div id="detalhes" class="panel-collapse collapse">
			<div class="box-body">

				<div class="col-sm-6 invoice-col">
					<address>
					<b>Data compra:&nbsp;</b><?php if (isset($dados_compra) && !empty($dados_compra->data_compra)) echo date_mysql_to_human($dados_compra->data_compra) ?><br>
					<b>Destino:&nbsp;</b><?php if (isset($dados_compra) && !empty($dados_compra->destino)) echo $dados_compra->destino ?><br><br>
						<strong>Responsável</strong><br>
						Nome:&nbsp;<?php if (isset($dados_compra) && !empty($dados_compra->primeiro_nome)) echo $dados_compra->primeiro_nome ?><br>
						CPF:&nbsp;<?php if (isset($dados_compra) && !empty($dados_compra->cpf)) echo $dados_compra->cpf ?><br>
						Email:&nbsp;<?php if (isset($dados_compra) && !empty($dados_compra->endereco_email)) echo $dados_compra->endereco_email ?><br><br>
						<!--Telefone:&nbsp;<?php if (isset($dados_compra) && !empty($dados_compra->telefone)) echo $dados_compra->telefone ?><br>-->
					</address>
				</div>
				
				<div class="col-sm-6 invoice-col">
					<address>
						<strong>Contato</strong><br>
						Telefone:&nbsp;<?php if (isset($dados_compra) && !empty($dados_compra->telefone)) echo $dados_compra->telefone ?><br>
						Endereço:&nbsp;<?php if (isset($dados_compra) && !empty($dados_compra->endereco)) echo $dados_compra->endereco ?><?php if (isset($dados_compra) && !empty($dados_compra->numero)) echo ', ' . $dados_compra->numero ?><br>
						<?php
						if (isset($dados_compra) && !empty($dados_compra->complemento)) {
							echo 'Complemento: ' . $dados_compra->complemento . '<br;>';
						}
						?>
						Cidade:&nbsp;<?php if (isset($dados_compra) && !empty($dados_compra->cidade)) echo $dados_compra->cidade . ' / ' . $dados_compra->uf ?><br>
						Cep:&nbsp;<?php if (isset($dados_compra) && !empty($dados_compra->cep)) echo $dados_compra->cep ?><br>
						<br>
						<strong>Pessoa Contato</strong><br>
						Nome:&nbsp;<?php if (isset($dados_compra) && !empty($dados_compra->nome_contato)) echo $dados_compra->nome_contato ?><br>
						Telefone:&nbsp;<?php if (isset($dados_compra) && !empty($dados_compra->telefone_contato)) echo $dados_compra->telefone_contato ?>
					</address>
				</div>
			</div>
		</div>
	</div>


	<div class="panel box box-primary">
		<div class="box-header with-border">
			<h4 class="box-title">
				<a data-toggle="collapse" data-parent="#accordion" href="#collapseTwo">
					Dados para preenchimento do DOC
				</a>
			</h4>
		</div>
		<div id="collapseTwo" class="panel-collapse collapse">
			<div class="box-body">
				<div class="col-sm-4 invoice-col">
					<b>Cliente:&nbsp;</b><?php if (isset($dados_compra) && !empty($dados_compra->primeiro_nome)) echo $dados_compra->primeiro_nome ?><br>
					<b>Vendedor:&nbsp;</b><?php if (isset($dados_compra) && !empty($dados_compra->vendedor)) echo $dados_compra->vendedor ?><br>
					<b>Emissor:&nbsp;</b><?php if (isset($dados_compra) && !empty($dados_compra->vendedor)) echo $dados_compra->vendedor ?><br>
					<b>Fornecedor:&nbsp;</b><?php if (isset($dados_compra->seguradora) && !empty($dados_compra->seguradora)) echo nome_seguradora($dados_compra->seguradora) ?><br>
					<b>Promotor:&nbsp;</b>?<br>
				</div>
				<div class="col-sm-4 invoice-col">
					<b>SISAV:&nbsp;</b>?<br>
					<b>Destino:&nbsp;</b><?php if (isset($dados_compra) && !empty($dados_compra->destino)) echo $dados_compra->destino ?><br>
					<b>Valor U$:&nbsp;</b><br>
					<b>Forma de Pagto:&nbsp;</b><?php if (isset($metodo_pagamento)) echo $metodo_pagamento ?><br>
					<b>Net:&nbsp;</b>?<br>
				</div>
				<div class="col-sm-4 invoice-col">
					<b>Câmbio:&nbsp;</b><?php if (isset($dados_compra) && !empty($dados_compra->cambio)) echo $dados_compra->cambio ?><br>
					<b>Data Emissão:&nbsp;</b>?<br>
					<b>Valor R$:&nbsp;</b><?php if (isset($dados_compra) && !empty($dados_compra->valor_com_desconto)) echo FormatarValor($dados_compra->valor_com_desconto) ?><br>
					<b>Rentabilidade:&nbsp;</b>?<br>
				</div>
			</div>
		</div>
	</div>
	</div>


	<h5> Viajantes <b>(<?php echo $dados_compra->viajantes ?>)</b> </h5>
	<div class="row">
		<div class="col-xs-12 table-responsive">
			<table class="table table-striped">
				<thead>
					<tr>
						<th>Nome</th>
						<th>Anexar Apólice</th>
						<th>CPF</th>

						<th>Data Nascim.</th>
						<th>Idade</th>
						<th>Sexo</th>
					</tr>
				</thead>
				<tbody>
					<?php foreach ($viajantes as $v) { ?>
						<tr>
							<td><?php if (isset($v->nome) && !empty($v->nome)) echo $v->nome ?></td>
							<td>
								<?php if (isset($v->apolice) && !empty($v->apolice)) { ?>
									<a class="btn btn-primary btn-xs" target="_blank" href="<?php echo base_url() . $v->link_apolice ?>" data-toggle="tooltip" data-original-title="<?php echo $v->nome_apolice ?>"> <i class="fa fa-fw fa-eye"></i> Ver Apólice </a>
								<?php } else { ?>
									<input id="fileUpload_<?php echo $v->id ?>" type="file" name="<?php echo 'ApoliceUser_' . $v->id ?>" />
								<?php } ?>
							</td>
							<td><?php if (isset($v->cpf) && !empty($v->cpf)) echo limpaCPF_CNPJ($v->cpf) ?></td>
							<td><?php if (isset($v->data_nascimento) && !empty($v->data_nascimento)) echo date_mysql_to_human($v->data_nascimento) ?></td>
							<td><?php if (isset($v->idade) && !empty($v->idade)) echo $v->idade ?></td>
							<td><?php if (isset($v->genero) && !empty($v->genero)) echo $v->genero ?></td>
						</tr>
					<?php } ?>

				</tbody>
			</table>
		</div>
	</div>
	<hr>
	<div class="row">
		<div class="col-xs-6">
			<p class="lead">Metodo de pagamento:</p>
			<p class="text-muted well well-sm no-shadow" style="margin-top: 10px;">
				<?php echo $metodo_pagamento ?>
			</p>
			<?php if ($dados_compra->tipo_pagamento == 'CC') { ?>
				<address>
					<strong>Dados do cartão</strong><br>
					Bandeira do cartão:&nbsp;<b><?php if (isset($dados_cartao) && !empty($dados_cartao->bandeira_cartao)) echo strtoupper($dados_cartao->bandeira_cartao); ?></b><br>
					Nome cartão:&nbsp;<b><?php if (isset($dados_cartao) && !empty($dados_cartao->nome_gravado_cartao)) echo $dados_cartao->nome_gravado_cartao ?></b><br>
					Número cartão:&nbsp;<b><?php if (isset($dados_cartao) && !empty($dados_cartao->numero_gravado_cartao)) echo $dados_cartao->numero_gravado_cartao ?></b><br>
					Código Verificador:&nbsp;<b><?php if (isset($dados_cartao) && !empty($dados_cartao->codigo_verificador)) echo $dados_cartao->codigo_verificador ?></b><br>
					Validade:&nbsp;<b><?php if (isset($dados_cartao) && !empty($dados_cartao->validade_cartao)) echo $dados_cartao->validade_cartao ?></b><br>
					Nº de Parcelas:&nbsp;<b><?php if (isset($dados_cartao) && !empty($dados_cartao->numero_parcelas)) echo $dados_cartao->numero_parcelas ?></b><br>
					<hr>

					<?php if (isset($dados_cartao->nome_titular_cartao) && !empty($dados_cartao->nome_titular_cartao)) { ?>

						<label class="label label-danger"> Outro titular</label><br><strong> Titular do cartão</strong>
						<br>
						Nome titular cartão:&nbsp;<?php if (isset($dados_cartao) && !empty($dados_cartao->nome_titular_cartao)) echo $dados_cartao->nome_titular_cartao ?><br>
						Identidade titular cartão:&nbsp;<?php if (isset($dados_cartao) && !empty($dados_cartao->identidade_titular_cartao)) echo $dados_cartao->identidade_titular_cartao ?><br>
						Cpf titular cartão:&nbsp;<?php if (isset($dados_cartao) && !empty($dados_cartao->cpf_titular_cartao)) echo $dados_cartao->cpf_titular_cartao ?><br>
						Endereço titular cartão:&nbsp;<?php if (isset($dados_cartao) && !empty($dados_cartao->endereco_titular_cartao)) echo $dados_cartao->endereco_titular_cartao ?><br>
						Nascimento titular cartão:&nbsp;<?php if (isset($dados_cartao) && !empty($dados_cartao->nascimento_titular_cartao)) echo $dados_cartao->nascimento_titular_cartao ?><br>
						Cidade / Estado titular cartão:&nbsp;<?php if (isset($dados_cartao) && !empty($dados_cartao->cidade_titular_cartao)) echo $dados_cartao->cidade_titular_cartao ?>
						/ <?php if (isset($dados_cartao) && !empty($dados_cartao->uf_titular_cartao)) echo $dados_cartao->uf_titular_cartao ?><br>

					<?php } else { ?>
						<strong>Titular do cartão</strong><br>
						<label class="label label-info"> O próprio solicitante</label>
					<?php } ?>
				</address>
			<?php } ?>
		</div>
		<div class="col-xs-6">
			<p class="lead">Detalhes</p>
			<div class="table-responsive">
				<table class="table">
					<?php if(isset($seguradora)){ ?>
						<tr>
						<td style="width:50%; padding: 4px;"> 
							<a class="btn btn-primary btn-xs" target="_blank" href="<?php echo base_url().'documentos/condicoes-gerais/'.$seguradora->termos ?>" data-toggle="tooltip" data-original-title="Ver apólice"> <i class="fa fa-fw fa-eye"></i></a> 
							Seguradora:</td>
						<th style="padding: 4px;">
							<?php if (isset($seguradora) && !empty($seguradora->nome_seguradora)) echo $seguradora->nome_seguradora ?>
						</th>
					</tr>

					<?php } ?>
					
					<tr>
						<td style="width:50%; padding: 4px;">Plano:</td>
						<th style="padding: 4px;"><?php if (isset($dados_compra->id_plano) && !empty($dados_compra->id_plano)) echo nome_plano($dados_compra->id_plano) ?></th>
					</tr>
					<tr>
						<td style="width:50%; padding: 4px;">Data Ida:</td>
						<th style="padding: 4px;"><?php if (isset($dados_compra->data_ida) && !empty($dados_compra->data_ida)) echo date_mysql_to_human($dados_compra->data_ida) ?></th>
					</tr>
					<tr>
						<td style="width:50%; padding: 4px;">Data Volta:</td>
						<th style="padding: 4px;"><?php if (isset($dados_compra->data_volta) && !empty($dados_compra->data_volta)) echo date_mysql_to_human($dados_compra->data_volta) ?></th>
					</tr>
					<tr>
						<td style="width:50%; padding: 4px;">Quantidade passageiros:</td>
						<th style="padding: 4px;"><?php echo $dados_compra->viajantes ?></th>
					</tr>
					<tr>
						<td style="width:50%; padding: 4px;">Total:</td>
						<th style="padding: 4px;">R$ <?php echo FormatarValor($dados_compra->valor_total) ?></th>
					</tr>

					<?php
					$valor = $dados_compra->valor_com_desconto;
					if (isset($valor) || $valor != null) { ?>
						<tr>
							<td style="width:50%; padding: 4px;">Desconto:</td>
							<th style="padding: 4px;"><?php echo ($dados_compra->tipo_desconto == 'P' ? $dados_compra->desconto . ' %' : 'R$ ' . FormatarValor($dados_compra->desconto)) ?></th>
						</tr>
						<tr>
							<td style="width:50%; padding: 4px;">Total com desconto:</td>
							<th style="padding: 4px;">R$ <?php echo FormatarValor($dados_compra->valor_com_desconto) ?></th>
						</tr>
					<?php } ?>
				</table>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-md-10">
			<label>Observação Interna</label>
			<textarea name='obs_interna' placeholder="Digite o conteúdo aqui" style="width: 100%; height: 70px; font-size: 14px; line-height: 18px; border: 1px solid #dddddd; padding: 10px;"><?php if (isset($dados_compra->obs_interna)) echo $dados_compra->obs_interna ?></textarea>
		</div>
		<div class="col-md-2 ">
			<label for="exampleInputEmail1">Status</label>
			<select class="form-control" name="status_pagamento">
				<option value="A" <?php if (isset($dados_compra->status_pagamento) && $dados_compra->status_pagamento == 'A') echo 'selected'; ?>>Autorizado</option>
				<option value="NA" <?php if (isset($dados_compra->status_pagamento) && $dados_compra->status_pagamento == 'NA') echo 'selected'; ?>>Não Autorizado</option>
				<option value="PC" <?php if (isset($dados_compra->status_pagamento) && $dados_compra->status_pagamento == 'PC') echo 'selected'; ?>>Problemas com cartão</option>
			</select>
		</div>
	</div>
	<!-- <div class="row">
		<hr>

		<h5>DADOS PARA LIBERAÇÃO</h5>
		<div class="col-md-4">
			<label for="exampleInputEmail1">Autorização</label>
			<input type="text" class="form-control" id="autorizacao1" name="autorizacao1" value="<?php if (isset($dados_compra->autorizacao1)) echo $dados_compra->autorizacao1 ?>" placeholder="Autorização">
		</div>

		<div class="col-md-4">
			<label for="exampleInputEmail1">Autorização</label>
			<input type="text" class="form-control" id="autorizacao2" name="autorizacao2" value="<?php if (isset($dados_compra->autorizacao1)) echo $dados_compra->autorizacao2 ?>" placeholder="Autorização">
		</div>

		

	</div> -->
	<!-- <div class="row">
		<hr>
		<h5>ANEXAR ARQUIVOS</h5>

		<div class="col-md-6">
			<input id="condicoes_gerais" type="file" name="condicoes_gerais" class="btn-file" />
			<label for="condicoes_gerais">
				<span> <i class="fa fa-paperclip"></i>&nbsp;Condições Gerais</span>
			</label>
		</div>

		<div class="col-md-6">
			<input id="fileUpload2" type="file" name="documento2" class="btn-file" />
			<label for="fileUpload2">
				<span> <i class="fa fa-paperclip"></i>&nbsp;Apólice de Seguro</span>
			</label>
		</div>
	
	</div> -->

	<input type="hidden" id="id_compra" name="id_compra" value="<?php if (isset($dados_compra)) echo $dados_compra->id ?>">
	<input type="hidden" id="cod_compra" name="cod_compra" value="<?php if (isset($dados_compra)) echo $dados_compra->cod_compra ?>">
	<!-- /.row -->

</section>