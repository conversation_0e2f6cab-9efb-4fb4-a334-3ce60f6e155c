<section class="content-header">
	<?php if (isset($resp) && !empty($resp)) { ?>
		<div class="row">
			<div class="col-md-1">
				<?php if (isset($_SERVER['HTTP_REFERER'])) : ?>
					<a class="btn btn-primary" href="<?php echo $_SERVER['HTTP_REFERER']; ?>"><i class="fa fa-fw fa-arrow-left"></i> </a>
				<?php endif ?>
				<small></small>
				<br />
			</div>

			<div class="col-md-11">
				<div class="<?php echo $class ?>">
					<button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
					<h5><i class="icon fa fa-check"></i><?php echo $mensagem ?></h5>
				</div>
			</div>
			<br />
		</div>
	<?php } else { ?>
		<?php if (isset($_SERVER['HTTP_REFERER'])) : ?>
			<a class="btn btn-primary" href="<?php echo $_SERVER['HTTP_REFERER']; ?>"><i class="fa fa-fw fa-arrow-left"></i> </a>
		<?php endif ?>
		<small></small>
		<br />
	<?php } ?>
</section>

<section class="content">
	<div class="row">
		<div class="col-xs-12">
			<div class="box">

				<div class="box-header">
					</br>
					<div class="box-body table-responsive no-padding">
						<table id="tabela-datatable" class="table table-hover">
							<thead>
								<tr>
									<th>#</th>
									<th>Origem</th>
									<th>Data Compra</th>
									<th>Nome Cliente</th>
									<th>Plano</th>
									<th>Valor Compra</th>
									<th>Status da compra</th>
									<th>Aut. / Detalh. / Apólices</th>
									<th>Ações</th>
								</tr>
							</thead>
							<tbody>
								<?php
								if (count($compras) > 0) {
									foreach ($compras as $s) {

								?>
										<tr>
											<td>
												<?php echo date_mysql_to_human($s->id) ?>
											</td>
											<td>
												<?php echo $s->name_origem ?>
											</td>
											<td>
												<?php echo date_mysql_to_human($s->data_compra) ?>
											</td>
											<td>
												<?php echo (isset($s->primeiro_nome) && !empty($s->primeiro_nome) ? $s->primeiro_nome : '') ?>

											</td>
											<td>
												<?php if (isset($s->id_plano) && !empty($s->id_plano)) echo nome_plano($s->id_plano) ?>
											</td>
											<td>
												<?php
												if (isset($s->desconto) && $s->desconto != null) {
													echo 'R$ ' . FormatarValor($s->valor_com_desconto);
												} else {
													if (isset($s->valor_total) && !empty($s->valor_total)) {
														echo 'R$ ' . FormatarValor($s->valor_total);
													} else {
														echo 'Sem valor';
													}
												}
												?>
												<?php echo (isset($s->tipo_desconto) && $s->tipo_desconto != null ? '(CA)' : '') ?>
											</td>
											<td>
												<?php if ($s->status_pagamento == 'A') { ?>
													<a class="btn btn-success btn-xs">Autorizado</a>
												<?php } elseif ($s->status_pagamento == 'NA') { ?>
													<a class="btn btn-danger btn-xs">Não Autorizou</a>
												<?php } elseif ($s->status_pagamento == 'PC') { ?>
													<a class="btn btn-danger btn-xs">Problemas com cartão</a>
												<?php } elseif ($s->status_pagamento == 'PD') { ?>
													<a class="btn btn-danger btn-xs">Pendente de dados</a>
												<?php } elseif ($s->status_pagamento == '') { ?>
													<a class="btn btn-primary btn-xs">Aguardando</a>
												<?php } ?>
											</td>
											<td>
												<?php if (isset($s->status_pagamento) && !empty($s->status_pagamento)) { ?>
													<a class="link-modal-dados btn btn-primary btn-xs" href="<?php echo base_url('autorizar-compra/' . $s->id) ?>"> Detalhes</a>&nbsp;&nbsp;
													<b><?php if (isset($s->total_apolices) && !empty($s->total_apolices)) echo $s->total_apolices . ' - <i class="fa fa-paperclip" aria-hidden="true"></i>'; ?> </b>
												<?php } else { ?>
													<a class="link-modal-dados btn btn-primary btn-xs" href="<?php echo base_url('autorizar-compra/' . $s->id) ?>"> Autorizar</a>
												<?php } ?>
											</td>
											<td>
												<?php if ($s->status_pagamento == 'A') { ?>
													<?php if ($s->email_de_autorizacao_enviado == '1') { ?>
														<a href="<?php echo base_url('enviar-email-autorizacao/' . $s->id) ?>" class="btn btn-success btn-xs"><small><i class="fa fa-send-o"></i></small> Email Enviado</a>
													<?php } else { ?>
														<a href="<?php echo base_url('enviar-email-autorizacao/' . $s->id) ?>" class="btn btn-info btn-xs"><small><i class="fa fa-send-o"></i></small> Enviar Email Autorizada</a>
													<?php } ?>
													<!--<a href="<?php echo base_url('finalizar-compra/' . $s->id) ?>" class="btn btn-primary btn-xs">Finalizar</a>-->
												<?php } else { ?>
													Aguardando autorização
												<?php } ?>

												<a href="<?php echo base_url('excluir-compra/' . $s->id) ?>" onclick="return confirm('Deseja realmente excluir este registro?');" class="btn btn-danger btn-xs">Excluir</a>
											</td>
										</tr>
									<?php
										// var_dump($s);
										// die();
									} ?>
								<?php } else { ?>
									<tr>
										<td colspan="8">
											NENHUM ITEM ENCONTRADO
										</td>
									</tr>
								<?php } ?>
							</tbody>
						</table>
					</div>
				</div>

			</div>
		</div>
	</div>
</section>

<div class="modal fade" id="modal-dados" tabindex="-1">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<form action="<?php echo base_url() ?>salvar-informacoes-compra" method="POST" enctype="multipart/form-data">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal">
						<span aria-hidden="true">&times;</span>
					</button>
					<p class="modal-title" id="myModalLabel"><b>Autorização da compra</b></p>
				</div>
				<div class="col-md-12">
					<div class="modal-body"></div>

				</div>
				<div class="modal-footer">
					<button type="submit" class="btn btn-primary"> Salvar Informações</button>
					<button type="button" class="btn btn-gray" data-dismiss="modal">Sair</button>
				</div>
			</form>
		</div>
	</div>
</div>