<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Clientes extends MY_Controller
{

	public function __construct()
	{
		parent::__construct();
		$this->load->model('Clientes_model');
		$this->load->model('Emails_model');
	}


	public function listar_clientes()
	{
		$data = $this->_getDataDashboard();

		$this->db->select('*');
		$this->db->from('tbl_user_clients');
		$this->db->where('user_active', 1);
		$this->db->order_by('id', 'desc');
		$data['clients'] = $this->db->get()->result();

		$data += $this->_getRespAction();

		$data['menuListCliente'] = true;
		$data['view'] = 'clientes/listar_clientes';
		$this->load->view('includes/template', $data);
	}


	public function dados($codcliente = 0)
	{
		$data = array();
		$this->db->select('*');
		$this->db->where('id', $codcliente);
		$data['dados_cliente'] = $this->db->get('tbl_user_clients')->row();

		$this->load->view('clientes/modal-detalhes-cliente', $data);
	}


	public function listar_clientes_pendente_aprovacao()
	{
		$data = $this->_getDataDashboard();

		$this->db->select('*');
		$this->db->from('tbl_user_clients');
		$this->db->where('user_active', 0);
		$this->db->order_by('id', 'desc');
		$data['clients'] = $this->db->get()->result();

		$data += $this->_getRespAction();

		$data['menuListCliente'] = true;
		$data['view'] = 'clientes/listar_clientes_pendentes';
		$this->load->view('includes/template', $data);
	}



	public function approve_client($id_client = null)
	{
		$idUser = $this->_getUserId()->id_usuario;
		if ($this->Clientes_model->approve_user($idUser, $id_client)) {
			redirect('list-clients?resp=s');
		}
	}



	# Produtos
	# =============================================================================================
	public function list_user_products($user_token = null)
	{

		$data = $this->_getDataDashboard();
		$resp = $this->_getDataClient($user_token);

		$user_id = $resp->id;
		$data['data_client'] = $resp;

		if ($user_id> 0) {
	
			$this->db->select('*');
			$this->db->from('tbl_user_products');
			$this->db->where('user_id', $resp->id);
			$products = $this->db->get()->result();

			foreach($products as $key => $prod){

				if($prod->product_session == 1){
					$label = 'Pendente';
				}
				if($prod->product_session == 2){
					$label = 'Á venda';
				}
				if($prod->product_session == 3){
					$label = 'Revisão';
				}
				if($prod->product_session == 9){
					$label = 'Inativo';
				}
				$products[$key]->status_label = $label;
			}	
				
			$data['list_products_client'] = $products;

			# Produtos Ativo
			$data['products_sale'] = $this->db->where('user_id', $user_id)->where('product_session', 2)->where('is_deleted', 0)->get('tbl_user_products')->result();
			
			# Produtos Revisão
			$data['products_revision'] = $this->db->where('user_id', $user_id)->where('product_session', 3)->where('is_deleted', 0)->get('tbl_user_products')->result();

			# Produtos Pendentes
			$data['products_pending'] = $this->db->where('user_id', $user_id)->where('product_session', 1)->where('is_deleted', 0)->get('tbl_user_products')->result();

			#Produtos inativos
			$data['products_inactive'] = $this->db->where('user_id', $user_id)->where('product_session', 9)->where('is_deleted', 0)->get('tbl_user_products')->result();


			$data['menuListPorductsPending'] = true;
			$data['menuProducts'] = true;


			$data['view'] = 'clientes/list_user_products_pending';
			$this->load->view('includes/template', $data);
		} else {
			echo 'user_not_exist, contactar setor desenvolvimento';
			die();
		}
	}

	// public function detalhes_produto($id_product)
	// {
	// 	$id_product = $this->input->get('id_product');

	// 	if ($id_product > 0) {

	// 		$this->db->select('*');
	// 		$this->db->from('tbl_user_products');
	// 		$this->db->where('id', $id_product);
	// 		$product = $this->db->get()->row();

	// 		$this->db->select('pm.price as mkt_price, m.name as mkt_name');
	// 		$this->db->from('tbl_producs_marktplaces pm');
	// 		$this->db->join('tbl_marketplaces m', 'm.id = pm.id_marketplace');
	// 		$this->db->where('pm.cod_mkt_product', $product->cod_mkt);
	// 		$this->db->where('pm.status', 1);
	// 		$marketplaces = $this->db->get()->result();

	// 		// foreach ($marketplaces as $key => $value) {
	// 		// 	$marketplaces[$key]->checkbox = '<input type="checkbox" class="i-checks" name="input[]">';
	// 		// 	$marketplaces[$key]->button = '<a href="google.com.br" class="i-checks">Teste</a>';
	// 		// }

	// 		$success = array(
	// 			'status' => 'success',
	// 			'cod' => 200,
	// 			'product' => $product,
	// 			'marketplaces' => $marketplaces
	// 		);
	// 		echo json_encode($success);
	// 		return;
	// 	} else {
	// 		echo 'product_not_find, contactar setor desenvolvimento';
	// 		die();
	// 	}
	// }


	public function details_user_products()
	{
		$id_product = $this->input->get('id_product');

		if ($id_product > 0) {

			$this->db->select('*');
			$this->db->from('tbl_user_products');
			$this->db->where('id', $id_product);
			$product = $this->db->get()->row();

			$this->db->select('pm.price as mkt_price, m.name as mkt_name');
			$this->db->from('tbl_producs_marktplaces pm');
			$this->db->join('tbl_marketplaces m', 'm.id = pm.id_marketplace');
			$this->db->where('pm.cod_mkt_product', $product->cod_mkt);
			$this->db->where('pm.status', 1);
			$marketplaces = $this->db->get()->result();

			// foreach ($marketplaces as $key => $value) {
			// 	$marketplaces[$key]->checkbox = '<input type="checkbox" class="i-checks" name="input[]">';
			// 	$marketplaces[$key]->button = '<a href="google.com.br" class="i-checks">Teste</a>';
			// }

			$success = array(
				'status' => 'success',
				'cod' => 200,
				'product' => $product,
				'marketplaces' => $marketplaces
			);
			echo json_encode($success);
			return;
		} else {
			echo 'product_not_find, contactar setor desenvolvimento';
			die();
		}
	}


	public function change_status_product()
	{
		$id_product = $this->input->post('id_product');
		$obs = $this->input->post('obs');
		$id_status = $this->input->post('id_status');

		if ($id_product > 0) {

			# update product
			$this->db->where('id', $id_product);

			if($id_status == 9){
				$active = 0;
			}else{
				$active = 1;
			}

			$dados = array(
				'product_session' => $id_status,
				'active' => $active,
				'obs' => $obs
			);

			if ($this->db->update('tbl_user_products', $dados)) {

				# Se atualizou produto, faz o envio do email para o User
				//$this->Emails_model->send_email_product_revision_client($id_product);


				$success = array(
					'status' => 'success',
					'cod' => 200,
					'msg' => 'product_actived',
					'send' => true
				);
				echo json_encode($success);
				return;

			}
		} else {
			echo 'error_product_send_revision, contactar setor desenvolvimento';
			die();
		}
	}




	public function send_product_to_revision()
	{
		$id_product = $this->input->post('id_product');

		if ($id_product > 0) {

			# update product
			$this->db->where('id', $id_product);

			$dados = array(
				'product_session' => 3,
				'obs' => $this->input->post('obs')
			);

			if ($this->db->update('tbl_user_products', $dados)) {

				# Se atualizou produto, faz o envio do email para o User
				//$this->Emails_model->send_email_product_revision_client($id_product);


				$success = array(
					'status' => 'success',
					'cod' => 200,
					'msg' => 'product_send_revision',
					'send' => true
				);
				echo json_encode($success);
				return;

			}
		} else {
			echo 'error_product_send_revision, contactar setor desenvolvimento';
			die();
		}
	}


	public function send_product_to_approval()
	{
		$id_product = $this->input->post('id_product');

		if ($id_product > 0) {

			# update product
			$this->db->where('id', $id_product);

			$dados = array(
				'product_session' => 2,
				'obs' => $this->input->post('obs') ?  $this->input->post('obs') : null,
				'title' => $this->input->post('title_product') ?  $this->input->post('title_product') : null,
				'description' => $this->input->post('description') ?  $this->input->post('description') : null,
				'descriptionSoon' => $this->input->post('descriptionsoon') ?  $this->input->post('descriptionsoon') : null,
			);


			if ($this->db->update('tbl_user_products', $dados)) {

				// $this->db->select('*');
				// $this->db->from('tbl_user_products');
				// $this->db->where('id', $id_product);
				// $product = $this->db->get()->row();

				// $this->db->select('email');
				// $this->db->from('tbl_user_clients');
				// $this->db->where('id', $product->user_id);
				// $user = $this->db->get()->row();

				# Se atualizou produto, faz o envio do email para o User


				$success = array(
					'status' => 'success',
					'cod' => 200,
					'msg' => 'product_send_approval',
					'send' => true
				);
				echo json_encode($success);
				return;

			}
		} else {
			echo 'error_product_send_approval, contactar setor desenvolvimento';
			die();
		}
	}

}
