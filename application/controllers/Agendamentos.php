<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Agendamentos extends MY_Controller
{

	public function __construct()
	{
		parent::__construct();
		$this->load->model('Agendamentos_model');
	}


	public function index()
	{
		$data = $this->_getDataDashboard();
		$data['hash'] = $this->_getToken();

		$data['profissionais'] = $this->_getProfissionais();
		$data['servicos'] = $this->_getServicos();
		$data['clientes'] = $this->_getClientes();

		$data['schedule'] = true;
		$data['new_scheduling'] = true;

		$data['resp'] = $this->input->get('resp');
		$data += $this->_getRespAction();

		$data['menuNewAgendamento'] = true;
		$data['view'] = 'agendamento/agendar';


		$this->load->view('includes/template', $data);
	}


	public function salvar_agendamento()
	{
		$user_system = $this->_getUserId()->id_usuario;
		$tipo_agendamento =  $this->input->post('tipo_agendamento');

		$idProfissional = null;
		if ($tipo_agendamento == 1) {
			$idProfissional = $this->_getIdProfissional($this->input->post('profissional'));
		}

		$id_cliente = $this->_getClient($this->input->post('cod_cliente'))->id;

		if ($this->Agendamentos_model->salvar_agendamento($user_system, $idProfissional, $tipo_agendamento, $id_cliente)) {
			redirect('calendar');
			//redirect('listar-cupons?resp=s');
		}
	}

	public function listar_agendamentos()
	{
		$data = $this->_getDataDashboard();
		$data += $this->_getDataCookie();


		$this->db->from('view_agendamentos');
		$data['agendamentos'] = $this->db->get()->result();

		$data['menuListAgendamento'] = true;
		$data['view'] = 'agendamento/listar_agendamentos';
		$this->load->view('includes/template', $data);
	}


	public function calendario()
	{
		$data = $this->_getDataDashboard();
		$data['hash'] = $this->_getToken();

		$this->db->select('*');
		$this->db->from('view_agendamentos_por_profissional');
		$data['agendaProfissional'] = $this->db->get()->result();


		$data['menuCalendario'] = true;
		$data['view'] = 'agendamento/calendario';
		$this->load->view('includes/template', $data);
	}


	public function getAgendamentos()
	{
		// $this->db->select('ag.*, pr.nome, pr.color, pr.tipo_profissional, cli.nome');
		// $this->db->from('tbl_agendamentos ag');
		// $this->db->join("tbl_profissionais pr", "pr.id = ag.id_profissional",'left');
		// $this->db->join("tbl_clientes cli", "cli.id = ag.id_cliente",'left');

		$this->db->from('view_agendamentos');
		$this->db->where('cancelamento', 0);
		$result  = $this->db->get()->result();

		$resp = [];
		foreach ($result as $key => $r) {

			$color = $r->cor_profissional;
			$data = explode("-", $r->data);
			$horas = explode(":", $r->hora);
			
			if ($r->id_servico == 7) {
				$color = '#a4927c';
			}
			if ($r->id_servico == 2 || $r->id_servico == 3) {
				$color = '#FA58F4';
			}

			if ($r->id_servico == 4) {
				$color = '#58ACFA';
			}

			if ($r->id_servico == 6) {
				$color = '#0c834a';
			}




			$resp[$key] = [
				'compromisso' => $r->nome_cliente,
				'color' => $color,
				'dia' => $data[2],
				'mes' => $data[1],
				'ano' => $data[0],
				'hora' => $horas[0],
				'minuto' => $horas[1]
			];
		}
		echo json_encode($resp, JSON_UNESCAPED_UNICODE);
	}


	public function editar($cod_agendamento = null)
	{
		$data = $this->_getDataDashboard();
		$idUser = $this->_getUserId()->id_usuario;

		$this->db->select('*');
		$this->db->where('cod_agendamento', $cod_agendamento);
		$this->db->where('user_system', $idUser);
		$this->db->from('tbl_agendamentos');
		$agendamento = $this->db->get()->row();


		if (!empty($agendamento)) {
			$data['agendamento'] = $agendamento;
			$data['schedule'] = true;
			$data['new_scheduling'] = true;
		} else {
			$data['no_agendamento'] = true;
			//$data['menuNewCliente'] = true;
		}

		$data['view'] = 'agendamento/agendar';
		$this->load->view('includes/template', $data);
	}

	public function salvar_edicao()
	{
		$codcli = $this->input->post('cli');
		if ($this->Clientes_model->salvar_edicao($codcli)) {
			redirect('list-clients?resp=ed');
		}
	}



	public function videos()
	{
		$data = $this->_getDataDashboard();
		$data += $this->_getDataCookie();


		// $this->db->from('view_agendamentos');
		// $data['agendamentos'] = $this->db->get()->result();

		//$data['menuListAgendamento'] = true;
		$data['view'] = 'videos/video_stream';
		$this->load->view('includes/template', $data);
	}
































	public function agendar()
	{
		$data = $this->_getLanguageDashboard();
		$data += $this->_getDataCookie();
		$data['cadastrar_testemunho'] = true;

		$data['resp'] = $this->input->get('resp');
		$resp = $this->input->get('resp');
		if ($resp == 's') {
			$data['mensagem'] = 'Cupom cadastrado com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'e') {
			$data['mensagem'] = 'Cupom excluido com sucesso!';
			$data['class'] = 'alert alert-danger alert-dismissible';
		} elseif ($resp == 'ed') {
			$data['mensagem'] = 'Cupom editado com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'p') {
			$data['mensagem'] = 'Você não tem autorização para editar Cupom!';
			$data['class'] = 'alert alert-warning alert-dismissible';
		}

		$data['view'] = 'cupom/cadastrar_cupom';
		$this->load->view('includes/template', $data);
	}

	public function salvar()
	{
		if ($this->Agendamentos_model->salvar_cupom()) {
			redirect('listar-cupons?resp=s');
		}
	}



	public function listar_cupom()
	{
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');
		$data['listar_cupons'] = true;

		$this->db->select('*');
		$this->db->from('tbl_cupom');
		$this->db->order_by('id_cupom', "desc");

		$data['cupons'] = $this->db->get()->result();

		$data['resp'] = $this->input->get('resp');
		$resp = $this->input->get('resp');
		if ($resp == 's') {
			$data['mensagem'] = 'Cupom cadastrado com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'e') {
			$data['mensagem'] = 'Cupom excluido com sucesso!';
			$data['class'] = 'alert alert-danger alert-dismissible';
		} elseif ($resp == 'ed') {
			$data['mensagem'] = 'Cupom editado com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'p') {
			$data['mensagem'] = 'Você não tem autorização para editar Cupom!';
			$data['class'] = 'alert alert-warning alert-dismissible';
		}

		$data['view'] = 'cupom/listar_cupons';
		$this->load->view('includes/template', $data);
	}

	public function excluir_cupom($id = null)
	{
		if ($this->Agendamentos_model->excluir_cupom($id)) {
			redirect('listar-cupons?resp=e');
		} else {
		}
	}

	public function editar_cupom($id = null)
	{
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');
		$data['cadastrar_cupom'] = true;

		$this->db->select('*');
		$this->db->where('id_cupom', $id);
		$this->db->from('tbl_cupom');
		$data['cupom'] = $this->db->get()->row();

		$data['view'] = 'cupom/cadastrar_cupom';
		$this->load->view('includes/template', $data);
	}
}
