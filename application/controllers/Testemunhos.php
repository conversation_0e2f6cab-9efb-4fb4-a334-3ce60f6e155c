<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Testemunhos extends MY_Controller {

	public function __construct() {
		parent::__construct();

		# carregando o model para utiliza-lo
		$this->load->model('Testemunho_model');
		$this->load->library('Uteis');
	}


	public function cadastrar_testemunho() {
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');
		$data['cadastrar_testemunho'] = true;

		$data['resp'] = $this->input->get('resp');
		$resp = $this->input->get('resp');
		if ($resp == 's') {
			$data['mensagem'] = 'Testemunho cadastrado com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'e') {
			$data['mensagem'] = 'Testemunho excluido com sucesso!';
			$data['class'] = 'alert alert-danger alert-dismissible';
		} elseif ($resp == 'ed') {
			$data['mensagem'] = 'Testemunho editado com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'p') {
			$data['mensagem'] = 'Você não tem autorização para editar Testemunho!';
			$data['class'] = 'alert alert-warning alert-dismissible';
		}

		$data['view'] = 'testemunhos/cadastrar_testemunho';
		$this->load->view('includes/template', $data);
	}

	public function salvar() {
		if ($this->Testemunho_model->salvar_testemunho()) {
			redirect('listar-testemunhos?resp=s');
		}
	}

	public function salvar_edicao() {
		$id = $this->input->post('id_testemunho');
		if ($this->Testemunho_model->salvar_edicao($id)) {
			redirect('listar-testemunhos?resp=ed');
		}
	}

	public function listar_testemunhos() {
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');
		$data['listar_testemunhos'] = true;

		$this->db->select('*');
		$this->db->from('tbl_testemunhos');
		$data['testemunhos'] = $this->db->get()->result();

		$data['resp'] = $this->input->get('resp');
		$resp = $this->input->get('resp');
		if ($resp == 's') {
			$data['mensagem'] = 'Testemunho cadastrado com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'e') {
			$data['mensagem'] = 'Testemunho excluido com sucesso!';
			$data['class'] = 'alert alert-danger alert-dismissible';
		} elseif ($resp == 'ed') {
			$data['mensagem'] = 'Testemunho editado com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'p') {
			$data['mensagem'] = 'Você não tem autorização para editar Testemunho!';
			$data['class'] = 'alert alert-warning alert-dismissible';
		}

		$data['view'] = 'testemunhos/listar_testemunhos';
		$this->load->view('includes/template', $data);
	}

	public function excluir_testemunho($id = null) {
		if ($this->Testemunho_model->excluir_testemunho($id)) {
			redirect('listar-testemunhos?resp=e');
		} else {

		}
	}

	public function editar_testemunho($id = null) {
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');
		$data['cadastrar_testemunho'] = true;

		$this->db->select('*');
		$this->db->where('id', $id);
		$this->db->from('tbl_testemunhos');
		$data['testemunho'] = $this->db->get()->row();

		$data['view'] = 'testemunhos/cadastrar_testemunho';
		$this->load->view('includes/template', $data);
	}

	


	


}
