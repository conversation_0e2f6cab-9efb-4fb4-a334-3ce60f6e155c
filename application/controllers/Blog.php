<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Blog extends MY_Controller {

	public function __construct() {
		parent::__construct();
		$this->load->library('Uteis');
		$this->load->helper('okuploader');

		$this->load->model('crud');
		$this->load->model('Blog_model');

		//$this->Blog_model->table 	 = 'tbl_blog';
		//$this->Blog_model->directory = './upload/blog/';

		//$this->load->library('upload');
	}

	/**
	 * Categorias de Postagens do Blog
	 */
	public function gerenciar_categoria() {
		$this->Blog_model->setTable('tbl_categoria_de_blog');
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');
		$data['gerenciar_categoria'] = true;

		$this->db->select('*');
		$this->db->from('tbl_categoria_de_blog');
		$data['categorias'] = $this->db->get()->result();

		$data['resp'] = $this->input->get('resp');
		$resp = $data['resp'];
		if ($resp == 'e') {
			$data['mensagem'] = 'Item excluído com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'p') {
			$data['mensagem'] = 'Você não tem autorização para editar este Item!';
			$data['class'] = 'alert alert-danger alert-dismissible';
		}

		$data['view'] = 'blog/categorias/gerenciar_categoria';
		$this->load->view('includes/template', $data);
	}

	public function cadastrar_categoria($id = NULL) {
		$this->crud->setTable('tbl_categoria_de_blog');
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');
		$data['cadastrar_categoria'] = true;

		if (isset($this->input->post()['system']) && $this->input->post()['system'] == 'true') {
			$post = $this->input->post()['form'];

	
			// $this->db->select('*');
			// $this->db->from('tbl_categoria_de_blog');
			// $data['categorias'] = $this->db->get()->result();

            // //Verifica existência da slug informada
			// //$loadSlug = $this->crud->get('slug', $post['slug']);

			// if (!empty($loadSlug) && $loadSlug[0]->id != $post['id']) {
			// 	$post['slug'] = self::geraSlug($post['slug']);
			// }
			if (!empty($post['id'])) {
				$this->crud->update($post, $post['id']);
			} else {
				$post['date'] = date('Y-m-d H:i:s');
				$this->crud->create($post);
			}
			redirect('gerenciar-categoria');
		}

		if (!empty($id)) {
			$data['categoria'] = $this->crud->get('id', $id);
		}

		$data['view'] = 'blog/categorias/cadastrar-categoria';
		$this->load->view('includes/template', $data);
	}

	public function geraSlug($slug_original) {
		$aux = $slug_original . '-' . rand();
		$loadSlug = $this->crud->get('slug', $aux);
		if (!empty($loadSlug)) {
			return self::geraSlug($aux);
		}
		return $aux;
	}

	public function excluir_categoria($id = null) {
		$this->crud->setTable('tbl_categoria_de_blog');
		if ($this->crud->delete($id)) {
			redirect('gerenciar-categoria/?resp=e');
		}
	}

	/**
	 * Postagens do Blog
	 */
	public function gerenciar_post() {
		$this->Blog_model->setTable('tbl_blog');
		$data['email_usuario'] = $this->session->userdata['email_usuario'];
		$data['codigo_usuario'] = $this->session->userdata['token_usuario'];
		$data['nome_usuario'] = $this->session->userdata['nome_usuario'];
		$data['gerenciar_post'] = true;

		//$data['blogs'] = $this->Blog_model->findAllBlog(array('status' => '1',), 'id', 'DESC');
		$this->db->select('b.*, cb.categoria as nome_categoria');
		$this->db->from('tbl_blog b');
		$this->db->join('tbl_categoria_de_blog cb', 'cb.id = b.categoria');
		$this->db->order_by('b.id', 'DESC');
		$data['blogs'] = $this->db->get()->result();

		$data['resp'] = $this->input->get('resp');
		$resp = $data['resp'];
		if ($resp == 'e') {
			$data['mensagem'] = 'Item excluído com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'p') {
			$data['mensagem'] = 'Você não tem autorização para editar este Item!';
			$data['class'] = 'alert alert-danger alert-dismissible';
		}

		//dd($data);

		$data['view'] = 'blog/posts/gerenciar-post';
		$this->load->view('includes/template', $data);
	}



	public function cadastrar_post($id = NULL) {
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');
		$data['cadastrar_post'] = true;

		$this->crud->setTable('tbl_blog');

		if (isset($this->input->post()['system']) && $this->input->post()['system'] == 'true') {
			$post = $this->input->post()['form'];
			//var_dump($_FILES);die();

			$img_blog = $_FILES['img_blog'];
			$aux = explode('.', strtolower(trim($img_blog['name'])));
			$extensao = array_pop($aux);

			if (!empty($img_blog['name']) && !empty($extensao)) {
				$new_logo = 'CG_' . $this->uteis->slug($post['titulo']) . '_' . rand() . '.' . $extensao;
				$configuracao1 = array(
					'upload_path' => '../assets/images/blog/',
					'allowed_types' => 'jpg|jpeg|png|gif',
					'file_name' => $new_logo,
					'max_size' => '1280'
				);
				//$this->upload->initialize($configuracao1);
				$this->load->library('upload', $configuracao1);
				if (!$this->upload->do_upload('img_blog')) {
					redirect('gerenciar-post?resp=e', 'refresh');
				}
				$this->removeLogotipo($post['id']);
				$post['img_blog'] = $new_logo;
			}

			// if (isset($this->input->post()['imagem_up'])) {
			// 	$files = $this->input->post()['imagem_up'];
			// 	if ($files) {
			// 		$i = 0;
			// 		$arrImg = Array();
			// 		foreach ($files as $file) {
			// 			$arrImg[$i]['ordem'] = $i;
			// 			$arrImg[$i]['imagem'] = $file;
			// 			$arrImg[$i]['date'] = date('Y-m-d H:i:s');
			// 			if ($post['id']) {
			// 				$arrImg[$i]['blog'] = $post['id'];
			// 			}
			// 			$i++;
			// 		}
			// 		$imagens = $this->session->set_userdata('imagens_tmp', $files);
			// 	}
			// }

			//echo "<pre>";var_dump($post);die();

			//$post['slug'] = preg_replace("/[^a-zA-Z0-9-]+/", "", str_replace(" ", "-", strtolower($post['titulo'])));

			//$arrErros = NoticiaBusiness::validateNoticia($post);
			//Verifica existência da slug informada
			/*$loadSlug = $this->crud->get('slug', $post['slug']);
			if (!empty($loadSlug) && $loadSlug[0]->id != $post['id']) {
				$post['slug'] = self::geraSlug($post['slug']);
			}*/
			if (!empty($post['id'])) {
				$this->crud->update($post, $post['id']);
				//$this->Blog_model->Atualizar($post['id'], $post);
				$this->mover_arquivos($post['id']);
				unset($_SESSION['imagens_tmp']);
			} else {
				$post['date'] = date('Y-m-d H:i:s');
				$cod = $this->crud->create($post);
				//$this->cria_pasta($cod);
				unset($_SESSION['imagens_tmp']);
			}
			redirect('gerenciar-post');
		} else {
			$this->ajaxlimpatmp();
			if (isset($arrImg) && count($arrImg) > 0) {
				foreach ($arrImg as $id => $vl) {
					$this->uteis->remove_tmp_file($arrImg[$id]);
				}
				unset($_SESSION['imagens_tmp']);
			}
		}

		if (!empty($id)) {
			$data['blog'] = $this->crud->get('id', $id);
		}
		$this->Blog_model->setTable('tbl_categoria_de_blog');
		$data['categorias'] = $this->Blog_model->findAllCategoria();

		$data['view'] = 'blog/posts/cadastrar-post';
		$this->load->view('includes/template', $data);
	}

	public function excluir_post($id = null) {
		$this->crud->setTable('tbl_blog');
		if ($this->crud->delete($id)) {
			redirect('gerenciar-post/?resp=e');
		}
	}

	/**
	 *  FUNÇÕES ORAKUPLOADER
	 */
	public function ajaxlimpatmp() {
		if (!empty($_SESSION['imagens_tmp'])) {
			foreach ($_SESSION['imagens_tmp'] as $img) {
				$this->uteis->remove_tmp_file($img);
			}
		}
		unset($_SESSION['imagens_tmp']);
	}

	public function get_imagens($id = NULL) {
		$this->Blog_model->setTable('tbl_blog_imagem');
		if ($id) {
			$list = $this->Blog_model->findAllImagem(array('a.blog' => $id,), 'a.ordem', 'ASC');
			if (count($list) > 0) {
				foreach ($list as $item) {
					$img .= $item['imagem'] . "|";
				}
				$aux = substr($img, 0, -1);
			} else {
				$aux = 'false';
			}
		} else {
			$aux = 'false';
		}
		echo $aux;
	}

	public function cria_pasta($codigo) {
		$this->crud->setTable('tbl_blog');
		
		$load = $this->crud->get('id', $codigo)[0];
		
		if (!empty($load->id)) {
			$pasta = '../uploads' . DIRECTORY_SEPARATOR . 'blog_' . $load->id;
			if (!is_dir($pasta)) {
				mkdir($pasta, 0777);
			} else {
				if (!is_writable($pasta)) {
					chmod($pasta, 0777);
				}
			}
			self::mover_arquivos($codigo);
		}
	}

	public function mover_arquivos($codigo) {
		$arquivos = $this->Blog_model->findAllImagem(array('a.blog' => $codigo), 'a.id', 'asc');
		$pasta = '../uploads' . DIRECTORY_SEPARATOR . 'blog_' . $codigo;
		if (count($arquivos) > 0) {
			if (!is_dir($pasta)) {
				self::cria_pasta($codigo);
			}
			foreach ($arquivos as $arq) {
				if (file_exists('../uploads' . DIRECTORY_SEPARATOR . $arq['imagem'])) {
					rename('../uploads' . DIRECTORY_SEPARATOR . $arq['imagem'], $pasta . DIRECTORY_SEPARATOR . $arq['imagem']);
				}
			}
		}
	}

	public function removeLogotipo($id) {
		$this->db->select('*');
		$this->db->from('tbl_blog');
		$this->db->where('id', $id);
		$query = $this->db->get();

		if ($query->num_rows() > 0) {
			$row = $query->row_array();
			if (!empty($row['img_seguradora'])) {
				@unlink('../assets/images/blog/' . $row['img_blog']);
			}
		}
	}

}
