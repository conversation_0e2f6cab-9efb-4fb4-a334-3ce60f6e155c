<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Products extends MY_Controller
{

	public function __construct()
	{
		parent::__construct();
		// $this->sldc = $this->load->database('sldc', TRUE);
		$this->load->model('Clientes_model');
		// $this->load->library('Uteis');
	}

	public function list_products_to_approval()
	{
		$data = $this->_getDataDashboard();

		$this->db->select('up.user_id, count(*) as quantity_products, uc.name, uc.user_token, uc.email');
		$this->db->from('tbl_user_products up');
		$this->db->join('tbl_user_clients uc', 'uc.id = up.user_id');
		$this->db->where('up.product_session', 1);
		$this->db->group_by('user_id');
		$data['list_user_products'] = $this->db->get()->result();

	
		$data['view'] = 'products/list_products_to_approval';
		$this->load->view('includes/template', $data);
	}

}
