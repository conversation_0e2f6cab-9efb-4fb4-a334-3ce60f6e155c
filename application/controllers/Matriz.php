<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Matriz extends MY_Controller {


	public function __construct() {
		parent::__construct();

		# carregando o model para utiliza-lo
		$this->load->model('Cupom_model');
		$this->load->library('Uteis');
	}



	public function listar()
	{
		$data = $this->_getDataCookie();

		$this->db->select('*');
		$this->db->from('tbl_clientes');
		$data['clientes'] = $this->db->get()->result();

		$data['resp'] = $this->input->get('resp');
		$data += $this->_getRespAction();

		$data['view'] = 'clientes/listar_clientes';
		$this->load->view('includes/template', $data);
	}



	public function cadastrar()
	{
		$data = $this->_getLanguageDashboard();
		$data += $this->_getDataCookie();
		$data['hash'] = $this->_getToken();

		$data['resp'] = $this->input->get('resp');
		$data += $this->_getRespAction();

		$data['menuNewCliente'] = true;
		$data['view'] = 'clientes/cadastrar_cliente';
		$this->load->view('includes/template', $data);
	}


	public function salvar()
	{
		if ($this->Clientes_model->cadastrar_cliente()) {
			redirect('listar-clientes?resp=s');
		}
	}

	public function salvar_cadastro() {
		if ($this->Cupom_model->salvar_cupom()) {
			redirect('listar-cupons?resp=s');
		}
	}

	public function editar($id = null) {
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');
		$data['cadastrar_cupom'] = true;
		
		$this->db->select('*');
		$this->db->where('id_cupom', $id);
		$this->db->from('tbl_cupom');
		$data['cupom'] = $this->db->get()->row();

		$data['view'] = 'cupom/cadastrar_cupom';
		$this->load->view('includes/template', $data);
	}

	public function salvar_edicao() {
		$id = $this->input->post('id_cupom');
		if ($this->Cupom_model->salvar_edicao($id)) {
			redirect('listar-cupons?resp=ed');
		}
	}


	public function excluir($id = null) {
		if ($this->Cupom_model->excluir_cupom($id)) {
			redirect('listar-cupons?resp=e');
		} else {

		}
	}


}
