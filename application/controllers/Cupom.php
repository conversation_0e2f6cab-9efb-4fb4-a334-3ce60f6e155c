<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Cupom extends MY_Controller {

	public function __construct() {
		parent::__construct();

		# carregando o model para utiliza-lo
		$this->load->model('Cupom_model');
		$this->load->library('Uteis');
	}


	public function cadastrar_cupom() {
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');
		$data['cadastrar_testemunho'] = true;

		$data['resp'] = $this->input->get('resp');
		$resp = $this->input->get('resp');
		if ($resp == 's') {
			$data['mensagem'] = 'Cupom cadastrado com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'e') {
			$data['mensagem'] = 'Cupom excluido com sucesso!';
			$data['class'] = 'alert alert-danger alert-dismissible';
		} elseif ($resp == 'ed') {
			$data['mensagem'] = 'Cupom editado com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'p') {
			$data['mensagem'] = 'Você não tem autorização para editar Cupom!';
			$data['class'] = 'alert alert-warning alert-dismissible';
		}

		$data['view'] = 'cupom/cadastrar_cupom';
		$this->load->view('includes/template', $data);
	}

	public function salvar() {
		if ($this->Cupom_model->salvar_cupom()) {
			redirect('listar-cupons?resp=s');
		}
	}

	public function salvar_edicao() {
		$id = $this->input->post('id_cupom');
		if ($this->Cupom_model->salvar_edicao($id)) {
			redirect('listar-cupons?resp=ed');
		}
	}

	public function listar_cupom() {
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');
		$data['listar_cupons'] = true;

		$this->db->select('*');
		$this->db->from('tbl_cupom');
		$this->db->order_by('id_cupom', "desc");
		
		$data['cupons'] = $this->db->get()->result();

		$data['resp'] = $this->input->get('resp');
		$resp = $this->input->get('resp');
		if ($resp == 's') {
			$data['mensagem'] = 'Cupom cadastrado com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'e') {
			$data['mensagem'] = 'Cupom excluido com sucesso!';
			$data['class'] = 'alert alert-danger alert-dismissible';
		} elseif ($resp == 'ed') {
			$data['mensagem'] = 'Cupom editado com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'p') {
			$data['mensagem'] = 'Você não tem autorização para editar Cupom!';
			$data['class'] = 'alert alert-warning alert-dismissible';
		}

		$data['view'] = 'cupom/listar_cupons';
		$this->load->view('includes/template', $data);
	}

	public function excluir_cupom($id = null) {
		if ($this->Cupom_model->excluir_cupom($id)) {
			redirect('listar-cupons?resp=e');
		} else {

		}
	}

	public function editar_cupom($id = null) {
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');
		$data['cadastrar_cupom'] = true;
		
		$this->db->select('*');
		$this->db->where('id_cupom', $id);
		$this->db->from('tbl_cupom');
		$data['cupom'] = $this->db->get()->row();

		$data['view'] = 'cupom/cadastrar_cupom';
		$this->load->view('includes/template', $data);
	}

	


	


}
