<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Faq extends MY_Controller {

	public function __construct() {
		parent::__construct();
		$this->load->model('Faq_model');
		$this->Faq_model->setTable('tbl_faq');
	}

	public function listar_faq() {
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');
		$data['listar_faq'] = true;

		$data['faq'] = $this->Faq_model->getAll();

		$data['resp'] = $this->input->get('resp');
		$resp = $this->input->get('resp');
		if ($resp == 'atts') {
			$data['mensagem'] = 'Item atualizado com susesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'ep') {
			$data['mensagem'] = 'Por favor, verifique o preenchimento do formuário!';
			$data['class'] = 'alert alert-danger alert-dismissible';
		} elseif ($resp == 's') {
			$data['mensagem'] = 'Item cadastrado com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'e') {
			$data['mensagem'] = 'Item excluído com sucesso!';
			$data['class'] = 'alert alert-sucess alert-dismissible';
		} elseif ($resp == 'ed') {
			$data['mensagem'] = 'Item editado com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'p') {
			$data['mensagem'] = 'Você não tem autorização para editar este Item!';
			$data['class'] = 'alert alert-danger alert-dismissible';
		}

		$data['view'] = 'faq/listar_faq';
		$this->load->view('includes/template', $data);
	}

	public function cadastrar_faq($id = null) {
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');
		$data['cadastra_faq'] = true;

		$data['faq'] = NULL;
		if (isset($id)) {
			$this->db->select('*');
			$this->db->where('id', $id);
			$this->db->from('tbl_faq');
			$data['faq'] = $this->db->get()->row();
		}

		if ($this->input->post('system') == 'true') {
			$post = $this->input->post('form');
			if (!empty($post['pergunta']) && !empty($post['resposta'])) {
				if (!empty($post['id'])) {
					// UPDATE FAQ
					$this->Faq_model->Atualizar($post['id'], $post);
					redirect('listar-faq?resp=atts', 'refresh');
				} else {
					// CREATE FAQ
					$this->Faq_model->Cadastrar($post);
					redirect('listar-faq?resp=s', 'refresh');
				}
			} else {
				redirect('cadastrar-faq?resp=ep', 'refresh');
			}
		}
		$data['resp'] = $this->input->get('resp');
		$resp = $data['resp'];
		if ($resp == 'ep') {
			$data['mensagem'] = 'Por favor, verifique o preenchimento do formuário!';
			$data['class'] = 'alert alert-danger alert-dismissible';
		} elseif ($resp == 's') {
			$data['mensagem'] = 'Item cadastrado com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'e') {
			$data['mensagem'] = 'Item excluido com sucesso!';
			$data['class'] = 'alert alert-danger alert-dismissible';
		} elseif ($resp == 'ed') {
			$data['mensagem'] = 'Item editado com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'p') {
			$data['mensagem'] = 'Você não tem autorização para editar este Item!';
			$data['class'] = 'alert alert-warning alert-dismissible';
		}

		$data['view'] = 'faq/cadastrar_faq';
		$this->load->view('includes/template', $data);
	}

	public function excluir_faq($id = null) {
		if ($this->Faq_model->Deletar($id)) {
			redirect('listar-faq/?resp=e');
		} else {

		}
	}

}
