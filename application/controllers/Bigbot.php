<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON>bot extends MY_Controller {

	public function __construct() {
		parent::__construct();

		#$this->dbzap = $this->load->database('db_zap', TRUE);
	}


	public function check_message()
	{
		$id_message = $this->input->post('id_message');

		$author = [
			'user_name' => $this->session->userdata('nome_usuario'),
			'user_code' => $this->session->userdata('token_usuario'),
			'date_hour' => date('Y-m-d H:m:i'),
			'origin' => 'kurumin'
		];

		$this->dbzap->where('id', $id_message);
		$data = [
			'read' => 1,
			'author' => json_encode($author)
		];
		if($this->dbzap->update('tbl_msgs_criticas', $data)){
			$resp = [
				'update' => 'success'
			];
			echo json_encode($resp);
		}
	}

	public function excluir_cupom($id = null) {
		if ($this->Cupom_model->excluir_cupom($id)) {
			redirect('listar-cupons?resp=e');
		} else {

		}
	}

	public function editar_cupom($id = null) {
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');
		$data['cadastrar_cupom'] = true;
		
		$this->db->select('*');
		$this->db->where('id_cupom', $id);
		$this->db->from('tbl_cupom');
		$data['cupom'] = $this->db->get()->row();

		$data['view'] = 'cupom/cadastrar_cupom';
		$this->load->view('includes/template', $data);
	}

	


	


}
