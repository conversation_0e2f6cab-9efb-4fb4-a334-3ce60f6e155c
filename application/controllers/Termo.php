<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Termo extends MY_Controller {

	public function __construct() {
		parent::__construct();
	}

	public function cadastrar_termo($indice = null) {
		$this->load->model('Termo_model');

		$load = $this->Termo_model->getLast();
		if (count($load) > 0) {
			$data['id'] = $load[0]->id;
			$data['conteudo'] = $load[0]->conteudo;
		} else {
			$data['id'] = null;
			$data['conteudo'] = NULL;
		}
		$data['email_usuario'] = $this->session->userdata['email_usuario'];
		$data['codigo_usuario'] = $this->session->userdata['token_usuario'];
		$data['nome_usuario'] = $this->session->userdata["nome_usuario"];

		$data['view'] = 'termo/cadastrar_termo';
		$this->load->view('includes/template', $data);
	}

	public function salvar_conteudo() {
		$this->load->model('Termo_model');
		if (!empty($_POST['system']) && $_POST['system'] == 'true') {
			$post = $_POST;
			unset($post['system']);
			$data['conteudo'] = $post['conteudo'];
			if (!empty($post['id'])) {
				$this->Termo_model->Atualizar($post['id'], $data);
			} else {
				$this->Termo_model->Inserir($data);
			}
			redirect('cadastrar-termo');
		}
	}

}
