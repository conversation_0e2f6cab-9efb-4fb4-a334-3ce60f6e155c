<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Suppliers extends MY_Controller
{

	public function __construct()
	{
		parent::__construct();
	}

	public function register_suppliers() {
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');
		$data['purchase_status'] = true;

		$this->db->select('*');
		$this->db->from('tbl_suppliers');
		$data['suppliers'] = $this->db->get()->result();


		$data['resp'] = $this->input->get('resp');
		$resp = $this->input->get('resp');
		if ($resp == 's') {
			$data['mensagem'] = 'Fornecedor salvo com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible text-white';
		} elseif ($resp == 'e') {
			$data['mensagem'] = 'Fornecedor excluido com sucesso!';
			$data['class'] = 'alert alert-danger alert-dismissible text-white';
		} elseif ($resp == 'p') {
			$data['mensagem'] = 'Você não tem autorização para editar Fornecedores!';
			$data['class'] = 'alert alert-danger alert-dismissible text-white';
		}

		$data['view'] = 'fornecedores/gerir_fornecedores';
		$this->load->view('includes/template', $data);
	}

	public function save_suppliers() {
		if ($this->save()) {
			redirect('suppliers?resp=s');
		}
	}

	public function dalete_suppliers($id = null) {
		if ($this->excluir_tabela($id)) {
			redirect('suppliers?resp=e');
		} else {

		}
	}


	public function save() { 
		$data['author'] = $this->session->userdata('nome_usuario');
		$data['name_supplier'] = $this->input->post('name_supplier');
		$data['status'] = 1;

		return $this->db->insert('tbl_suppliers', $data);
	}

	public function excluir_tabela($id) {
		$this->db->where('id', $id);
		return $this->db->delete('tbl_suppliers');
	}


	
}
