<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Notifications extends MY_Controller {

	public function __construct() {
		parent::__construct();
	}

	public function get_latest_notification($user_id) {
        // Query para buscar a notificação mais antiga do usuário especificado
        $this->db->where('user_id', $user_id);
		$this->db->where('is_read', 0);
        $this->db->order_by('id', 'ASC'); // Ordena pelo campo 'id' em ordem ascendente
        $this->db->limit(1);
        $query = $this->db->get('tbl_operations_notifications');
        $notification = $query->row();

		$msg = '<b>De:</b> '.$this->get_user($notification->user_from).'    <br>    <b>Assunto: </b>'.$notification->title.'    <br>    <b>Data:</b> '.$this->formatarData($notification->created_at);

        // Verifica se encontrou alguma notificaçãoformatarData
        if ($notification) {
            $response = array(
                'status' => 'success',
                'msg' => $msg,
                'id' => $notification->id 
            );
        } else {
            $response = array(
                'status' => 'no_notification'
            );
        }

        // Define o cabeçalho para JSON
        $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode($response));
    }

	private function get_user($user_id)
	{
		$this->db->where('id_usuario', $user_id);
		$user = $this->db->get('tbl_usuarios')->row();
		return $user->nome;
	}

    public function mark_as_read($notification_id) {
        $data = json_decode($this->input->raw_input_stream, true);

        if (isset($data['is_read']) && $data['is_read'] == 1) {
            $update_data = array(
                'is_read' => 1,
                'read_at' => date('Y-m-d H:i:s', strtotime($data['read_at']))
            );

            $this->db->where('id', $notification_id);
            $this->db->update('tbl_operations_notifications', $update_data);

            if ($this->db->affected_rows() > 0) {
                $response = array('status' => 'success');
            } else {
                $response = array('status' => 'error', 'message' => 'Não foi possível atualizar a notificação.');
            }

            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($response));
        }
    }

	public function formatarData($data) {
		// Converte a string de data para um objeto DateTime
		$dateTime = DateTime::createFromFormat('Y-m-d H:i:s', $data);
	
		// Verifica se a data foi convertida corretamente
		if ($dateTime === false) {
			return "Data inválida";
		}
	
		// Formata a data para o formato desejado
		return $dateTime->format('d.m.Y - H:i:s');
	}

    public function send_notification() {

        if ($this->input->is_ajax_request()) {
            $data = json_decode($this->input->raw_input_stream, true);
            $colaborador_id = $data['colaborador_id'];
            $text_notification = $data['text_notification'];

            // Validar os dados
            if (empty($colaborador_id) || empty($text_notification)) {
                echo json_encode(array('success' => false, 'message' => 'Campos obrigatórios não preenchidos.'));
                return;
            }

            $notification_data = array(
                'user_from' =>  $this->session->userdata('id_usuario'),
                'user_id' => $colaborador_id,
                'title' => $text_notification
            );

            $insert = $this->db->insert('tbl_operations_notifications', $notification_data);

            if ($insert) {
                $response = array('success' => true);
            } else {
                $response = array('success' => false, 'message' => 'Erro ao inserir no banco de dados.');
            }

            echo json_encode($response);
        } else {
            show_404();
        }
    }

}
