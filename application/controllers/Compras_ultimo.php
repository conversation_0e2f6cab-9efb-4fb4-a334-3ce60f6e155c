<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Compras extends MY_Controller {

	public function __construct() {
		parent::__construct();

		# carregando o model para utiliza-lo
		$this->load->model('Seguros_model');
		$this->load->model('Compras_model');
		$this->load->model('Emails_model');
	}

	public function index() {
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');

		# Pegando usuários ativos
		$this->db->where('status_plano', 1);
		$planos = $this->db->get('tbl_newseguros')->result();
		$data['planos_ativos'] = $this->db->affected_rows($planos);

		$data['view'] = 'dashboard';
		$this->load->view('includes/template', $data);
	}

	public function cadastrar_seguros() {
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');
		$data['cadastra_plano'] = true;

		# Pegando seguradoras para apresentar no cadastro
		$this->db->select('id, nome_seguradora');
		$data['seguradoras'] = $this->db->get('tbl_seguradoras')->result();

		$data['resp'] = $this->input->get('resp');
		$resp = $this->input->get('resp');
		if ($resp == 's') {
			$data['mensagem'] = 'Plano cadastrado com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'e') {
			$data['mensagem'] = 'Plano excluido com sucesso!';
			$data['class'] = 'alert alert-danger alert-dismissible';
		} elseif ($resp == 'ed') {
			$data['mensagem'] = 'Plano editado com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'p') {
			$data['mensagem'] = 'Você não tem autorização para editar Instituição!';
			$data['class'] = 'alert alert-warning alert-dismissible';
		}

		$data['view'] = 'seguros/cadastrar_seguros';
		$this->load->view('includes/template', $data);
	}

	public function salvar() {
		if ($this->Seguros_model->cadastrar_plano()) {
			redirect('listar-planos?resp=s');
		} else {

		}
	}


	public function listar_compras() {
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');
		$data['listar_pedidos_compra_do_site'] = true;

		$this->db->select('sc.*, o.name_origem');
		$this->db->from('tbl_solicitacao_de_compra sc');
		$this->db->join("tbl_origem o", "o.id = sc.origem_id",'left');	
		//$this->db->order_by('data_compra', 'DESC');
		$this->db->order_by('id', 'DESC');
		$compras = $this->db->get()->result();


		foreach ($compras as $key => $value){
			
			# Pegando seguros ativos
			$this->db->where('id_compra', $value->id);
			$apolices = $this->db->get('tbl_cliente_apolice')->result();
			$total_apolices = $this->db->affected_rows($apolices);

			$compras[$key]->total_apolices = $total_apolices;
		}

		$data['compras'] = array();
		if (isset($compras) && !empty($compras)) {
			$data['compras'] = $compras;
		}

		$data['view'] = 'compras/listar_compras';
		$this->load->view('includes/template', $data);
	}



	public function enviar_email_autorizacao() {

		$id = $this->uri->segment(2);
		if ($this->Emails_model->compra_autorizada($id)) {
			$this->db->where('id', $id);
			$dados = array(
				'email_de_autorizacao_enviado' => '1'
			);
			$this->db->update('tbl_solicitacao_de_compra', $dados);
		}

		redirect('compras-efetuadas');
	}



	public function excluir_compra($id = 0) {
		$data = array();
		$this->db->where('id', $id);
		$this->db->delete('tbl_solicitacao_de_compra');
		redirect('compras-efetuadas');
	}



	# passando dados do seguro para o modal para consulta do usuário
#=====================================================================================================

	public function dados_compra($id = 0) {
		$data = array();

		$this->db->select('sc.*, tu.nome as vendedor, ts.seguradora');
		$this->db->from('tbl_solicitacao_de_compra sc');
		$this->db->join('tbl_usuarios tu', 'tu.id_usuario = sc.id_vendedor', 'left');
		$this->db->join('tbl_newseguros ts', 'ts.id = sc.id_plano', 'left');
		$this->db->where('sc.id', $id);
		$compra = $this->db->get()->row();
		$data['dados_compra'] = $compra;

		$this->db->select('tbls.*');
		$this->db->from('tbl_seguradoras tbls');
		$this->db->where('tbls.id', $compra->seguradora);
		$seguradora = $this->db->get()->row();
		$data['seguradora'] = $seguradora;

		$this->db->select('v.*, tca.nome_apolice as apolice, tca.link_apolice, tca.nome_apolice');
		$this->db->from('tbl_viajantes v');
		$this->db->join('tbl_cliente_apolice tca', 'tca.id_cliente = v.id', 'left');
		$this->db->where('v.cod_compra', $compra->cod_compra);
		$data['viajantes'] = $this->db->get()->result();

		$this->db->select('tblo.*');
		$this->db->from('tbl_origem tblo');
		$this->db->where('tblo.id', $compra->origem_id);
		$seguradora = $this->db->get()->row()->name_origem;
		$data['origem_promotor'] = $seguradora;

		$this->db->select('tba.*');
		$this->db->from('tbl_alertas_cronjob tba');
		$this->db->where('tba.id_venda', $compra->id);
		$this->db->where('tba.tipo_alerta', 'R');
		$alertRenovacao = $this->db->get()->row();
		$data['alerta_renovacao'] = $alertRenovacao;

		$this->db->select('tba.*');
		$this->db->from('tbl_alertas_cronjob tba');
		$this->db->where('tba.id_venda', $compra->id);
		$this->db->where('tba.tipo_alerta', 'F');
		$alertFeedback = $this->db->get()->row();
		$data['alerta_feedback'] = $alertFeedback;

		//$data['quant_viajantes'] = $this->db->affected_rows($viajantes);

		if ($compra->tipo_pagamento == 'CC') {
			$this->db->select('*');
			$this->db->where('cod_compra', $compra->cod_compra);
			$cartao = $this->db->get('tbl_dados_cartao')->row();
			$data['dados_cartao'] = $cartao;
			$data['metodo_pagamento'] = 'Cartão de crédito';
		}
		if ($compra->tipo_pagamento == 'B') {
			$data['metodo_pagamento'] = 'Boleto';
		}
		if ($compra->tipo_pagamento == 'TB') {
			$data['metodo_pagamento'] = 'Tranferênca Bancária';
		}

		$this->load->view('compras/modal-detalhes-complementos-compra', $data);
	}

	public function salvar_dados_para_autorizacao() {
		if ($this->Compras_model->dados_da_autorizacao()) {
			redirect('compras-efetuadas');
		} 
	}

	public function editar($id = null) {
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');
		$data['cadastra_plano'] = true;

		# Pegando seguradoras para apresentar no cadastro
		$this->db->select('id, nome_seguradora');
		$data['seguradoras'] = $this->db->get('tbl_seguradoras')->result();


		$this->db->select('*');
		$this->db->where('id', $id);
		$this->db->from('tbl_newseguros');
		$plano = $this->db->get()->row();

		$data['plano'] = $plano;

		$data['view'] = 'seguros/cadastrar_seguros';
		$this->load->view('includes/template', $data);
	}

	public function salvar_edicao() {
		$id = $this->input->post('id_plano');
		if ($this->Seguros_model->salvar_edicao($id)) {
			redirect('listar-planos?resp=ed');
		}
	}

	public function excluir($id = null) {
		if ($this->Seguros_model->excluir_plano($id)) {
			redirect('listar-planos?resp=e');
		} else {

		}
	}

}
