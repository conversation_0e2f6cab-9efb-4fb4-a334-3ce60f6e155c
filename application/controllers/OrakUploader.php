<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class OrakUploader extends CI_Controller {

	public function __construct() {
		parent::__construct();
		$this->load->library('session');
		$this->load->helper('okuploader');
		$this->load->helper('session');
	}

	public function doUpload() {
		if (!isset($_REQUEST['path'])) {
			exit;
		}
		$dirback = '../';
		$main_path = $dirback . $_REQUEST["main_path"];
		$thumbnail_path = $dirback . $_REQUEST["thumbnail_path"];
		if ($this->input->get('delete') !== null) {
			unlink($main_path . "/" . $this->input->get('delete'));
			unlink($thumbnail_path . "/" . $this->input->get('delete'));
			if (file_exists($main_path . "/cache/" . $this->input->get('delete')))
				@unlink($main_path . "/cache/" . $this->input->get('delete'));
			if (file_exists($thumbnail_path . "/cache/" . $this->input->get('delete')))
				@unlink($thumbnail_path . "/cache/" . $this->input->get('delete'));
			exit;
		} elseif ($this->input->get('rotate') !== null) {
			rotateImage($this->input->get('rotate'), $main_path, $_GET['degree_lvl']);
			rotateImage($this->input->get('rotate'), $thumbnail_path, $_GET['degree_lvl']);
			echo $this->input->get('rotate');
			exit;
		}
		$filename = time() . '_' . strtolower($this->input->get('filename'));
		$filename = preg_replace("#\\s+#", "_", $filename);
		$cyr = array(
			'ж', 'ч', 'щ', 'ш', 'ю', 'а', 'б', 'в', 'г', 'д', 'е', 'з', 'и', 'й', 'к', 'л', 'м', 'н', 'о', 'п', 'р', 'с', 'т', 'у', 'ф', 'х', 'ц', 'ъ', 'ь', 'я',
			'Ж', 'Ч', 'Щ', 'Ш', 'Ю', 'А', 'Б', 'В', 'Г', 'Д', 'Е', 'З', 'И', 'Й', 'К', 'Л', 'М', 'Н', 'О', 'П', 'Р', 'С', 'Т', 'У', 'Ф', 'Х', 'Ц', 'Ъ', 'Ь', 'Я');
		$lat = array(
			"l", "s",
			'zh', 'ch', 'sht', 'sh', 'yu', 'a', 'b', 'v', 'g', 'd', 'e', 'z', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'r', 's', 't', 'u', 'f', 'h', 'c', 'y', 'x', 'q',
			'Zh', 'Ch', 'Sht', 'Sh', 'Yu', 'A', 'B', 'V', 'G', 'D', 'E', 'Z', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'R', 'S', 'T', 'U', 'F', 'H', 'c', 'Y', 'X', 'Q');
		$filename = str_replace($cyr, $lat, $filename);
		$filename = normalizeChars($filename);
		$bytes = file_put_contents(
			$main_path . '/' . ($filename), file_get_contents('php://input')
		);
		$imgsize = @getimagesize($main_path . '/' . $filename);
		if (!isset($imgsize) || !isset($imgsize['mime']) || !in_array($imgsize['mime'], array('image/jpeg', 'image/png'))) {
			@unlink($main_path . '/' . ($filename));
			exit;
		}
		if ($_REQUEST["resize_to"] > 0) {
			$width = $imgsize[0];
			$height = $imgsize[1];
			if ($width > $_REQUEST["resize_to"])
				createThumbnail($main_path, $filename, $main_path, $_REQUEST["resize_to"], 100);
		}
		if ($bytes > 8) {
			if ((int) $_REQUEST["thumbnail_size"] > 0)
				createThumbnail($main_path, $filename, $thumbnail_path, $_REQUEST["thumbnail_size"], 100);
		} else
			exit;

		if (isset($_REQUEST["watermark"]) && $_REQUEST["watermark"] != '') {
			addWatermark($dirback . $_REQUEST["watermark"], $main_path, $filename);
			addWatermark($dirback . $_REQUEST["watermark"], $thumbnail_path, $filename);
		}

		$crop_to_width = isset($_REQUEST['orakuploader_crop_to_width']) ? (int) $_REQUEST['orakuploader_crop_to_width'] : 0;
		$crop_to_height = isset($_REQUEST['orakuploader_crop_to_height']) ? (int) $_REQUEST['orakuploader_crop_to_height'] : 0;
		$crop_thumb_to_width = isset($_REQUEST['orakuploader_crop_thumb_to_width']) ? (int) $_REQUEST['orakuploader_crop_thumb_to_width'] : 0;
		$crop_thumb_to_height = isset($_REQUEST['orakuploader_crop_thumb_to_height']) ? (int) $_REQUEST['orakuploader_crop_thumb_to_height'] : 0;
		if ($crop_thumb_to_width > 0 && $crop_thumb_to_height > 0) {
			$this->okuploader->crop($crop_thumb_to_width, $crop_thumb_to_height, $main_path . '/' . $filename, $thumbnail_path . '/' . $filename);
		}
		if ($crop_to_width > 0 && $crop_to_height > 0) {
			$this->okuploader->crop($crop_to_width, $crop_to_height, $main_path . '/' . $filename, $main_path . '/' . $filename);
		}

		//guarda imagens em uma sessao
		$_SESSION['imagens_tmp'][] = $filename;
		$this->session->set_userdata('imagens_tmp', $_SESSION['imagens_tmp']);
		echo $filename;
	}

}

?>