<?php

defined('BASEPATH') or exit('No direct script access allowed');

class <PERSON>mbretes extends MY_Controller
{

	public function __construct()
	{
		parent::__construct();
		$this->load->model('Lembretes_model');
	}

	public function index()
	{
		$userId = $this->_getUserId()->id_usuario;

		$data = $this->_getDataDashboard();
		$data['hash'] = $this->_getToken();

		$this->db->select('*');
		$this->db->from('tbl_lembretes');
		$this->db->where('coduser', $userId);
		$this->db->where('is_deleted', '1');
		$this->db->or_where('is_checked', '1');
		$data['alllembretes'] = $this->db->get()->result();

		 //
		 //var_dump($data['alllembretes']);
		// die();

		$data['menuLembretes'] = true;
		$data['view'] = 'agendamento/lembretes';
		$this->load->view('includes/template', $data);
	}


	public function delete($id = null)
	{
		if ($this->Lembretes_model->marcar_como_excluido($id)) {
			redirect('sticky-notes');
		} else {
		}
	}

	public function check($id = null)
	{
		if ($this->Lembretes_model->marcar_como_checked($id)) {
			redirect('sticky-notes');
		} else {
		}
	}



	public function index1()
	{
		$data = $this->_getLanguageDashboard();
		$data += $this->_getDataCookie();
		$data['hash'] = $this->_getToken();

		$data['profissionais'] = $this->_getProfissionais();
		$data['servicos'] = $this->_getServicos();

		$data['schedule'] = true;
		$data['new_scheduling'] = true;

		$data['resp'] = $this->input->get('resp');
		$data += $this->_getRespAction();

		$data['menuNewAgendamento'] = true;
		$data['view'] = 'agendamento/agendar';
		$this->load->view('includes/template', $data);
	}
}
