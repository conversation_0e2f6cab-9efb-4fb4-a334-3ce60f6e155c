<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Profissionais extends MY_Controller
{

	public function __construct()
	{
		parent::__construct();
		//$this->load->model('Agendamentos_model');
	}


	public function index()
	{
		$data = $this->_getDataDashboard();
		$data['hash'] = $this->_getToken();

		$data['profissionais'] = $this->_getProfissionais();

		$data['resp'] = $this->input->get('resp');
		$data += $this->_getRespAction();

		$data['menuProfissionais'] = true;

		$data['view'] = 'gestao/profissionais';
		$this->load->view('includes/template', $data);
	}

}
