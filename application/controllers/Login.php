<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Login extends CI_Controller {

	public function __constrcut() {
		parent::__construct();
	}

	public function index() {

		$data = $this->getLanguageLogin();
		$this->load->library('form_validation');
		$this->load->view('login/login-usuario', $data);
	}


	private function getLanguageLogin(){
		$this->load->helper('language');
		$this->lang->load("app", 'portugues_pt');
		$data['title'] = lang('title');
		$data['username'] = lang('username');
		$data['password'] = lang('password');
		$data['login'] = lang('login');
		$data['forgot_password'] = lang('forgot_password');
		$data['newaccount'] = lang('newaccount');
		$data['nothaveaccount'] = lang('nothaveaccount');
		$data['message_footer'] = lang('message_footer');
		return $data;
	}


	public function logar() {

		$dados = $this->getLanguageLogin();
		$user = addslashes($this->input->post('login'));
		$pass = addslashes($this->input->post('password'));

		$this->load->model('Login_model');
		$login = $this->Login_model->getLogin($user, $pass);
		
		$retorno = $login->erro;

		switch ($retorno) {
			case 0:
				$dadossessao['cod_erro'] = 0;
				$dadossessao['id_usuario'] = $login->id_usuario;
				$dadossessao['nome_usuario'] = $login->nome;
				$dadossessao['email_usuario'] = $login->email;
				$dadossessao['token_usuario'] = $login->token;
				$dadossessao['logado'] = true;

				$this->session->set_userdata($dadossessao);
				
				// Inserindo o log de acesso
				$this->Login_model->inserir_log($login->id_usuario, $login->token, 'Login');

				redirect(base_url());
				break;

			case 1:
				//$this->Login_model->inserir_log($login->id_usuario, $login->token, 'Block');
				$dados['alerta'] = "E-mail ou Senha inválidos.";
				$this->load->view("login/login-usuario", $dados);
				break;

			case 2:
				$this->Login_model->inserir_log($login->id_usuario, $login->token, 'Block');
				$dados['alerta'] = "Sem acesso - Por favor contactar a dbvip! Obrigado.";
				$this->load->view("login/login-usuario", $dados);
				break;
		}
	}

	/*
	 * Metodo que desloga o usuario e faz inserte no log, e depois redireciona para a area de login
	 */

	public function logout() {
		$this->load->model('Login_model');
		$this->Login_model->inserir_log($this->session->userdata('id_usuario'), $this->session->userdata('token_usuario'), 'Logout');

		session_destroy();
		redirect(base_url());
	}	

	public function recuperar() {
		$str = "ABCDEFGHJKMNOPQRSTUXZ23456789";
		$maximo = strlen($str) - 1;
		$senha_gerada = '';
		for ($i = 0; $i < 6; $i++) {
			//$senha_gerada .= $str{mt_rand(0, $maximo)};
		}

		$email = $this->input->post('email');
		// $email = '<EMAIL>';
		if ($email == '') {
			$dados['invalido'] = null;
			$this->load->view("prestador/recuperar-senha", $dados);
		} else {
			$this->db->select('*');
			$this->db->where('email_responsavel', $email);
			$prestador = $this->db->get('prestadores')->row();
			//$quantidade = $this->db->affected_rows($prestador);
			#se existir o email - segue abaixo
			if ($this->db->affected_rows($prestador) > 0) {

				if ($prestador->email_validado) {
					# Atualizando a senha no banco de dados para a gerada
					$this->db->where('email_responsavel', $email);
					$dados = array('senha' => md5($senha_gerada));
					$this->db->update('prestadores', $dados);


					# Enviando o email com a nova senha
					$this->load->model('Emails_Model');
					$this->Emails_Model->recuperar_senha($senha_gerada, $prestador);

					$dados['invalido'] = null;
					$dados['alerta'] = "ATENÇÃO - Favor verificar sua caixa de e-mail com nova senha.";
					$this->load->view("prestador/recuperar-senha", $dados);
				} else {
					$dados['email'] = $prestador->email_responsavel;
					$dados['invalido'] = 1;
					$this->load->view("prestador/recuperar-senha", $dados);
					// var_dump($prestador->nome_atendimento);
					// die();
				}
			} else {
				$dados['invalido'] = null;
				$dados['erro'] = "E-mail não cadastrado no RedeCare";
				$this->load->view("prestador/recuperar-senha", $dados);
			}
		}
	}

	public function alterar_senha_prestador() {
		$id_prestador = $this->session->userdata("idPrestador");
		$nova_senha = $this->input->post('nova_senha');

		$this->db->where('idPrestador', $id_prestador);
		$dados = array('senha' => md5($nova_senha));
		$this->db->update('prestadores', $dados);
		return true;
	}

# Verificando se email é igual ao digitado no alerta caso contrario sobrescreve o email no banco.
# E envia o email de boas vindas novamente
# Gilson 23.02.2017
#=========================================================================================

	public function reenviar_validacao_email() {
		$emailenviado = $this->input->post('emaildigitado');
		$emaildaview = $this->input->post('emaildaview');
		//$emailenviado = '<EMAIL>';
		//$emaildaview ='<EMAIL>';

		$this->load->model('Prestador_Model');
		$this->load->model('Emails_Model');

		if (isset($emailenviado) && !empty($emailenviado)) {
			$consulta = $this->Prestador_Model->verificando_se_existe_email_prestador($emailenviado);
			$consulta1 = $this->Prestador_Model->verificando_se_existe_email_prestador($emaildaview);
			if (isset($consulta) && !empty($consulta)) {
				$this->Emails_Model->boas_vindas_prestador_nao_validou_email($consulta);
			} else {
				$this->Prestador_Model->troca_de_email_por_novo_email($consulta1->idPrestador, $emailenviado);
				$consulta = $this->Prestador_Model->verificando_se_existe_email_prestador($emailenviado);
				$this->Emails_Model->boas_vindas_prestador_nao_validou_email($consulta);
				redirect(base_url('logout'));
			}
		} else {
			redirect(base_url());
		}
	}

}
