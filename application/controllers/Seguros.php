<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Seguros extends MY_Controller {

	public function __construct() {
		parent::__construct();

		# carregando o model para utiliza-lo
		$this->load->model('Seguros_model');
		$this->load->library('Uteis');
	}

	public function index() {
		//$this->load->model('Financeiro_Model');

		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');

		# Pegando usuários ativos
		$this->db->where('status_plano', 1);
		$planos = $this->db->get('tbl_newseguros')->result();
		$data['planos_ativos'] = $this->db->affected_rows($planos);

		$data['view'] = 'dashboard';
		$this->load->view('includes/template', $data);
	}

	public function cadastrar_seguros() {
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');
		$data['cadastra_plano'] = true;

		# Pegando seguradoras para apresentar no cadastro
		$this->db->select('id, nome_seguradora');
		$data['seguradoras'] = $this->db->get('tbl_seguradoras')->result();

		$this->db->select('*');
		$data['tabela_seguradora'] = $this->db->get('tbl_tabela')->result();

		$data['resp'] = $this->input->get('resp');
		$resp = $this->input->get('resp');
		if ($resp == 's') {
			$data['mensagem'] = 'Plano cadastrado com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'e') {
			$data['mensagem'] = 'Plano excluido com sucesso!';
			$data['class'] = 'alert alert-danger alert-dismissible';
		} elseif ($resp == 'ed') {
			$data['mensagem'] = 'Plano editado com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'p') {
			$data['mensagem'] = 'Você não tem autorização para editar Instituição!';
			$data['class'] = 'alert alert-warning alert-dismissible';
		}

		$this->db->select('*');
		$this->db->from('tbl_destinos');
		$data['destinos'] = $this->db->get()->result();

		$data['view'] = 'seguros/cadastrar_seguros';
		$this->load->view('includes/template', $data);
	}

	public function salvar() {
		if ($this->input->post('disponibilidade')) {
			$disponibilidade = implode(',', $this->input->post('disponibilidade'));
			$_POST['disponibilidade'] = $disponibilidade;
		}

		if ($this->Seguros_model->cadastrar_plano()) {
			redirect('listar-planos?resp=s');
		}
	}

	public function listar_seguros() {
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');
		$data['listar_planos'] = true;

		$this->db->select('*');
		$this->db->from('tbl_newseguros');
		$data['seguros'] = $this->db->get()->result();

		$data['resp'] = $this->input->get('resp');
		$resp = $this->input->get('resp');
		if ($resp == 's') {
			$data['mensagem'] = 'Plano cadastrado com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'e') {
			$data['mensagem'] = 'Plano excluido com sucesso!';
			$data['class'] = 'alert alert-danger alert-dismissible';
		} elseif ($resp == 'ed') {
			$data['mensagem'] = 'Plano editado com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'p') {
			$data['mensagem'] = 'Você não tem autorização para editar Instituição!';
			$data['class'] = 'alert alert-warning alert-dismissible';
		}

		$data['view'] = 'seguros/listar_seguros';
		$this->load->view('includes/template', $data);
	}

	# passando dados do seguro para o modal para consulta do usuário
#=====================================================================================================

	public function dados($id = 0) {
		$data = array();
		$this->db->select('*');
		$this->db->where('id', $id);
		$data['dados_do_seguro'] = $this->db->get('tbl_newseguros')->row();

		$this->load->view('seguros/modal-detalhes-seguro', $data);
	}

	# editar instituições cadastradas

	public function editar($id = null) {
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');
		$data['cadastra_plano'] = true;

		# Pegando seguradoras para apresentar no cadastro
		$this->db->select('id, nome_seguradora');
		$data['seguradoras'] = $this->db->get('tbl_seguradoras')->result();

		$this->db->select('*');
		$data['tabela_seguradora'] = $this->db->get('tbl_tabela')->result();


		$this->db->select('*');
		$this->db->where('id', $id);
		$this->db->from('tbl_newseguros');
		$plano = $this->db->get()->row();

		$this->db->select('*');
		$this->db->from('tbl_destinos');
		$data['destinos'] = $this->db->get()->result();

		$data['aux'] = explode(',', $plano->disponibilidade);
		$data['plano'] = $plano;

		$data['view'] = 'seguros/cadastrar_seguros';
		$this->load->view('includes/template', $data);
	}

	public function salvar_edicao() {
		if ($this->input->post('disponibilidade')) {
			$disponibilidade = implode(',', $this->input->post('disponibilidade'));
			$_POST['disponibilidade'] = $disponibilidade;
		}
		$id = $this->input->post('id_plano');
		if ($this->Seguros_model->salvar_edicao($id)) {
			redirect('listar-planos?resp=ed');
		}
	}

	# excluir plano

	public function excluir($id = null) {
		if ($this->Seguros_model->excluir_plano($id)) {
			redirect('listar-planos?resp=e');
		} else {

		}
	}

	public function salvar_dolar() {
		if ($this->Seguros_model->salvar_dolar()) {
			redirect(base_url());
		}
	}

	/**
	 *  GERENCIAMENTO DE DESTINO
	 */
	public function listar_destinos() {
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');
		$data['listar_destinos'] = true;

		$this->db->select('*');
		$this->db->from('tbl_destinos');
		$data['destinos'] = $this->db->get()->result();

		$data['resp'] = $this->input->get('resp');
		$resp = $this->input->get('resp');

		if ($resp == 's') {
			$data['mensagem'] = 'Destino cadastrado com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'e') {
			$data['mensagem'] = 'Destino excluido com sucesso!';
			$data['class'] = 'alert alert-danger alert-dismissible';
		} elseif ($resp == 'ed' || $resp == 'atts') {
			$data['mensagem'] = 'Destino editado com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'p') {
			$data['mensagem'] = 'Você não tem autorização para editar este registro!';
			$data['class'] = 'alert alert-warning alert-dismissible';
		}

		$data['view'] = 'seguros/listar_destinos';
		$this->load->view('includes/template', $data);
	}

	public function cadastrar_destinos($id = null) {
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');
		$data['cadastra_destinos'] = true;

		if (isset($id)) {
			$this->db->select('*')
				->where('id', $id)
				->from('tbl_destinos');
			$data['destino'] = $this->db->get()->row();
		}

		if ($this->input->post('system') == 'true') {
			$this->Seguros_model->setTable('tbl_destinos');
			$post = $this->input->post('form');
			$post['cadastrado_por'] = $this->session->userdata('nome_usuario');
			if (!empty($post['destino'])) {
				if (!empty($post['id'])) {
					// UPDATE FAQ
					$this->Seguros_model->Atualizar($post['id'], $post);
					redirect('listar-destinos?resp=atts', 'refresh');
				} else {
					// CREATE FAQ
					$this->Seguros_model->Cadastrar($post);
					redirect('listar-destinos?resp=s', 'refresh');
				}
			} else {
				redirect('cadastrar-destinos?resp=ep', 'refresh');
			}
		}
		$data['resp'] = $this->input->get('resp');
		$resp = $data['resp'];
		if ($resp == 'ep') {
			$data['mensagem'] = 'Por favor, verifique o preenchimento do formuário!';
			$data['class'] = 'alert alert-danger alert-dismissible';
		} elseif ($resp == 's') {
			$data['mensagem'] = 'Item cadastrado com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'e') {
			$data['mensagem'] = 'Item excluido com sucesso!';
			$data['class'] = 'alert alert-danger alert-dismissible';
		} elseif ($resp == 'ed') {
			$data['mensagem'] = 'Item editado com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'p') {
			$data['mensagem'] = 'Você não tem autorização para editar este Item!';
			$data['class'] = 'alert alert-warning alert-dismissible';
		}

		$data['view'] = 'seguros/cadastrar_destinos';
		$this->load->view('includes/template', $data);
	}

	public function excluir_destinos($id = null) {
		$this->Seguros_model->setTable('tbl_destinos');
		if ($this->Seguros_model->Deletar($id)) {
			redirect('listar-destinos/?resp=e');
		} else {

		}
	}

	/**
	 * GERENCIAMENTO DE SEGURADORAS
	 */
	public function gerenciar_seguradoras() {
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');
		$data['listar_destinos'] = true;

		$this->db->select('*');
		$this->db->from('tbl_seguradoras');
		$data['seguradoras'] = $this->db->get()->result();

		$data['resp'] = $this->input->get('resp');
		$resp = $this->input->get('resp');

		if ($resp == 'ep') {
			$data['mensagem'] = 'Por favor, verifique o preenchimento do formuário!';
			$data['class'] = 'alert alert-danger alert-dismissible';
		} elseif ($resp == 's') {
			$data['mensagem'] = 'Seguradora cadastrada com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'e') {
			$data['mensagem'] = 'Item removido com sucesso!';
			$data['class'] = 'alert alert-danger alert-dismissible';
		} elseif ($resp == 'ed' || $resp == 'atts') {
			$data['mensagem'] = 'Seguradora alterada com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'p') {
			$data['mensagem'] = 'Você não tem autorização para editar este registro!';
			$data['class'] = 'alert alert-warning alert-dismissible';
		}

		$data['view'] = 'seguros/listar_seguradoras';
		$this->load->view('includes/template', $data);
	}

	public function cadastrar_seguradora($id = null) {
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');
		$data['cadastra_seguradoras'] = true;

		if (isset($id)) {
			$this->db->select('*')
				->where('id', $id)
				->from('tbl_seguradoras');
			$data['seguradora'] = $this->db->get()->row();
		}

		if ($this->input->post('system') == 'true') {
			$this->load->library('upload');

			$this->Seguros_model->setTable('tbl_seguradoras');
			$post = $this->input->post('form');

			$img_seguradora = $_FILES['img_seguradora'];
			$aux = explode('.', strtolower(trim($img_seguradora['name'])));
			$extensao = array_pop($aux);
			if (!empty($img_seguradora['name']) && !empty($extensao)) {
				$new_logo = 'CG_' . $this->uteis->slug($post['nome_seguradora']) . '_' . rand() . '.' . $extensao;
				$configuracao1 = array(
					'upload_path' => '../assets/images/seguradoras/',
					'allowed_types' => 'jpg|jpeg|png|gif',
					'file_name' => $new_logo,
					'max_size' => '800'
				);
				$this->upload->initialize($configuracao1);
				if (!$this->upload->do_upload('img_seguradora')) {
					redirect('listar-seguradoras?resp=atts', 'refresh');
				}
				$this->removeLogotipo($post['id']);
				$post['img_seguradora'] = $new_logo;
			}

			$arquivo = $_FILES['termos'];
			if (!empty($arquivo['name'])) {

				$ext = $this->uteis->get_extensao($arquivo['name']);
				// print_r($ext);
				// exit;
				$new_file = 'CG_' . $this->uteis->slug($post['nome_seguradora']) . '_' . rand() . '.' . $ext;
				$configuracao = array(
					'upload_path' => './documentos/condicoes-gerais/',
					'allowed_types' => 'pdf',
					'file_name' => $new_file,
					'max_size' => '20000'
				);
				$this->upload->initialize($configuracao);
				if (!$this->upload->do_upload('termos')) {
					redirect('listar-seguradoras?resp=ep', 'refresh');
				}
				$this->removeCondicoesGerais($post['id']);
				$post['termos'] = $new_file;
			}

			if (!empty($post['nome_seguradora']) && !empty($post['parcelamento'])) {
				if (!empty($post['id'])) {
					// UPDATE FAQ
					$this->Seguros_model->Atualizar($post['id'], $post);
					redirect('listar-seguradoras?resp=atts', 'refresh');
				} else {
					// CREATE FAQ
					$post['cadastrado_por'] = $this->session->userdata('nome_usuario');
					$this->Seguros_model->Cadastrar($post);
					redirect('listar-seguradoras?resp=s', 'refresh');
				}
			} else {
				redirect('cadastrar-seguradora?resp=ep', 'refresh');
			}
		}
		$data['resp'] = $this->input->get('resp');
		$resp = $data['resp'];
		if ($resp == 'ep') {
			$data['mensagem'] = 'Por favor, verifique o preenchimento do formuário!';
			$data['class'] = 'alert alert-danger alert-dismissible';
		} elseif ($resp == 's') {
			$data['mensagem'] = 'Item cadastrado com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'e') {
			$data['mensagem'] = 'Item excluido com sucesso!';
			$data['class'] = 'alert alert-danger alert-dismissible';
		} elseif ($resp == 'ed') {
			$data['mensagem'] = 'Item editado com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'p') {
			$data['mensagem'] = 'Você não tem autorização para editar este Item!';
			$data['class'] = 'alert alert-warning alert-dismissible';
		}

		$data['view'] = 'seguros/cadastrar_seguradoras';
		$this->load->view('includes/template', $data);
	}

	public function excluir_seguradora($id = null) {
		$this->Seguros_model->setTable('tbl_seguradoras');
		if ($this->Seguros_model->Deletar($id)) {
			redirect('listar-seguradoras/?resp=e');
		} else {

		}
	}

	public function removeCondicoesGerais($id) {
		$this->db->select('*');
		$this->db->from('tbl_seguradoras');
		$this->db->where('id', $id);
		$query = $this->db->get();

		if ($query->num_rows() > 0) {
			$row = $query->row_array();
			if (!empty($row['termos'])) {
				@unlink('./documentos/condicoes-gerais/' . $row['termos']);
			}
		}
	}

	public function removeLogotipo($id) {
		$this->db->select('*');
		$this->db->from('tbl_seguradoras');
		$this->db->where('id', $id);
		$query = $this->db->get();

		if ($query->num_rows() > 0) {
			$row = $query->row_array();
			if (!empty($row['img_seguradora'])) {
				@unlink('../assets/images/seguradoras/' . $row['img_seguradora']);
			}
		}
	}


	public function teste(){
		return $_POST(); 
	}



}
