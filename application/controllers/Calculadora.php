<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON>culadora extends MY_Controller {

	public function __construct() {
		parent::__construct();
		$this->load->library('uteis');
		$this->load->model('Emails_model');
		$this->load->model('Calculadora_model');
	}


	public function index()
	{
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');
		$data['enviar_cotacao'] = true;

		$filtro = array();
		$filtro['status'] = 1;
		$data['destinos'] = $this->getAll('tbl_destinos', $filtro);

		$data['view'] = 'calc/buscar_planos';
		$this->load->view('includes/template', $data);
	}




	public function getAll($tabela, $filtro = array()) {
		$query = $this->db->select('*')
			->from($tabela);
		foreach ($filtro as $id => $itemFiltro) {
			$this->db->where($filtro[$id], $itemFiltro);
		}
		return $this->db->get()->result();
	}



	public function enviar_propostas()
	{
		# pAssando valores necessarios para as variaveis
		$destino = $this->input->post('destino');
		$ida = $this->input->post('data_ida');
		$volta = $this->input->post('data_volta');

		$desconto_aplicado = $this->input->post('desconto_aplicado');
		$comdesconto = $this->input->post('desconto');
		$comparcelamento = $this->input->post('parcelamento');

		$passageiros = $this->input->post('passageiros');

		$planos = $this->input->post('plano');

		$dias = $this->entre_datas( date_human_to_mysql($ida),  date_human_to_mysql($volta));

		$nome_cliente = $this->input->post('nome_cliente');
		$email_cliente = $this->input->post('email_cliente');
		
		$email_cc = $this->input->post('email_cc') ? $this->input->post('email_cc') : null;
		$observacao = $this->input->post('observacao_cliente');
		$observacao_interna = $this->input->post('observacao_interna');
		
		$coberturas = [
			'Despesas médicas hospitalares (DMH) por evento',
			'Despesas médicas para doença pré-existente',
			'Desepesas odontológicas',
			'Despesas farmaceuticas',
		
			'Despesas médicas para esporte (Lazer)',
			'Translado médico',
			'Extravio de bagagem',
			'Atraso na entrega da bagagem',
			'Cancelamento de viagem',
			'Atraso de vôo',
			'Interrupção de viagem',
			'Repatriação funerária',
			'Repatriação sanitária',
			'Seguro de morte acidental',
			'Seguro de invalidez permanente por acidente',
			'Assistencia juridica',
			'Acompanhamento de um familiar',
			'Atendimento 24hs em Português'
		];
	

        foreach ($planos as $idplano) {
			
			# Busca por plano e todos os seus calculos
			$value = $this->getSeguros($destino, $ida, $volta, $passageiros, $desconto_aplicado, $idplano)[0];

			$items[$value->nome_plano] = [ 
			   $value->despesas_medicas_e_hospitalares ? $value->despesas_medicas_e_hospitalares : '---',
			   $value->despesas_medicas_para_doenca_pre_existente ? $value->despesas_medicas_para_doenca_pre_existente : '---',
			   $value->despesas_odontologicas ? $value->despesas_odontologicas : '---',
			   $value->despesas_farmaceuticas ? $value->despesas_farmaceuticas : '---',
			   $value->despesas_medicas_para_esportes_lazer ? $value->despesas_medicas_para_esportes_lazer : '---',
			   $value->translado_medico ? $value->translado_medico :'---',
			   $value->extravio_de_bagagem ? $value->extravio_de_bagagem : '---',
			   $value->reembolso_de_gastos_por_demora ? $value->reembolso_de_gastos_por_demora : '---',
			   $value->cancelamento_de_viagem ? $value->cancelamento_de_viagem : '---',
			   $value->recuperacao_de_enfermidade ? $value->recuperacao_de_enfermidade : '---',
			   $value->interrupcao_de_viagem ? $value->interrupcao_de_viagem :'---',
			   $value->reparticao_de_morte ? $value->reparticao_de_morte : '---',
			   $value->reparticao_sanitaria ? $value->reparticao_sanitaria : '---',
			   $value->seguro_de_morte_acidental ? $value->seguro_de_morte_acidental : '---',
			   $value->seguro_de_invalidez_permanente_acidente ? $value->seguro_de_invalidez_permanente_acidente : '---',
			   $value->assistencia_juridica_em_caso_de_acidente ? $value->assistencia_juridica_em_caso_de_acidente : '---',
			   $value->acompanhamento_familiar ? $value->acompanhamento_familiar : '---',
			   $value->assistencia_de_fianca_em_caso_de_acidente ? $value->assistencia_de_fianca_em_caso_de_acidente : '---'
		   ];

		    $seguradoras[$value->nome_plano] = [ 
				$value->nome_seguradora ? $value->nome_seguradora : '---',
			];

			if($comdesconto){
				$valor_com_desconto[$idplano] = [
					$value->valor_total_dos_dias_com_desconto_calculadora
				];
			}

			if($comparcelamento){
				$parcelamento[$idplano] = [
					$value->forma_parcelamento
				];
			}
	
			$valor[$idplano] = [ 
				$value->valor_total_dos_dias

			];
		}	

		// var_dump($seguradoras);
		// // var_dump($desconto);
		// var_dump($parcelamento);
		// dd('fim');

		$itemsKeys = array_keys($items);
		$itemsValues = array_values($items);

		$titulo = '';
        foreach ($itemsKeys as $name) {
			$titulo .= '<th>'.$name;
            $titulo .= '<div style="font-size: 11px; font-weight: 100;">('.$seguradoras[$name][0].')</div>';
			$titulo .= '</th>';
		}
		

		$body = '';
		for ($i = 0; $i < $this->getMaxArraySize($itemsValues); $i++){
		
			if($i % 2 == 0){
				$body .= '<tr style="background-color: #ffddcc;">';
		    } else {
				$body .= '<tr>';
		    }
			
				for ($c = 0; $c < 1; $c++){
					$body .= '<td>'.$coberturas[$i].'</td>';
				}
				for ($y = 0; $y < count($itemsKeys); $y++){
					$body .= '<td>'.$itemsValues[$y][$i].'</td>';
				}
			$body .= '</tr>';
		} 

		$valores_rodape = '';
		foreach ($valor as $key => $val) {
			$valores_rodape .= '<td style="background-color: rgb(195, 199, 202); font-size: 18px;" color="#000000">';
				//var_dump($val[0]);
				$valores_rodape .= '<div style="font-size: 20px;margin-top: 7px;"> R$ '.$val[0].'</div>';

				if($comparcelamento){
					//var_dump($parcelamento[$key][0]);
					$valores_rodape .= '<div style="font-size: 13px;margin-top: 6px;">'.$parcelamento[$key][0].'</div>';
			    }

				if($comdesconto){
					//var_dump($desconto[$key][0]);
					$valores_rodape .= '<div style="font-size: 13px;margin-top: 6px;">À vista: '.$valor_com_desconto[$key][0].'</div>';
				}	
				
			$valores_rodape .= '</td>';
		}
		
		//dd($valores_rodape);


		# Gravar na BD
		$this->gravar_cotacao($destino, $ida, $volta, $passageiros, $nome_cliente, $email_cliente, $email_cc, $observacao, $dias, $planos, $comdesconto, $comparcelamento, $desconto_aplicado, $observacao_interna);


		if ($this->Emails_model->enviar_cotacao($body, $destino, $ida, $volta, $passageiros, $nome_cliente, $email_cliente, $email_cc, $observacao, $titulo, $dias, $valores_rodape)) {

			$data['email_usuario'] = $this->session->userdata('email_usuario');
			$data['codigo_usuario'] = $this->session->userdata('token_usuario');
			$data['nome_usuario'] = $this->session->userdata('nome_usuario');

			$filtro = array();
			$filtro['status'] = 1;
			$data['destinos'] = $this->getAll('tbl_destinos', $filtro);

			$data['view'] = 'calc/buscar_planos';
			$this->load->view('includes/template', $data);
		}
		
	}

	# funcao usada para gera tabela da table
	private function getMaxArraySize($items) {
		$total = 0;
		array_map(function($item) use (&$total) {
			$total = (count($item) > $total) ? count($item) : $total;
		}, $items);
		return $total;
	}


	# funcao usada para gerar cod que sera atribuido a envio de pedido de cotação
	private function getCod(){
		$CI = & get_instance();
		if ($queryid = $CI->db->query("SHOW TABLE STATUS LIKE 'tbl_cotacoes_enviadas';")) {
			$rowid = $queryid->row();
			$id = intval($rowid->Auto_increment);
		}
		return sha1($id);
	}




	private function gravar_cotacao(
		 $destino, $ida, $volta, $passageiros,
		 $nome_cliente, $email_cliente,  $email_cc, $observacao, 
		 $dias, $planos, $comdesconto, $comparcelamento, 
		 $desconto_aplicado, $observacao_interna){

		$cod_cotacao = $this->getCod();

		$descaplicado = null;
		$enviado_desconto = 0;
		$enviado_parcelamento = 0;

		if($comdesconto){
			$enviado_desconto = 1;
			$descaplicado = $desconto_aplicado;
		}
		if($comparcelamento){
			$enviado_parcelamento = 1;
		}

		$dados_cotacao = [
			'cod_cotacao' => $cod_cotacao,
			'nome_cliente' => $nome_cliente,
			'email_cliente' => $email_cliente,
			'email_cc' => $email_cc,
			'quantidade_passageiros' => $passageiros,
			'obs_cliente' => $observacao,
			'enviado_desconto' => $enviado_desconto,
			'porcentagem_desconto' => $descaplicado,
			'enviado_parcelamento' => $enviado_parcelamento,
			'id_vendedor' => $this->session->userdata('token_usuario'),
			'data_ida' => date_human_to_mysql($ida),
			'data_volta' => date_human_to_mysql($volta),
			'dias' => $dias,
			'destino' => $destino,
			'planos' => json_encode($planos),
			'obs_interna' => $observacao_interna
		];

		$this->Calculadora_model->grava_cotacao($dados_cotacao);


		foreach ($planos as $idplano) {
			
		// 	# Busca por plano e todos os seus calculos
			 $value = $this->getSeguros($destino, $ida, $volta, $passageiros, $desconto_aplicado, $idplano)[0];
			
			$this->db->where('cod_cotacao', $cod_cotacao);
			$id_cotacao = $this->db->get('tbl_cotacoes_enviadas')->row()->id;
		
			$dados = [
				'cod_cotacao' => $cod_cotacao,
				'id_seguro' => $value->id,
				'id_cotacao' => $id_cotacao,
				'plano' => $value->nome_plano,
				'seguradora' => $value->nome_seguradora,
				'dolar_aplicado' => $value->cotacao_dolar_usado,
				'viajantes' => $value->quantidade_viajantes,
				'valor_por_viajante' => $value->valor_por_viajante,
				'valor_total' => $value->valor_total_dos_dias,
				'desconto' => $this->input->post('desconto_aplicado'),
				'valor_desconto_aplicado' => $value->desconto_aplicado,
				'valor_com_desconto' => $value->valor_total_dos_dias_com_desconto_calculadora,
				'parcelamento' => $value->forma_parcelamento,
				'idade_maxima' => $value->id_maxima
				
			];

			$this->Calculadora_model->gravar_dados_dessa_cotacao($dados);
		}	

	}






	public function listar_seguros() {

		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');

		$destino =  $this->input->post('destino');
		$ida =  $this->input->post('data_ida');
		$volta =  $this->input->post('data_volta');
		$passageiros =  $this->input->post('passageiros');
		$dias = $this->input->post('dias_admin');

		$desconto_aplicado = $this->input->post('desconto');

		$data['seguros'] = $this->getSeguros($destino, $ida, $volta, $passageiros, $desconto_aplicado);
		$data['total_planos'] = count($data['seguros']);
		$data['filtro'] = (object) array(
			'destino' => $destino,
			'data_ida' => $ida,
			'data_volta' => $volta,
			'passageiros' => $passageiros,
			'desconto_aplicado' => $desconto_aplicado ? $desconto_aplicado : 0,
			'quantidade_dias' => $dias
		);

		$data['view'] = 'calc/list_seguros';
		$this->load->view('includes/template', $data);
	}

	function entre_datas($dataini, $datafim) {
		$data_inicio = new DateTime($dataini);
		$data_fim = new DateTime($datafim);
	
		// Resgata diferença entre as datas
		$dateInterval = $data_inicio->diff($data_fim);
		return $dateInterval->days + 1;
	}
	


	private function getSeguros($destino, $ida, $volta, $passageiros, $desconto = null, $id = null) {

		$pais_destino =  $destino;
		$data_inicio =  $ida;
		$data_termino =  $volta;
		$viajantes =  $passageiros;


		# convertendo a data ida e volta
		$data_ida = date_human_to_mysql($data_inicio);
		$data_volta = date_human_to_mysql($data_termino);

		# Quantidade de dias entre as datas
		$quantidade_de_dias = $this->entre_datas($data_ida, $data_volta);

		# Pegando a cotação do dolar para aplicar nos calculos
		$this->db->select('dolar_dia');
		$this->db->where('id', 1);
		$result = $this->db->get('tbl_configuracoes')->row();

		# Busca dos seguros
		$this->db->select('s.*, (IF (s.relevancia IS NULL OR s.relevancia = 0, 999, s.relevancia) )as relevancia, 
									seg.nome_seguradora, 
									seg.img_seguradora, 
									seg.cotacao_dolar as dolar_seguradora,
									seg.parcelamento'
						);
		$this->db->from('tbl_newseguros s');

		# Verificando se extiste destino
		if (!empty($pais_destino)) {
			$this->db->like('disponibilidade', $pais_destino);
		}
		
		$this->db->order_by('relevancia ASC');
		$this->db->join('tbl_seguradoras seg', 's.seguradora = seg.id');
			  
		if(!empty($id) && $id != null){
			$this->db->where('s.id', $id);
		}

		if($id == null){
			$this->db->where('status_plano', 1);
		}

		//$this->db->limit(6);
		//$this->db->order_by('relev', 'ASC');

		$lista_seguros = $this->db->get()->result();


		# Aqui vamos montar toda a resposta para a lista de seguros
		$planos = array();
		foreach ($lista_seguros as $plano)
		{
			# Passando os valores para variaveis
			$plano->data_ida = $data_inicio;
			$plano->data_volta = $data_termino;
			$plano->pais_destino = $pais_destino;
			$plano->dolar_geral_sistema = $result->dolar_dia;
			$plano->quantidade_dias = $quantidade_de_dias;
			$plano->quantidade_viajantes = $viajantes;

			# Valor por dia do Seguro
			$valorDia = $plano->valor_dia;

			# Dolar do sistema ( Geral )
			$dolar = $result->dolar_dia;

			#parametro cotacao dolar usado
			$plano->cotacao_dolar_usado = $dolar;

			# Definimos o valor por viajante conforme o Dolar, valor do dia e Quantidade de Dias
			$plano->valor_por_viajante = FormatarValor($dolar * $valorDia * $quantidade_de_dias);

			# Verifica se existe cambio para a seguradora do seguro
			if(!empty($plano->dolar_seguradora)){ 
				# Se sim o dolar passa a ser o definido na seguradora
				$dolar = $plano->dolar_seguradora;

				# Definindo novo valor para o dolar com o valor da seguradora
				$plano->cotacao_dolar_usado = $dolar;

				# Valor por viajante é recalculado utilizando o dolar da propria Seguradora
				$plano->valor_por_viajante = FormatarValor($dolar * $valorDia * $quantidade_de_dias);
			}

			# Definindo o valor total dos dias
			$plano->valor_total_dos_dias = FormatarValor(($dolar * $valorDia * $viajantes ) * $quantidade_de_dias);

			$plano->valor_para_ordenar = ($dolar * $valorDia * $viajantes ) * $quantidade_de_dias;
			
			# Se Brasil ou Cruzeiro Não aplicar conversão com o dolar
			if ($pais_destino == 'Brasil' || $pais_destino == 'Cruzeiro') {
				$plano->valor_por_viajante = FormatarValor($valorDia * $quantidade_de_dias);
				
				# Definindo o valor total dos dias
				$plano->valor_total_dos_dias = FormatarValor(($valorDia * $viajantes ) * $quantidade_de_dias);
				$plano->valor_para_ordenar = ($valorDia * $viajantes ) * $quantidade_de_dias;
			}


			# Verificando se existe tabela associada para busca de valores
			if($plano->id_tabela > 0 ){
				
				# Definindo filtros para a tabela de dias
				$this->db->select('*');
				$this->db->where('id_tabela', $plano->id_tabela);
				$tabelas = $this->db->get('tbl_tabela_dias')->result();
				
				# FIltrando qual linha vamos trabalhar da tabela associada
				foreach ($tabelas as $tab){
					# Identificando em qual linha se encaixa o plano
					if($quantidade_de_dias >= $tab->dia_ini && $quantidade_de_dias <= $tab->dia_fim ){
						#Definndo o valor como o valor que esta na linha que extraimos
						$valorDia = $tab->valor_dia;
					}
				}

				# Passando mais valores para variaveis
				$plano->valor_tabela = $valorDia;
				$plano->cotacao_dolar_usado = $dolar;
				$plano->valor_por_viajante = FormatarValor($dolar * $valorDia);
				$plano->valor_total_dos_dias = FormatarValor($dolar * $valorDia * $viajantes);
				$plano->valor_para_ordenar = $dolar * $valorDia * $viajantes;

			}

			$plano->valor_total_dos_dias_com_desconto_calculadora = $plano->valor_total_dos_dias;
			$plano->desconto_aplicado = '0,00';
			if($desconto){
				$valorTotal = $plano->valor_para_ordenar;
				$valor_a_subtrair = $valorTotal / 100 * $desconto;
				$plano->valor_total_dos_dias_com_desconto_calculadora = 'R$ '.FormatarValor($valorTotal - $valor_a_subtrair);
				$plano->desconto_aplicado =  FormatarValor($valor_a_subtrair);
			}

			$plano->valor_parcela =  FormatarValor($plano->valor_para_ordenar / $plano->parcelamento);
			$plano->forma_parcelamento = $plano->parcelamento.' x '.$plano->valor_parcela;
			$plano->forma_parcelamento_email = $plano->parcelamento.' x R$ '.$plano->valor_parcela;


			# Fazer o push de todos os seguros filtrados
			array_push($planos, $plano);

			// Lendo todo o array para verificar a ordem pelo valor total
			foreach ($planos as $key => $row) {
				$total[$key]  = $row->valor_para_ordenar;
			}
			
		
			// Ordena os dados por total crescente, edition crescente.
			array_multisort($total, SORT_ASC,SORT_NUMERIC, $planos);

		}

		// foreach ($planos as $key => $row) {
		// 	var_dump($row->nome_plano, $row->valor_total_dos_dias);
		// var_dump($row->forma_parcelamento);
		// }
		// dd('fim');

		return $planos;
	}


	public function listar_cotacoes() {
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');
		$data['listar_cotacoes'] = true;

		$this->db->select('*');
		$this->db->from('tbl_cotacoes_enviadas');
		
		$data['cotacoes'] = $this->db->get()->result();

		$data['resp'] = $this->input->get('resp');
		$resp = $this->input->get('resp');
		if ($resp == 's') {
			$data['mensagem'] = 'Cotação cadastrada com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'e') {
			$data['mensagem'] = 'Cotação excluido com sucesso!';
			$data['class'] = 'alert alert-danger alert-dismissible';
		} elseif ($resp == 'ed') {
			$data['mensagem'] = 'Cotação editado com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'p') {
			$data['mensagem'] = 'Você não tem autorização para editar Cotação!';
			$data['class'] = 'alert alert-warning alert-dismissible';
		}

		$data['view'] = 'calc/listar_cotacoes_enviadas';
		$this->load->view('includes/template', $data);
	}

	public function excluir_cotacao($id = null) {
		if ($this->Calculadora_model->excluir_cotacao($id)) {
			redirect('listar-cotacoes?resp=e');
		} else {

		}
	}


	public function detalhes_cotacao($id = null) {
		$data = array();
		$this->db->select('*');
		$this->db->where('id_cotacao', $id);
		$compra = $this->db->get('tbl_cotacoes_enviadas_planos')->result();
		$data['planos_cotacoes'] = $compra;

		$this->db->select('*');
		$this->db->from('tbl_cotacoes_enviadas');
		$this->db->where('id', $id);
		
		$data['dados_cotacao'] = $this->db->get()->row();

	//	dd($data['dados_cotacao'] );

		$this->load->view('calc/modal-detalhes-cotacao', $data);
	}


}
