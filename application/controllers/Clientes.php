<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Clientes extends MY_Controller
{

	public function __construct()
	{
		parent::__construct();
		$this->load->model('Clientes_model');
		$this->load->model('Emails_model');
	}


	public function listar_clientes()
	{
		$data = $this->_getDataDashboard();

		$this->db->select('*');
		$this->db->from('tbl_user_clients');
		#$this->db->where('user_active', 1);
		$this->db->order_by('id', 'desc');
		$data['clients'] = $this->db->get()->result();

		$data += $this->_getRespAction();

		$data['menuListCliente'] = true;
		$data['session'] = 'Listar Clientes';
		$data['view'] = 'clientes/listar_clientes';
		$this->load->view('includes/template', $data);
	}

	public function listar_clientes_all()
	{

		$this->db->distinct();
		$this->db->select('c.id as id_seller, c.user_active as active, c.bighub_package as plano, c.email, c.name as name_user, c.nif as nif_user, tc.bank_number, tc.name_company, tc.doc_number as doc_company, c.create_at as registration_date, tc.address, tc.number, tc.complement, tc.postal_code, tc.country, tc.district');
		$this->db->from('tbl_user_clients c');
		$this->db->join('tbl_company as tc', 'c.id = tc.client_id');
		$this->db->order_by('c.id', 'desc');
		$clients = $this->db->get()->result();
	
		foreach ($clients as $key => $value){

			$clients[$key]->timeline = base_url('customer-timelime?user='.$value->id_seller);
		}
		

		# integration date
		echo json_encode($clients);

		die();
	}


	public function impersonate()
	{
		$token = $this->input->post('user');
		$resp = $this->curl_impersonate($token);
		echo json_encode($resp->data->api_token);
	}


	private function curl_impersonate($user_token)
	{
		$url  = "https://app.bighub.store/api/v1/authorize?app_key=$user_token";

		$handle   = curl_init($url);

		$request_headers     = [];
		$request_headers[] = 'Accept: application/json';
		$request_headers[] = 'Content-Type: application/json';

		curl_setopt($handle, CURLOPT_RETURNTRANSFER, TRUE);
		curl_setopt($handle, CURLOPT_HTTPHEADER, $request_headers);
		$response = curl_exec($handle);

		return json_decode($response);
	}


	public function update_status_user()
	{
		$user_token = $this->input->post('user');
		$status = $this->input->post('status');
		if($status == 'active'){
			$status = 1;
			$status_update = 'activated_user';
			$text = 'Usuário ativado manualmente.';
		}else{
			$status = 0;
			$status_update = 'inactivated user';
			$text = 'Usuário desativado manualmente.';
		}

		if ($this->Clientes_model->update_status_user($user_token, $status)) {
			
			$customer = $this->db->where('user_token', $user_token)->get('tbl_user_clients')->row();
			$this->insert_timeline($customer->id, $text, 'A');
			
			if($this->Clientes_model->update_status_user_tbl_subscription($user_token, $status)){
				$resp = [
					'update' => 'success',
					'resp' => $status_update,
					'token' => $user_token
				];
				echo json_encode($resp);
			}
		}
	}


	public function customer_payments()
	{
		$data = $this->_getDataDashboard();

		$this->db->select('ts.user_id, ts.created_at as date_register, ts.package_id, ts.status as status_register, tuc.name, tuc.email, tp.id, tp.amount, tp.total_amount, tp.tax, tp.status as status_payment, tp.created_at as date_status_payment');
		$this->db->from('tbl_subscriptions ts');
		$this->db->join('tbl_payments as tp', 'ts.id = tp.subscription_id');
		$this->db->join('tbl_user_clients as tuc', 'ts.user_id = tuc.id');
		$this->db->order_by('ts.id', 'desc');
		$payments = $this->db->get()->result();
		// echo json_encode($payments);
		// die();
		$data['payments'] = $payments;

		$data['menuFinanceiro'] = true;
		$data['session'] = 'Pagamentos Clientes';
		$data['view'] = 'financeiro/customer_payments';
		$this->load->view('includes/template', $data);
	}


	public function check_payment_customer()
	{
		$data = $this->_getDataDashboard();

		$this->db->select('id, name');
		$all_clients = $this->db->get('tbl_user_clients')->result();
		$data['clientes'] = $all_clients;

		$this->db->select('tp.id as id_registo, ts.user_id, ts.created_at as date_register, ts.package_id, ts.status as status_register, tuc.name, tuc.email, tp.id, tp.amount, tp.total_amount, tp.tax, tp.status as status_payment, tp.created_at as date_status_payment, tp.reference, tpack.name as name_package');
		$this->db->from('tbl_subscriptions ts');
		$this->db->join('tbl_payments as tp', 'ts.id = tp.subscription_id');
		$this->db->join('tbl_user_clients as tuc', 'ts.user_id = tuc.id');
		$this->db->join('tbl_packages as tpack', 'ts.package_id = tpack.id');
		$this->db->order_by('ts.id', 'desc');
		$payments = $this->db->get()->result();
		$data['payments'] = $payments;

		$this->db->select('id, name, price');
		$all_clients = $this->db->get('tbl_packages')->result();
		$data['packages'] = $all_clients;

		$data['view'] = 'clientes/registar_pagamento_cliente';
		$this->load->view('includes/template', $data);
	}


	public function inser_payment_customer()
	{
		$customer_id = $this->input->post('seller');
		$observacao = $this->input->post('observacao');
		$package = $this->input->post('package');
		
		$percentagem = 21;

		if($package == 1){
			$plano = 'Kart';
			$amount = 30.00;
			$valor = 30;
			$resultado = $valor + ($valor * ($percentagem / 100));
			$total_amount = number_format($resultado, 2, '.', ',');
		}

		if($package == 2){
			$plano = 'Rally';
			$amount = 60.00;
			$valor = 60;
			$resultado = $valor + ($valor * ($percentagem / 100));
			$total_amount = number_format($resultado, 2, '.', ',');
		}

		if($package == 3){
			$plano = 'Super Car';
			$amount = 100.00;
			$valor = 100;
			$resultado = $valor + ($valor * ($percentagem / 100));
			$total_amount = number_format($resultado, 2, '.', ',');
		}

		# Adiconando meses a data atual
		$end_date = new DateTime();
		$months = $this->input->post('months');
		$end_date->add(new DateInterval('P'.$months.'M'));

		# Verificando se já existe entrada na tbl_subscription para o customer_id
		$this->db->where('user_id', $customer_id);
		$resp = $this->db->get('tbl_subscriptions')->row();

		# Recebimento do cliente
		$data_subscriptions = [
			'package_id' => $package,
			'status' => 'active',
			'start_date' => date('Y-m-d H:i:s'),
			'end_date' => $end_date->format('Y-m-d H:i:s')
		];
		
		if(isset($resp) && !empty($resp)){
			$data_subscriptions['renewed_at'] = date('Y-m-d H:i:s');

			$this->db->where('id', $resp->id);
			if($this->db->update('tbl_subscriptions', $data_subscriptions)){
				$this->insert_timeline($customer_id, 'Atualização das informações na subscrição. Período da Data: '.date('Y-m-d H:i:s').' a Data final: '.$end_date->format('Y-m-d H:i:s').'- Kurumin', 'A');
			}
		}else{
			$data_subscriptions['user_id'] = $customer_id;

			if($this->db->insert('tbl_subscriptions', $data_subscriptions)){
				$this->insert_timeline($customer_id, 'Inseriu dados na subscrição. Período da Data: '.date('Y-m-d H:i:s').' a Data final: '.$end_date->format('Y-m-d H:i:s').'- Kurumin', 'A');
			}
		}

		# Busvando o ultimo id inserido na tbl_subscription
		$this->db->select_max('id');
		$query = $this->db->where('user_id', $customer_id)->get('tbl_subscriptions');
		$row = $query->row();

		$data_payment = [
			'subscription_id' => $row->id,
			'reference' => 'FREE_TRIAL',
			'amount' => $amount,
			'total_amount' => $total_amount,
			'tax' => 21.00,
			'currency' => 'EUR',
			'status' => 'paid'
		];
		
		if($this->db->insert('tbl_payments', $data_payment)){
			$this->insert_timeline($customer_id, 'Usuário foi marcado como pago, plano '.$plano.', liberação de '.$months.' meses.'.$observacao.' - Kurumin', 'A');
			echo json_encode($data_payment);
		}

		# Update no user, ativar e colocar sessão de preenchimento dos dados da empresa.
		$this->db->where('id', $customer_id);
		$data_active_user = [
			'user_step' => 2,
			'user_active' => 1
		];
		if($this->db->update('tbl_user_clients', $data_active_user)){
			echo json_encode($data_active_user);
			$this->insert_timeline($customer_id, 'Usuaŕio foi ativado e encaminhado para a etapa de preenchimento dos dados da empresa. - Thor ', 'A');
		}

		redirect('check-payment-customer');
	}



	public function customer_timeline()
	{
		$data = $this->_getDataDashboard();

		$customer_id = $this->input->get('user');

		$this->db->where('id', $customer_id);		
		$customer = $this->db->get('tbl_user_clients')->row();


		$this->db->select('tl.*, tl.created_at as data_history, tu.*, tu.nome as name_user, tuc.*, tuc.name as client_name');
		$this->db->join('tbl_usuarios tu', 'tu.id_usuario = tl.usersystem_id');
		$this->db->join('tbl_user_clients tuc', 'tuc.id = tl.customer_id');
		$this->db->where('tl.customer_id', $customer_id);
		$this->db->order_by('tl.id', 'desc'); 
		
		$timeline = $this->db->get('tbl_timeline tl')->result();

		$data['customer_name'] = $customer->name;
		$data['customer_id'] = $customer_id;
		#echo json_encode($timeline);
	

		$data['timeline'] = $timeline;

		$data['menuFinanceiro'] = true;
		$data['session'] = 'Histórico Cliente';
		$data['view'] = 'timeline/customer_timeline';
		$this->load->view('includes/template', $data);
	}


	public function insert_history_timeline(){
		$text = $this->input->post('texttimeline');
		$customer_id = $this->input->post('customer_id');

		if($this->insert_timeline($customer_id, $text)){
			redirect('customer-timelime?user='.$customer_id);
		}
	}

	private function insert_timeline($customer_id, $text, $action = 'M'){
		$data = $this->_getDataDashboard();
		$usersystem = $this->db->where('email',$data['email_usuario'] )->get('tbl_usuarios')->row();
		
		$data_bd = [
			'customer_id' => $customer_id,
			'usersystem_id' => $usersystem->id_usuario,
			'text' => $text,
			'action' => $action
		];

		return $this->db->insert('tbl_timeline', $data_bd);
	}

	

























	public function dados($codcliente = 0)
	{
		$data = array();
		$this->db->select('*');
		$this->db->where('id', $codcliente);
		$data['dados_cliente'] = $this->db->get('tbl_user_clients')->row();

		$this->load->view('clientes/modal-detalhes-cliente', $data);
	}


	public function listar_clientes_pendente_aprovacao()
	{
		$data = $this->_getDataDashboard();

		$this->db->select('*');
		$this->db->from('tbl_user_clients');
		$this->db->where('user_active', 0);
		$this->db->order_by('id', 'desc');
		$data['clients'] = $this->db->get()->result();

		$data += $this->_getRespAction();

		$data['menuListCliente'] = true;
		$data['view'] = 'clientes/listar_clientes_pendentes';
		$this->load->view('includes/template', $data);
	}



	public function approve_client($id_client = null)
	{
		$idUser = $this->_getUserId()->id_usuario;
		if ($this->Clientes_model->approve_user($idUser, $id_client)) {
			redirect('list-clients?resp=s');
		}
	}



	# Produtos
	# =============================================================================================
	public function list_user_products($user_token = null)
	{

		$data = $this->_getDataDashboard();
		$resp = $this->_getDataClient($user_token);

		$user_id = $resp->id;
		$data['data_client'] = $resp;

		if ($user_id> 0) {
	
			$this->db->select('*');
			$this->db->from('tbl_user_products');
			$this->db->where('user_id', $resp->id);
			$products = $this->db->get()->result();

			foreach($products as $key => $prod){
				$label = 'Revisão';
				if($prod->product_session == 1){
					$label = 'Pendente';
				}
				if($prod->product_session == 2){
					$label = 'Á venda';
				}
				if($prod->product_session == 3){
					$label = 'Revisão';
				}
				if($prod->product_session == 4){
					$label = 'Em processamento';
				}
				if($prod->product_session == 9){
					$label = 'Inativo';
				}
				$products[$key]->status_label = $label;
			}	
				
			$data['list_products_client'] = $products;

			# Produtos Ativo
			$data['products_sale'] = $this->db->where('user_id', $user_id)->where('product_session', 2)->where('is_deleted', 0)->get('tbl_user_products')->result();
			
			# Produtos Revisão
			$data['products_revision'] = $this->db->where('user_id', $user_id)->where('product_session', 3)->where('is_deleted', 0)->get('tbl_user_products')->result();

			# Produtos Pendentes
			$data['products_pending'] = $this->db->where('user_id', $user_id)->where('product_session', 1)->where('is_deleted', 0)->get('tbl_user_products')->result();

			#Produtos inativos
			$data['products_inactive'] = $this->db->where('user_id', $user_id)->where('product_session', 9)->where('is_deleted', 0)->get('tbl_user_products')->result();


			$data['menuListPorductsPending'] = true;
			$data['menuProducts'] = true;


			$data['view'] = 'clientes/list_user_products_pending';
			$this->load->view('includes/template', $data);
		} else {
			echo 'user_not_exist, contactar setor desenvolvimento';
			die();
		}
	}

	// public function detalhes_produto($id_product)
	// {
	// 	$id_product = $this->input->get('id_product');

	// 	if ($id_product > 0) {

	// 		$this->db->select('*');
	// 		$this->db->from('tbl_user_products');
	// 		$this->db->where('id', $id_product);
	// 		$product = $this->db->get()->row();

	// 		$this->db->select('pm.price as mkt_price, m.name as mkt_name');
	// 		$this->db->from('tbl_producs_marktplaces pm');
	// 		$this->db->join('tbl_marketplaces m', 'm.id = pm.id_marketplace');
	// 		$this->db->where('pm.cod_mkt_product', $product->cod_mkt);
	// 		$this->db->where('pm.status', 1);
	// 		$marketplaces = $this->db->get()->result();

	// 		// foreach ($marketplaces as $key => $value) {
	// 		// 	$marketplaces[$key]->checkbox = '<input type="checkbox" class="i-checks" name="input[]">';
	// 		// 	$marketplaces[$key]->button = '<a href="google.com.br" class="i-checks">Teste</a>';
	// 		// }

	// 		$success = array(
	// 			'status' => 'success',
	// 			'cod' => 200,
	// 			'product' => $product,
	// 			'marketplaces' => $marketplaces
	// 		);
	// 		echo json_encode($success);
	// 		return;
	// 	} else {
	// 		echo 'product_not_find, contactar setor desenvolvimento';
	// 		die();
	// 	}
	// }


	public function details_user_products()
	{
		$id_product = $this->input->get('id_product');

		if ($id_product > 0) {

			$this->db->select('*');
			$this->db->from('tbl_user_products');
			$this->db->where('id', $id_product);
			$product = $this->db->get()->row();

			$this->db->select('pm.price as mkt_price, m.name as mkt_name');
			$this->db->from('tbl_producs_marktplaces pm');
			$this->db->join('tbl_marketplaces m', 'm.id = pm.id_marketplace');
			$this->db->where('pm.cod_mkt_product', $product->cod_mkt);
			$this->db->where('pm.status', 1);
			$marketplaces = $this->db->get()->result();

			// foreach ($marketplaces as $key => $value) {
			// 	$marketplaces[$key]->checkbox = '<input type="checkbox" class="i-checks" name="input[]">';
			// 	$marketplaces[$key]->button = '<a href="google.com.br" class="i-checks">Teste</a>';
			// }

			$success = array(
				'status' => 'success',
				'cod' => 200,
				'product' => $product,
				'marketplaces' => $marketplaces
			);
			echo json_encode($success);
			return;
		} else {
			echo 'product_not_find, contactar setor desenvolvimento';
			die();
		}
	}


	public function change_status_product()
	{
		$id_product = $this->input->post('id_product');
		$obs = $this->input->post('obs');
		$id_status = $this->input->post('id_status');

		if ($id_product > 0) {

			# update product
			$this->db->where('id', $id_product);

			if($id_status == 9){
				$active = 0;
			}else{
				$active = 1;
			}

			$dados = array(
				'product_session' => $id_status,
				'active' => $active,
				'obs' => $obs
			);

			if ($this->db->update('tbl_user_products', $dados)) {

				# Se atualizou produto, faz o envio do email para o User
				//$this->Emails_model->send_email_product_revision_client($id_product);


				$success = array(
					'status' => 'success',
					'cod' => 200,
					'msg' => 'product_actived',
					'send' => true
				);
				echo json_encode($success);
				return;

			}
		} else {
			echo 'error_product_send_revision, contactar setor desenvolvimento';
			die();
		}
	}




	public function send_product_to_revision()
	{
		$id_product = $this->input->post('id_product');

		if ($id_product > 0) {

			# update product
			$this->db->where('id', $id_product);

			$dados = array(
				'product_session' => 3,
				'obs' => $this->input->post('obs')
			);

			if ($this->db->update('tbl_user_products', $dados)) {

				# Se atualizou produto, faz o envio do email para o User
				//$this->Emails_model->send_email_product_revision_client($id_product);


				$success = array(
					'status' => 'success',
					'cod' => 200,
					'msg' => 'product_send_revision',
					'send' => true
				);
				echo json_encode($success);
				return;

			}
		} else {
			echo 'error_product_send_revision, contactar setor desenvolvimento';
			die();
		}
	}


	public function send_product_to_approval()
	{
		$id_product = $this->input->post('id_product');

		if ($id_product > 0) {

			# update product
			$this->db->where('id', $id_product);

			$dados = array(
				'product_session' => 4,
				'obs' => $this->input->post('obs') ?  $this->input->post('obs') : null,
				'title' => $this->input->post('title_product') ?  $this->input->post('title_product') : null,
				'description' => $this->input->post('description') ?  $this->input->post('description') : null,
				'descriptionSoon' => $this->input->post('descriptionsoon') ?  $this->input->post('descriptionsoon') : null,
			);


			if ($this->db->update('tbl_user_products', $dados)) {

				// $this->db->select('*');
				// $this->db->from('tbl_user_products');
				// $this->db->where('id', $id_product);
				// $product = $this->db->get()->row();

				// $this->db->select('email');
				// $this->db->from('tbl_user_clients');
				// $this->db->where('id', $product->user_id);
				// $user = $this->db->get()->row();

				# Se atualizou produto, faz o envio do email para o User


				$success = array(
					'status' => 'success',
					'cod' => 200,
					'msg' => 'product_send_approval',
					'send' => true
				);
				echo json_encode($success);
				return;

			}
		} else {
			echo 'error_product_send_approval, contactar setor desenvolvimento';
			die();
		}
	}

}
