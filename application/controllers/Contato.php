<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Contato extends MY_Controller {

	public function __construct() {
		parent::__construct();
		$this->load->model('crud');
		$this->crud->setTable('tbl_contato');
	}

	public function listar_contato() {
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');
		$data['listar_contato'] = true;

		$data['contatos'] = $this->crud->getAll('id', 'DESC');

		$data['resp'] = $this->input->get('resp');
		$resp = $data['resp'];
		if ($resp == 'e') {
			$data['mensagem'] = 'Item excluído com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'p') {
			$data['mensagem'] = 'Você não tem autorização para editar este Item!';
			$data['class'] = 'alert alert-danger alert-dismissible';
		}

		$data['view'] = 'contato/listar_contato';
		$this->load->view('includes/template', $data);
	}

	public function visualizar_contato($id) {
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');
		$data['visualizar_contato'] = true;

		$data['contato'] = $this->crud->get('id', $id);
		if ($data['contato']->lida == '0') {
			$upd['lida'] = '1';
			$this->crud->update($upd, $id, 'id');
		}

		$data['view'] = 'contato/visualizar_contato';
		$this->load->view('includes/template', $data);
	}

	public function excluir_contato($id = null) {
		if ($this->crud->delete($id)) {
			redirect('listar-contato/?resp=e');
		} else {

		}
	}

}
