<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Condicoes extends MY_Controller {

	public function __construct() {
		parent::__construct();
	}

	public function cadastrar_condicoes($indice = null) {
		$this->load->model('Condicoes_model');

		$load = $this->Condicoes_model->getLast();
		if (count($load) > 0) {
			$data['id'] = $load[0]->id;
			$data['conteudo'] = $load[0]->conteudo;
		} else {
			$data['id'] = null;
			$data['conteudo'] = NULL;
		}
		$data['email_usuario'] = $this->session->userdata['email_usuario'];
		$data['codigo_usuario'] = $this->session->userdata['token_usuario'];
		$data['nome_usuario'] = $this->session->userdata["nome_usuario"];

		$data['view'] = 'condicoes/cadastrar_condicoes';
		$this->load->view('includes/template', $data);
	}

	public function salvar_conteudo() {
		$this->load->model('Condicoes_model');
		if (!empty($_POST['system']) && $_POST['system'] == 'true') {
			$post = $_POST;
			unset($post['system']);
			$data['conteudo'] = $post['conteudo'];
			if (!empty($post['id'])) {
				// update tbl_empresa
				$this->Condicoes_model->Atualizar($post['id'], $data);
			} else {
				// create tbl_empresa
				$this->Condicoes_model->Inserir($data);
			}
			redirect('Condicoes/cadastrar_condicoes');
		}
	}

}
