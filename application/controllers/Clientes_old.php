<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Clientes extends MY_Controller
{

	public function __construct()
	{
		parent::__construct();
		$this->load->model('Clientes_model');
		$this->load->model('Emails_model');
	}


	public function listar_clientes()
	{
		$data = $this->_getDataDashboard();

		$this->db->select('*');
		$this->db->from('tbl_user_clients');
		$this->db->where('user_active', 1);
		$this->db->order_by('id', 'desc');
		$data['clients'] = $this->db->get()->result();

		$data += $this->_getRespAction();

		$data['menuListCliente'] = true;
		$data['view'] = 'clientes/listar_clientes';
		$this->load->view('includes/template', $data);
	}


	public function dados($codcliente = 0)
	{
		$data = array();
		$this->db->select('*');
		$this->db->where('id', $codcliente);
		$data['dados_cliente'] = $this->db->get('tbl_user_clients')->row();

		$this->load->view('clientes/modal-detalhes-cliente', $data);
	}


	public function listar_clientes_pendente_aprovacao()
	{
		$data = $this->_getDataDashboard();

		$this->db->select('*');
		$this->db->from('tbl_user_clients');
		$this->db->where('user_active', 0);
		$this->db->order_by('id', 'desc');
		$data['clients'] = $this->db->get()->result();

		$data += $this->_getRespAction();

		$data['menuListCliente'] = true;
		$data['view'] = 'clientes/listar_clientes_pendentes';
		$this->load->view('includes/template', $data);
	}



	public function approve_client($id_client = null)
	{
		$idUser = $this->_getUserId()->id_usuario;
		if ($this->Clientes_model->approve_user($idUser, $id_client)) {
			redirect('list-clients?resp=s');
		}
	}



	# Produtos
	# =============================================================================================
	public function list_user_products($user_token = null)
	{

		$data = $this->_getDataDashboard();
		$resp = $this->_getDataClient($user_token);

		$user_id = $resp->id;
		$data['data_client'] = $resp;

		if ($user_id> 0) {
	
			$this->db->select('*');
			$this->db->from('tbl_user_products');
			$this->db->where('user_id', $resp->id);
			$products = $this->db->get()->result();

			foreach($products as $key => $prod){

				if($prod->product_session == 1){
					$label = 'Pendente';
				}
				if($prod->product_session == 2){
					$label = 'Á venda';
				}
				if($prod->product_session == 3){
					$label = 'Revisão';
				}
				if($prod->product_session == 9){
					$label = 'Inativo';
				}
				$products[$key]->status_label = $label;
			}	
				
			$data['list_products_client'] = $products;

			# Produtos Ativo
			$data['products_sale'] = $this->db->where('user_id', $user_id)->where('product_session', 2)->where('is_deleted', 0)->get('tbl_user_products')->result();
			
			# Produtos Revisão
			$data['products_revision'] = $this->db->where('user_id', $user_id)->where('product_session', 3)->where('is_deleted', 0)->get('tbl_user_products')->result();

			# Produtos Pendentes
			$data['products_pending'] = $this->db->where('user_id', $user_id)->where('product_session', 1)->where('is_deleted', 0)->get('tbl_user_products')->result();

			#Produtos inativos
			$data['products_inactive'] = $this->db->where('user_id', $user_id)->where('product_session', 9)->where('is_deleted', 0)->get('tbl_user_products')->result();


			$data['menuListPorductsPending'] = true;
			$data['menuProducts'] = true;


			$data['view'] = 'clientes/list_user_products_pending';
			$this->load->view('includes/template', $data);
		} else {
			echo 'user_not_exist, contactar setor desenvolvimento';
			die();
		}
	}

	// public function detalhes_produto($id_product)
	// {
	// 	$id_product = $this->input->get('id_product');

	// 	if ($id_product > 0) {

	// 		$this->db->select('*');
	// 		$this->db->from('tbl_user_products');
	// 		$this->db->where('id', $id_product);
	// 		$product = $this->db->get()->row();

	// 		$this->db->select('pm.price as mkt_price, m.name as mkt_name');
	// 		$this->db->from('tbl_producs_marktplaces pm');
	// 		$this->db->join('tbl_marketplaces m', 'm.id = pm.id_marketplace');
	// 		$this->db->where('pm.cod_mkt_product', $product->cod_mkt);
	// 		$this->db->where('pm.status', 1);
	// 		$marketplaces = $this->db->get()->result();

	// 		// foreach ($marketplaces as $key => $value) {
	// 		// 	$marketplaces[$key]->checkbox = '<input type="checkbox" class="i-checks" name="input[]">';
	// 		// 	$marketplaces[$key]->button = '<a href="google.com.br" class="i-checks">Teste</a>';
	// 		// }

	// 		$success = array(
	// 			'status' => 'success',
	// 			'cod' => 200,
	// 			'product' => $product,
	// 			'marketplaces' => $marketplaces
	// 		);
	// 		echo json_encode($success);
	// 		return;
	// 	} else {
	// 		echo 'product_not_find, contactar setor desenvolvimento';
	// 		die();
	// 	}
	// }


	public function details_user_products()
	{
		$id_product = $this->input->get('id_product');

		if ($id_product > 0) {

			$this->db->select('*');
			$this->db->from('tbl_user_products');
			$this->db->where('id', $id_product);
			$product = $this->db->get()->row();

			$this->db->select('pm.price as mkt_price, m.name as mkt_name');
			$this->db->from('tbl_producs_marktplaces pm');
			$this->db->join('tbl_marketplaces m', 'm.id = pm.id_marketplace');
			$this->db->where('pm.cod_mkt_product', $product->cod_mkt);
			$this->db->where('pm.status', 1);
			$marketplaces = $this->db->get()->result();

			// foreach ($marketplaces as $key => $value) {
			// 	$marketplaces[$key]->checkbox = '<input type="checkbox" class="i-checks" name="input[]">';
			// 	$marketplaces[$key]->button = '<a href="google.com.br" class="i-checks">Teste</a>';
			// }

			$success = array(
				'status' => 'success',
				'cod' => 200,
				'product' => $product,
				'marketplaces' => $marketplaces
			);
			echo json_encode($success);
			return;
		} else {
			echo 'product_not_find, contactar setor desenvolvimento';
			die();
		}
	}


	public function change_status_product()
	{
		$id_product = $this->input->post('id_product');
		$obs = $this->input->post('obs');
		$id_status = $this->input->post('id_status');

		if ($id_product > 0) {

			# update product
			$this->db->where('id', $id_product);

			if($id_status == 9){
				$active = 0;
			}else{
				$active = 1;
			}

			$dados = array(
				'product_session' => $id_status,
				'active' => $active,
				'obs' => $obs
			);

			if ($this->db->update('tbl_user_products', $dados)) {

				# Se atualizou produto, faz o envio do email para o User
				//$this->Emails_model->send_email_product_revision_client($id_product);


				$success = array(
					'status' => 'success',
					'cod' => 200,
					'msg' => 'product_actived',
					'send' => true
				);
				echo json_encode($success);
				return;

			}
		} else {
			echo 'error_product_send_revision, contactar setor desenvolvimento';
			die();
		}
	}




	public function send_product_to_revision()
	{
		$id_product = $this->input->post('id_product');

		if ($id_product > 0) {

			# update product
			$this->db->where('id', $id_product);

			$dados = array(
				'product_session' => 3,
				'obs' => $this->input->post('obs')
			);

			if ($this->db->update('tbl_user_products', $dados)) {

				# Se atualizou produto, faz o envio do email para o User
				//$this->Emails_model->send_email_product_revision_client($id_product);


				$success = array(
					'status' => 'success',
					'cod' => 200,
					'msg' => 'product_send_revision',
					'send' => true
				);
				echo json_encode($success);
				return;

			}
		} else {
			echo 'error_product_send_revision, contactar setor desenvolvimento';
			die();
		}
	}


	public function send_product_to_approval()
	{
		$id_product = $this->input->post('id_product');

		if ($id_product > 0) {

			# update product
			$this->db->where('id', $id_product);

			$dados = array(
				'product_session' => 2,
				'obs' => $this->input->post('obs') ?  $this->input->post('obs') : null,
				'title' => $this->input->post('title_product') ?  $this->input->post('title_product') : null,
				'description' => $this->input->post('description') ?  $this->input->post('description') : null,
				'descriptionSoon' => $this->input->post('descriptionsoon') ?  $this->input->post('descriptionsoon') : null,
			);


			if ($this->db->update('tbl_user_products', $dados)) {

				// $this->db->select('*');
				// $this->db->from('tbl_user_products');
				// $this->db->where('id', $id_product);
				// $product = $this->db->get()->row();

				// $this->db->select('email');
				// $this->db->from('tbl_user_clients');
				// $this->db->where('id', $product->user_id);
				// $user = $this->db->get()->row();

				# Se atualizou produto, faz o envio do email para o User


				$success = array(
					'status' => 'success',
					'cod' => 200,
					'msg' => 'product_send_approval',
					'send' => true
				);
				echo json_encode($success);
				return;

			}
		} else {
			echo 'error_product_send_approval, contactar setor desenvolvimento';
			die();
		}
	}








	public function cadastrar_cliente()
	{
		$data = $this->_getDataDashboard();
		$data['hash'] = $this->_getToken();

		$data['resp'] = $this->input->get('resp');
		$data += $this->_getRespAction();

		$data['menuNewCliente'] = true;
		$data['view'] = 'clientes/cadastrar_cliente';
		$this->load->view('includes/template', $data);
	}


	public function salvar()
	{
		$idUser = $this->_getUserId()->id_usuario;
		if ($this->Clientes_model->salvar_cliente($idUser)) {
			redirect('list-clients?resp=s');
		}
	}

	public function migracao_clientes()
	{

		$codclient = hash('sha512', date('Y-m-d'));
		$data['id_usuario_system'] = $this->_getUserId()->id_usuario;

		$this->sldc->select('*');
		$this->sldc->from('tbl_clientes');
		$clientes = $this->sldc->get()->result();
		var_dump($clientes);
		die();



		$data['codcliente'] = $codclient;
		$data['data_cadastro'] = date('Y-m-d');
		$data['nascimento'] = date_human_to_mysql($this->input->post('data_nascimento'));
		$data['documento'] = $this->input->post('documento');
		$data['nome'] = $this->input->post('nome');
		$data['email'] = $this->input->post('email');
		$data['fone'] = $this->input->post('telefone');
		$data['genero'] = $this->input->post('genero');
		$data['codpostal'] = $this->input->post('codpostal');
		$data['endereco_morada'] = $this->input->post('endereco_morada');
		$data['numero'] = $this->input->post('numero');
		$data['bairro_freguesia'] = $this->input->post('bairro_freguesia');
		$data['cidade_concelho'] = $this->input->post('cidade_concelho');
		$data['uf_distrito'] = $this->input->post('uf_distrito');
		$data['complemento'] = $this->input->post('complemento');
		$data['status'] = $this->input->post('status_cliente');
		$data['obs_interna'] = $this->input->post('observacoes_internas');

		//return $this->db->insert('tbl_clientes', $data);
	}





	public function historico($codcliente = 0)
	{
		$id_cliente = $this->_getClient($codcliente)->id;

		$this->db->from('view_agendamentos');
		$this->db->where('id_cliente', $id_cliente);
		$historico = $this->db->get()->result();
		$data['historico'] = $historico;

		$cliente = $this->_getClient($codcliente);
		$data['cliente'] = $cliente->nome;


		foreach ($historico as $key => $value) {
			$this->db->select('link_img');
			$this->db->where('cod_agendamento', $value->cod_agendamento);
			$imagens = $this->db->get('tbl_imagens')->result();
			$data['historico'][$key]->imagens = $imagens;
		}

		$this->load->view('clientes/modal-historico', $data);
	}

	public function excluir($codcli = null)
	{
		if ($this->Clientes_model->excluir($codcli)) {
			redirect('list-clients');
		} else {
		}
	}


	public function editar($codcli = null)
	{
		$data = $this->_getDataDashboard();
		$idUser = $this->_getUserId()->id_usuario;


		$this->db->select('*');
		$this->db->where('codcliente', $codcli);
		$this->db->where('id_usuario_system', $idUser);
		$this->db->from('tbl_clientes');
		$cliente = $this->db->get()->row();

		if (!empty($cliente)) {
			$data['cliente'] = $cliente;
			$data['menuNewCliente'] = true;
		} else {
			$data['no_client'] = true;
			//$data['menuNewCliente'] = true;
		}

		$data['view'] = 'clientes/cadastrar_cliente';
		$this->load->view('includes/template', $data);
	}

	public function salvar_edicao()
	{
		$codcli = $this->input->post('cli');
		if ($this->Clientes_model->salvar_edicao($codcli)) {
			redirect('list-clients?resp=ed');
		}
	}


	public function getClienteCpf()
	{
		$cpf = $this->input->post('cpf');

		$this->db->select('c.*');
		$this->db->from('tbl_clientes c');
		$this->db->where('c.cpf', $cpf);
		$result = $this->db->get()->row();

		echo json_encode(array('cliente' => $result));
	}


	public function getClienteEmail()
	{
		$email = $this->input->post('email');

		$this->db->select('c.*');
		$this->db->from('tbl_clientes c');
		$this->db->where('c.email', $email);
		$result = $this->db->get()->row();

		echo json_encode(array('cliente' => $result));
	}

	public function salvar_dolar()
	{
		if ($this->Seguros_model->salvar_dolar()) {
			redirect(base_url());
		}
	}

	public function listar_destinos()
	{
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');
		$data['listar_destinos'] = true;

		$this->db->select('*');
		$this->db->from('tbl_destinos');
		$data['destinos'] = $this->db->get()->result();

		$data['resp'] = $this->input->get('resp');
		$resp = $this->input->get('resp');

		if ($resp == 's') {
			$data['mensagem'] = 'Destino cadastrado com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'e') {
			$data['mensagem'] = 'Destino excluido com sucesso!';
			$data['class'] = 'alert alert-danger alert-dismissible';
		} elseif ($resp == 'ed' || $resp == 'atts') {
			$data['mensagem'] = 'Destino editado com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'p') {
			$data['mensagem'] = 'Você não tem autorização para editar este registro!';
			$data['class'] = 'alert alert-warning alert-dismissible';
		}

		$data['view'] = 'seguros/listar_destinos';
		$this->load->view('includes/template', $data);
	}

	public function cadastrar_destinos($id = null)
	{
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');
		$data['cadastra_destinos'] = true;

		if (isset($id)) {
			$this->db->select('*')
				->where('id', $id)
				->from('tbl_destinos');
			$data['destino'] = $this->db->get()->row();
		}

		if ($this->input->post('system') == 'true') {
			$this->Seguros_model->setTable('tbl_destinos');
			$post = $this->input->post('form');
			$post['cadastrado_por'] = $this->session->userdata('nome_usuario');
			if (!empty($post['destino'])) {
				if (!empty($post['id'])) {
					// UPDATE FAQ
					$this->Seguros_model->Atualizar($post['id'], $post);
					redirect('listar-destinos?resp=atts', 'refresh');
				} else {
					// CREATE FAQ
					$this->Seguros_model->Cadastrar($post);
					redirect('listar-destinos?resp=s', 'refresh');
				}
			} else {
				redirect('cadastrar-destinos?resp=ep', 'refresh');
			}
		}
		$data['resp'] = $this->input->get('resp');
		$resp = $data['resp'];
		if ($resp == 'ep') {
			$data['mensagem'] = 'Por favor, verifique o preenchimento do formuário!';
			$data['class'] = 'alert alert-danger alert-dismissible';
		} elseif ($resp == 's') {
			$data['mensagem'] = 'Item cadastrado com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'e') {
			$data['mensagem'] = 'Item excluido com sucesso!';
			$data['class'] = 'alert alert-danger alert-dismissible';
		} elseif ($resp == 'ed') {
			$data['mensagem'] = 'Item editado com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'p') {
			$data['mensagem'] = 'Você não tem autorização para editar este Item!';
			$data['class'] = 'alert alert-warning alert-dismissible';
		}

		$data['view'] = 'seguros/cadastrar_destinos';
		$this->load->view('includes/template', $data);
	}

	public function excluir_destinos($id = null)
	{
		$this->Seguros_model->setTable('tbl_destinos');
		if ($this->Seguros_model->Deletar($id)) {
			redirect('listar-destinos/?resp=e');
		} else {
		}
	}


	public function gerenciar_seguradoras()
	{
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');
		$data['listar_destinos'] = true;

		$this->db->select('*');
		$this->db->from('tbl_seguradoras');
		$data['seguradoras'] = $this->db->get()->result();

		$data['resp'] = $this->input->get('resp');
		$resp = $this->input->get('resp');

		if ($resp == 'ep') {
			$data['mensagem'] = 'Por favor, verifique o preenchimento do formuário!';
			$data['class'] = 'alert alert-danger alert-dismissible';
		} elseif ($resp == 's') {
			$data['mensagem'] = 'Seguradora cadastrada com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'e') {
			$data['mensagem'] = 'Item removido com sucesso!';
			$data['class'] = 'alert alert-danger alert-dismissible';
		} elseif ($resp == 'ed' || $resp == 'atts') {
			$data['mensagem'] = 'Seguradora alterada com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'p') {
			$data['mensagem'] = 'Você não tem autorização para editar este registro!';
			$data['class'] = 'alert alert-warning alert-dismissible';
		}

		$data['view'] = 'seguros/listar_seguradoras';
		$this->load->view('includes/template', $data);
	}

	public function cadastrar_seguradora($id = null)
	{
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');
		$data['cadastra_seguradoras'] = true;

		if (isset($id)) {
			$this->db->select('*')
				->where('id', $id)
				->from('tbl_seguradoras');
			$data['seguradora'] = $this->db->get()->row();
		}

		if ($this->input->post('system') == 'true') {
			$this->load->library('upload');

			$this->Seguros_model->setTable('tbl_seguradoras');
			$post = $this->input->post('form');

			$img_seguradora = $_FILES['img_seguradora'];
			$aux = explode('.', strtolower(trim($img_seguradora['name'])));
			$extensao = array_pop($aux);
			if (!empty($img_seguradora['name']) && !empty($extensao)) {
				$new_logo = 'CG_' . $this->uteis->slug($post['nome_seguradora']) . '_' . rand() . '.' . $extensao;
				$configuracao1 = array(
					'upload_path' => '../assets/images/seguradoras/',
					'allowed_types' => 'jpg|jpeg|png|gif',
					'file_name' => $new_logo,
					'max_size' => '800'
				);
				$this->upload->initialize($configuracao1);
				if (!$this->upload->do_upload('img_seguradora')) {
					redirect('listar-seguradoras?resp=atts', 'refresh');
				}
				$this->removeLogotipo($post['id']);
				$post['img_seguradora'] = $new_logo;
			}

			$arquivo = $_FILES['termos'];
			if (!empty($arquivo['name'])) {

				$ext = $this->uteis->get_extensao($arquivo['name']);
				// print_r($ext);
				// exit;
				$new_file = 'CG_' . $this->uteis->slug($post['nome_seguradora']) . '_' . rand() . '.' . $ext;
				$configuracao = array(
					'upload_path' => './documentos/condicoes-gerais/',
					'allowed_types' => 'pdf',
					'file_name' => $new_file,
					'max_size' => '20000'
				);
				$this->upload->initialize($configuracao);
				if (!$this->upload->do_upload('termos')) {
					redirect('listar-seguradoras?resp=ep', 'refresh');
				}
				$this->removeCondicoesGerais($post['id']);
				$post['termos'] = $new_file;
			}

			if (!empty($post['nome_seguradora']) && !empty($post['parcelamento'])) {
				if (!empty($post['id'])) {
					// UPDATE FAQ
					$this->Seguros_model->Atualizar($post['id'], $post);
					redirect('listar-seguradoras?resp=atts', 'refresh');
				} else {
					// CREATE FAQ
					$post['cadastrado_por'] = $this->session->userdata('nome_usuario');
					$this->Seguros_model->Cadastrar($post);
					redirect('listar-seguradoras?resp=s', 'refresh');
				}
			} else {
				redirect('cadastrar-seguradora?resp=ep', 'refresh');
			}
		}
		$data['resp'] = $this->input->get('resp');
		$resp = $data['resp'];
		if ($resp == 'ep') {
			$data['mensagem'] = 'Por favor, verifique o preenchimento do formuário!';
			$data['class'] = 'alert alert-danger alert-dismissible';
		} elseif ($resp == 's') {
			$data['mensagem'] = 'Item cadastrado com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'e') {
			$data['mensagem'] = 'Item excluido com sucesso!';
			$data['class'] = 'alert alert-danger alert-dismissible';
		} elseif ($resp == 'ed') {
			$data['mensagem'] = 'Item editado com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'p') {
			$data['mensagem'] = 'Você não tem autorização para editar este Item!';
			$data['class'] = 'alert alert-warning alert-dismissible';
		}

		$data['view'] = 'seguros/cadastrar_seguradoras';
		$this->load->view('includes/template', $data);
	}

	public function excluir_seguradora($id = null)
	{
		$this->Seguros_model->setTable('tbl_seguradoras');
		if ($this->Seguros_model->Deletar($id)) {
			redirect('listar-seguradoras/?resp=e');
		} else {
		}
	}

	public function removeCondicoesGerais($id)
	{
		$this->db->select('*');
		$this->db->from('tbl_seguradoras');
		$this->db->where('id', $id);
		$query = $this->db->get();

		if ($query->num_rows() > 0) {
			$row = $query->row_array();
			if (!empty($row['termos'])) {
				@unlink('./documentos/condicoes-gerais/' . $row['termos']);
			}
		}
	}

	public function removeLogotipo($id)
	{
		$this->db->select('*');
		$this->db->from('tbl_seguradoras');
		$this->db->where('id', $id);
		$query = $this->db->get();

		if ($query->num_rows() > 0) {
			$row = $query->row_array();
			if (!empty($row['img_seguradora'])) {
				@unlink('../assets/images/seguradoras/' . $row['img_seguradora']);
			}
		}
	}


	public function teste()
	{
		return $_POST();
	}
}
