<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Profile extends MY_Controller {
	# Definindo as Variaveis

	protected $email_gerente;
	protected $codigo_gerente;
	protected $posto_por_gerente = array();

	public function __construct() {
		parent::__construct();

		# carregando o model para utiliza-lo
		//$this->load->model('Filtro_Model');
		$this->load->model('Validacoes_model');

		#passando dados da session para variaveis
		$this->id_usuario = $this->session->userdata('id_usuario');
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');

		# Pegando as postos salvos por gerente
		// $this->posto_por_gerente = array();
		// foreach ($this->Filtro_Model->buscando_posto_por_gerente($this->session->userdata('id_gerente')) as $k => $v)
		// 	 array_push($this->posto_por_gerente, $v);
		// # forçando o JSON
		// header('Content-Type: application/json');
		// echo json_encode($resultado);/**/
	}

	public function troca_senha() {
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');

		# caregamento da view
		$data['view'] = 'paginas/profile';
		$this->load->view('includes/template', $data);
	}

	public function validar_troca_senha() {
		$erros = array();

		$senha_atual = $this->input->post('senha_atual');
		if ($senha_atual == false) {
			array_push($erros, array(
				'span' => '.senha-atual-span',
				'mensagem' => 'Por favor preencher campo SENHA ATUAL.'));
		} else {
			if (!$this->Validacoes_model->verificando_se_senha_valida($this->id_usuario, $this->input->post('senha_atual'))) {
				array_push($erros, array(
					'span' => '.senha-atual-span',
					'mensagem' => 'Por favor verificar SENHA ATUAL a mesma não está correta.'));
			}
		}

		$nova_senha = $this->input->post('nova_senha');
		if ($nova_senha == false) {
			array_push($erros, array(
				'span' => '.nova-senha-span',
				'mensagem' => 'Por favor preencher campo NOVA SENHA.'));
		}

		$repetir_nova_senha = $this->input->post('repetir_senha');
		if ($repetir_nova_senha == false) {
			array_push($erros, array(
				'span' => '.repetir-nova-senha-span',
				'mensagem' => 'Por favor preencher campo REPETIR NOVA SENHA.'));
		}

		if ($nova_senha != $repetir_nova_senha) {
			array_push($erros, array(
				'span' => '.senha-diferentes-span',
				'mensagem' => 'Por favor preencher SENHAS IGUAIS.'));
		}


		if (!$erros) {
			echo json_encode(array(
				'estatus' => 1,
				'mensagem' => 'Tudo certo',
				'redirecionar' => FALSE,
				'atualizar' => FALSE));
		} else {
			echo json_encode(array(
				'estatus' => 0,
				'mensagem' => FALSE,
				'redirecionar' => FALSE,
				'atualizar' => FALSE,
				'erros' => $erros));
		}
	}

	public function alterar_senha() {
		$id_usuario = $this->session->userdata("id_usuario");
		$nova_senha = $this->input->post('nova_senha');

		$this->db->where('id_usuario', $id_usuario);
		$dados = array('senha' => md5($nova_senha));
		$this->db->update('tbl_usuarios', $dados);
		return true;
	}

}
