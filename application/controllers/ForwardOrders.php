<?php

ini_set('memory_limit', '2048M'); // Defina o valor de memória desejado, como 1024M para 1GB

defined('BASEPATH') or exit('No direct script access allowed');

class ForwardOrders extends CI_Controller
{

	private $order_bySupport = false;
	
	public function forward_order() {
		try {
			// Marcar que esta é uma solicitação de suporte
			$this->order_bySupport = true;
	
			// Obter o corpo da requisição JSON
			$json = file_get_contents('php://input');
			$data = json_decode($json);
	
			if (!$data) {
				throw new Exception("Invalid JSON data");
			}
	
			// Extrair os dados da requisição
			$channel = $data->channel ?? '';
			$id_usuario = $data->id_usuario ?? null;
			$order_type = $data->order_type ?? '';
			$order_id = $data->order_id ?? null;
			$client_id = $data->client_id ?? null;
	
			// Validar dados obrigatórios
			if (!$order_id || !$client_id) {
				throw new Exception("Missing required parameters");
			}
	
			// Determinar o endpoint baseado no tipo de ordem
			$endpoint = isset($data->order_type) && $data->order_type == "new_forward" 
				? "https://apolus.bighub.store/forward.php"
				: "https://apolo.bighub.store/set-user-to-order";
	
			// Iniciar a chamada curl para o endpoint externo
			$curl = curl_init();
	
			// Preparar os dados para envio ao endpoint externo
			$post_data = [
				'channel' => $channel,
				'id_usuario' => $id_usuario,
				'order_id' => $order_id,
				'client_id' => $client_id
			];
	
			// Adicionar order_type apenas se for do tipo 'new_forward'
			if ($order_type == "new_forward") {
				$post_data['order_type'] = "new_forward";
			}

			// echo json_encode($post_data);
			// die();
	
			// Configurar o curl
			curl_setopt_array($curl, [
				CURLOPT_URL => $endpoint,
				CURLOPT_RETURNTRANSFER => true,
				CURLOPT_ENCODING => '',
				CURLOPT_MAXREDIRS => 10,
				CURLOPT_TIMEOUT => 30,
				CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
				CURLOPT_CUSTOMREQUEST => 'POST',
				CURLOPT_POSTFIELDS => json_encode($post_data),
				CURLOPT_USERAGENT => 'Mozilla/5.0 (compatible; MeuScript/1.0)',
				CURLOPT_HTTPHEADER => [
					'Content-Type: application/json',
					'Accept: application/json',
					'X-Requested-With: XMLHttpRequest',
					'Origin: ' . base_url()
				],
			]);
	
			// Executar a chamada
			$response = curl_exec($curl);
			$err = curl_error($curl);
			$httpcode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
	
			curl_close($curl);
	
			if ($err) {
				throw new Exception("cURL Error: " . $err);
			}
	
			$response_data = json_decode($response);
	
			if (!$response_data && $httpcode != 200) {
				throw new Exception("Invalid response from external API. Status code: " . $httpcode);
			}
	
			log_message('info', 'Order forwarded: ' . json_encode($post_data) . ' to endpoint: ' . $endpoint);
	
			echo json_encode([
				'status' => 'success',
				'message' => 'Order successfully assigned to user',
				'response' => $response_data
			]);
	
		} catch (Exception $e) {
			log_message('error', 'Order forward error: ' . $e->getMessage());
			$this->output->set_status_header(500);
			echo json_encode([
				'status' => 'error',
				'message' => 'Failed to process order: ' . $e->getMessage()
			]);
		}
	}
	
    
}
