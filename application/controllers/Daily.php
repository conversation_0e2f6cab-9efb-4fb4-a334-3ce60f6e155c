<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Daily extends MY_Controller
{

	const XAPIKEY = '7e0913c6-015d-4c60-9c49-3e4837922e53';
	
	public function __construct()
	{
		parent::__construct();
	}

	public function register_daily()
	{
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');
		$data['daily_status'] = true;

		$this->db->select('*');
		$this->db->from('tbl_dailys');
		$this->db->order_by('id', 'DESC'); 
		$this->db->limit(5);
		$data['dailys'] = $this->db->get()->result();


		$data['resp'] = $this->input->get('resp');
		$resp = $this->input->get('resp');
		if ($resp == 's') {
			$data['mensagem'] = 'Daily salva com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible text-white';
		} elseif ($resp == 'e') {
			$data['mensagem'] = 'Daily excluida com sucesso!';
			$data['class'] = 'alert alert-danger alert-dismissible text-white';
		} elseif ($resp == 'p') {
			$data['mensagem'] = 'Você não tem autorização para editar Daily!';
			$data['class'] = 'alert alert-danger alert-dismissible text-white';
		}

		$data['view'] = 'daily/gerir_daily';
		$this->load->view('includes/template', $data);
	}

	public function save_daily()
	{
		if ($this->save()) {
			redirect('daily-scrum?resp=s');
		}
	}

	public function dalete_daily($id = null)
	{
		if ($this->excluir_tabela($id)) {
			redirect('daily-scrum?resp=e');
		} else {
		}
	}


	public function save()
	{
		$data['author_id'] = $this->session->userdata('id_usuario');
		$data['text'] = $this->input->post('text');

		if ($this->db->insert('tbl_dailys', $data)) {
			$text = $data['text'];
			$date = date('d/m/Y');
			$this->create_email($text, $date);
			return true;
		}
	}

	public function excluir_tabela($id)
	{
		$this->db->where('id', $id);
		return $this->db->delete('tbl_dailys');
	}



	# Function send payload para Fila
	public function create_email($text, $date)
	{
		#$emails = '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>';
	    $emails = '<EMAIL>';
		$payload = [
			"template_id" => 21,
			"sender" => "<EMAIL>",
			"receiver" => $emails,  // Usando os e-mails passados como parâmetro
			"is_active" => true,
			"variables" => [
				[
					"key" => "text",
					"value" => $text
				],
				[
					"key" => "date",
					"value" => $date
				]
			]
		];

		// URL da API
		$url_api = 'https://app.bighub.store/api/v2/communications/messages';

		// Inicializa o cURL
		$handle  = curl_init($url_api);

		// Configuração dos headers da requisição
		$request_headers = [];
		$request_headers[] = 'Accept: application/json';
		$request_headers[] = 'Content-Type: application/json';
		$request_headers[] = 'x-api-key:'.self::XAPIKEY;
	
		// Configurações do cURL para uma requisição POST
		curl_setopt($handle, CURLOPT_POST, true);
		curl_setopt($handle, CURLOPT_POSTFIELDS, json_encode($payload));
		curl_setopt($handle, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($handle, CURLOPT_HTTPHEADER, $request_headers);
	
		// Executa a requisição e captura a resposta
		$response = curl_exec($handle);
	
		// Verifica se houve erro na execução da requisição
		if (curl_errno($handle)) {
			echo 'Erro na requisição: ' . curl_error($handle);
			return;
		} 
	
		// Fecha a conexão cURL
		curl_close($handle);
	
		// Decodifica a resposta JSON para um array associativo
		$response_data = json_decode($response, true);

		// var_dump($response_data);
		// die();
	
		// Verifica se o campo "id" está presente na resposta e chama a função de GET
		if (isset($response_data['id'])) {
			// Chama a função de GET com o ID retornado
			$this->send_get_request($response_data['id']);
		} else {
			echo 'Erro: ID não encontrado na resposta da API.';
		}
	}


	public function send_get_request($message_id)
	{
		$url = "https://app.bighub.store/api/v2/communications/messages/{$message_id}/send";

		$handle = curl_init($url);

		$request_headers = [];
		$request_headers[] = 'Accept: application/json';
		$request_headers[] = 'Content-Type: application/json';
		$request_headers[] = 'x-api-key:'.self::XAPIKEY;

		curl_setopt($handle, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($handle, CURLOPT_HTTPHEADER, $request_headers);

		$response = curl_exec($handle);

		if (curl_errno($handle)) {
			echo 'Erro na requisição: ' . curl_error($handle);
		} else {
			echo $response;
		}

		curl_close($handle);
	}
}
