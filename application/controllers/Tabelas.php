<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Tabelas extends MY_Controller {

	public function __construct() {
		parent::__construct();

		# carregando o model para utiliza-lo
		$this->load->model('Tabelas_model');
		$this->load->library('Uteis');
	}


	public function cadastrar_tabela() {
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');
		$data['gerenciar_tabela'] = true;

		$this->db->select('*');
		$this->db->from('tbl_tabela');
		$data['tabelas'] = $this->db->get()->result();


		$data['resp'] = $this->input->get('resp');
		$resp = $this->input->get('resp');
		if ($resp == 's') {
			$data['mensagem'] = 'Tabela salva com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'e') {
			$data['mensagem'] = 'Tabela excluida com sucesso!';
			$data['class'] = 'alert alert-danger alert-dismissible';
		} elseif ($resp == 'ed') {
			$data['mensagem'] = 'Tabela editada com sucesso!';
			$data['class'] = 'alert alert-success alert-dismissible';
		} elseif ($resp == 'p') {
			$data['mensagem'] = 'Você não tem autorização para editar Testemunho!';
			$data['class'] = 'alert alert-warning alert-dismissible';
		}

		$data['view'] = 'tabelas/cadastrar_tabela';
		$this->load->view('includes/template', $data);
	}

	public function salvar() {
		if ($this->Tabelas_model->save()) {
			redirect('gerenciar-tabelas?resp=s');
		}
	}

	// public function salvar_edicao() {
	// 	$id = $this->input->post('id_tabela');
	// 	if ($this->Testemunho_model->save_edit($id)) {
	// 		redirect('listar-testemunhos?resp=ed');
	// 	}
	// }

	// public function listar_testemunhos() {
	// 	$data['email_usuario'] = $this->session->userdata('email_usuario');
	// 	$data['codigo_usuario'] = $this->session->userdata('token_usuario');
	// 	$data['nome_usuario'] = $this->session->userdata('nome_usuario');
	// 	$data['listar_testemunhos'] = true;

	// 	$this->db->select('*');
	// 	$this->db->from('tbl_testemunhos');
	// 	$data['testemunhos'] = $this->db->get()->result();

	// 	$data['resp'] = $this->input->get('resp');
	// 	$resp = $this->input->get('resp');
	// 	if ($resp == 's') {
	// 		$data['mensagem'] = 'Testemunho cadastrado com sucesso!';
	// 		$data['class'] = 'alert alert-success alert-dismissible';
	// 	} elseif ($resp == 'e') {
	// 		$data['mensagem'] = 'Testemunho excluido com sucesso!';
	// 		$data['class'] = 'alert alert-danger alert-dismissible';
	// 	} elseif ($resp == 'ed') {
	// 		$data['mensagem'] = 'Testemunho editado com sucesso!';
	// 		$data['class'] = 'alert alert-success alert-dismissible';
	// 	} elseif ($resp == 'p') {
	// 		$data['mensagem'] = 'Você não tem autorização para editar Testemunho!';
	// 		$data['class'] = 'alert alert-warning alert-dismissible';
	// 	}

	// 	$data['view'] = 'testemunhos/listar_testemunhos';
	// 	$this->load->view('includes/template', $data);
	// }

	public function excluir_tabela($id = null) {
		if ($this->Tabelas_model->excluir_tabela($id)) {
			redirect('gerenciar-tabelas?resp=e');
		} else {

		}
	}

	public function editar_testemunho($id = null) {
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');
		$data['cadastrar_testemunho'] = true;

		$this->db->select('*');
		$this->db->where('id', $id);
		$this->db->from('tbl_testemunhos');
		$data['testemunho'] = $this->db->get()->row();

		$data['view'] = 'testemunhos/cadastrar_testemunho';
		$this->load->view('includes/template', $data);
	}

	


	


}
