<?php

ini_set('memory_limit', '2048M'); // Defina o valor de memória desejado, como 1024M para 1GB

defined('BASEPATH') or exit('No direct script access allowed');

class Invoices extends 	MY_Controller
{

	const URL_API_MARKTPLACES = 'https://prd-mkp.bighub.store';

	public function __construct()
	{
		parent::__construct();
	}


	public function list_invoices()
	{
		$data = $this->_getDataDashboard();

		$this->db->select('tc.name as name_seller, tsp.*');
		$this->db->from('tbl_seller_payment_summary tsp');
		$this->db->join('tbl_user_clients tc', 'tc.id = tsp.id_seller', 'left');

		$data['invoices'] = $this->db->get()->result();

		$data += $this->_getRespAction();

		$data['session'] = 'Invoices';
		$data['view'] = 'invoices/list_invoices';
		$this->load->view('includes/template', $data);
	}



	public function generate_invoice()
	{
		//$data = $this->_getDataDashboard();

		$order_id = $this->input->post('order_id');

		$this->db->select('*');
		$this->db->where('id', $order_id);
		$order = $this->db->get('tbl_orders')->row();

		// echo json_encode($order);
		// die();

		if (isset($order) && isset($order->reference)) {
			$dataOrder = ["id" => $order->reference];
			//return;
		} else {
			echo "Não encontrada '$order_id' | ";
			return;
		}

		// echo 'FIMMM';
		// die();

		# Get channel da order
		$channel = $order->channel;

		#NIF deafults
		$numeroNIF = '999999999';

		// # PCCOMP & PCOMPES & CARREFOUR & PIXMANIA ----------------------------------------------------------
		if ($channel == 'pccomp' || $channel == 'pccompes' || $channel == 'carrefour' || $channel == 'pixmania') {

			if ($channel == 'pccomp' && $order->country == 'ES') {
				$channel = 'pccompes';
			}

			# Vai buscar os dados da order
			$getResponse = $this->curl_get_data_order($dataOrder, $channel);

			if(isset($getResponse)){
				try {

					$customer = $getResponse[0]->response->customer;
					// Tenta acessar os dados normalmente
					if (isset($customer->shipping_address->additional_info)) {
						$additionalInfo = $customer->shipping_address->additional_info;
						// Expressão regular melhorada para capturar o NIF após "Nif:"
						if (preg_match('/Nif:\s*([A-Z0-9]+)/i', $additionalInfo, $matches)) {
							$numeroNIF = $matches[1]; // Usar o grupo capturado
						}
					} 
				} catch (Error $e) {
					// Verifica se o erro é do tipo "Cannot use object of type stdClass as array"
					if (strpos($e->getMessage(), 'stdClass as array') !== false) {
						// Converte a resposta para array e tenta novamente
						$getResponse = $this->convertObjectToArray($getResponse);
			
						$customer = $getResponse[0]['responseFirst']['customer'];
						if (isset($customer['shipping_address']['additional_info'])) {
							$additionalInfo = $customer['shipping_address']['additional_info'];
							// Expressão regular melhorada para capturar o NIF após "Nif:"
							if (preg_match('/Nif:\s*([A-Z0-9]+)/i', $additionalInfo, $matches)) {
								$numeroNIF = $matches[1]; // Usar o grupo capturado
							}
						}
					} else {
						// Re-lança a exceção se for um erro diferente
						throw $e;
					}
				}
			}
		}

		$customer = json_decode($order->customer);
		$products_order = json_decode($order->items);

		$itens = count($products_order);
		if ($itens > 1) {
			var_dump('Order de numero -' . $order_id . ' tem mais de um item');
			return;
		}

		$products = [];
		foreach ($products_order as $prod) {
			$products = [
				'reference' => $prod->ean,
				'title' => $prod->title,
				'price' => $this->remove21porcento($order->total_price)
			];
		}

		if (isset($order) && $order != null) {

			$payload = [
				"user" => $order->user_id,
				"vat" => $numeroNIF,
				"number" => 'BIGHUB-' . $order->id,
				"name" => (isset($customer->billing_address->first_name) && !empty($customer->billing_address->first_name) && $customer->billing_address->first_name != '-') ? 
					$customer->billing_address->first_name . ' ' . $customer->billing_address->last_name : 
					$customer->first_name . ' ' . $customer->last_name,
				"address" => isset($customer->billing_address->street_1) ? 
					($customer->billing_address->street_1 . ' ' . ($customer->billing_address->street_2 ?? '')) : 
					($customer->shipping_address->street_1 . ' ' . ($customer->shipping_address->street_2 ?? '')),
				"city" => isset($customer->billing_address->city) ? 
					$customer->billing_address->city : 
					$customer->shipping_address->city,
				"zipCode" => isset($customer->billing_address->zip_code) ? 
					$customer->billing_address->zip_code : 
					$customer->shipping_address->zip_code,
				"country" => isset($customer->billing_address->country) ? 
					$customer->billing_address->country : 
					$customer->shipping_address->country,
				"products" => [
					$products
				]
			];


			// echo json_encode($payload);
			// die();

			$resp = $this->curl_send_data_moloni($payload);

			$author = [
				'user_name' => $this->session->userdata('nome_usuario'),
				'user_code' => $this->session->userdata('token_usuario'),
				'date_hour' => date('Y-m-d H:m:i'),
				'origin' => 'Thor'
			];

			$tbl_data = [
				'order_id' => $order->id,
				'payload_create_invoice' => json_encode($payload),
				'response_create_invoice' => json_encode($resp),
				'id_invoice' => $resp->id,
				'author' => json_encode($author)
			];

			$this->db->insert('tbl_invoices', $tbl_data);

			$resposta = [
				'status' => 'success',
				'id' => $resp->id,
				'order' => $order_id
			];
			echo json_encode($resposta);
		} else {
			$resposta = [
				'status' => 'error'
			];
			echo json_encode($resposta);
		}
	}

	private function remove21porcento($valor) {
		// Dividir o valor por 1.21
		$resultado_divisao = $valor / 1.21;
	
		// // Multiplicar o resultado por 1.21
		// $resultado_final = $resultado_divisao * 1.21;
	
		// // Arredondar o resultado para 2 casas decimais
		// $resultado_final_arredondado = round($resultado_final, 2);
	
		return $resultado_divisao;
	}

	private function convertObjectToArray($object)
	{
		if (is_object($object)) {
			$object = get_object_vars($object);
		}
		if (is_array($object)) {
			$object = array_map('convertObjectToArray', $object);
		}
		return $object;
	}



	# Chama Curl responsável pelo envio para o marktplace
	private function curl_send_data_moloni($order)
	{
		$url_api = self::URL_API_MARKTPLACES . "/moloni/create";

		$handle   = curl_init($url_api);

		$request_headers     = [];
		$request_headers[] = 'Content-Type: application/json';

		curl_setopt($handle, CURLOPT_POST, true);
		curl_setopt($handle, CURLOPT_POSTFIELDS, json_encode($order));
		curl_setopt($handle, CURLOPT_RETURNTRANSFER, TRUE);
		curl_setopt($handle, CURLOPT_HTTPHEADER, $request_headers);

		$response = curl_exec($handle);
		return json_decode($response);
	}


	private function curl_get_data_order($dataOrder, $channel)
	{
		$url_api = self::URL_API_MARKTPLACES . "/".$channel."/orderId";

		$handle   = curl_init($url_api);

		$request_headers     = [];
		$request_headers[] = 'Content-Type: application/json';

		curl_setopt($handle, CURLOPT_POST, true);
		curl_setopt($handle, CURLOPT_POSTFIELDS, json_encode($dataOrder));
		curl_setopt($handle, CURLOPT_RETURNTRANSFER, TRUE);
		curl_setopt($handle, CURLOPT_HTTPHEADER, $request_headers);

		$response = curl_exec($handle);
		return json_decode($response);
	}


























	public function order_details($id)
	{
		$data = $this->_getDataDashboard();

		$query = $this->db->where('id', $id)->get('tbl_orders');
		$resp = $query->row();

		$address = $resp->customer;
		$data = json_decode($address, true);

		$billingAddress = $data['billing_address'];
		$shippingAddress = $data['shipping_address'];

		// if($billingAddress == null){
		// 	$billingAddress = [
		// 		'first_name' => 'No name',
		// 		'last_name' => 'No last name',
		// 		'phone' => 'No phone',
		// 		'street_1' => ' No address',
		// 		'zip_code' => 'no zip_code',
		// 		'city' => 'No city'
		// 	];
		// }

		// if($shippingAddress == null){
		// 	$shippingAddress = [
		// 		'first_name' => 'No name',
		// 		'last_name' => 'No last name',
		// 		'phone' => 'No phone',
		// 		'street_1' => ' No address',
		// 		'zip_code' => 'no zip_code',
		// 		'city' => 'No city'
		// 	];
		// }
		$img_default = 'https://cdn.bighub.store/image/product-placeholder.png';
		$image = $img_default;

		#$data['attachment'] = $resp->attachment;
		$itens = json_decode($resp->items);
		$total_taxes = 0;

		$state_neuter = ['SHIPPING'];
		$state_positive = ['RECEIVED', 'TO_COLLECT', 'SHIPPED'];
		$state_negative = ['WAITING_ACCEPTANCE', 'WAITING_DEBIT', 'WAITING_DEBIT_PAYMENT', 'CLOSED', 'REFUNDED', 'CANCELED', 'REFUSED', 'AUTO_REFUSED', 'AUTO_CLOSED'];
		$state_class = 'state-neutra';

		foreach ($itens as $key => $item) {

			$taxes = $item->taxes[0]->amount ?? 0;
			$total_taxes = $total_taxes + $taxes;

			if (in_array($item->state, $state_positive)) {
				$state_class = 'state-positive';
			} elseif (in_array($item->state, $state_negative)) {
				$state_class = 'state-negative';
			} elseif (in_array($item->state, $state_neuter)) {
				$state_class = 'state-neuter';
			}

			if (empty($item->image) || $item->image != 'N/A') {
				$image = $item->image;
			}

			$new_itens[$key] = [
				'title' => $item->title,
				'ean' => $item->ean,
				'sku' => $item->sku,
				'image' => $image,
				'image_real' => $item->image,
				'state' => $item->state,
				'class_state' => $state_class,
				'quantity' => $item->quantity,
				'leadtime_to_ship' => $item->leadtime_to_ship,
				'shipping_price' => $item->shipping_price,
				'taxe_amount' => $taxes,
				'commission_fee' => $item->commission_fee,
				'commission_rate_vat' => $item->commission_rate_vat,
				'total_commission' => $item->total_commission,
				'total_price' => $item->total_price,
				'total_price_no_taxe' => round($item->total_price - $taxes, 2),
				'price_unit' => $item->price_unit,
			];
		}

		$data['items'] = json_decode(json_encode($new_itens));
		$data['total_taxes'] = $total_taxes;

		$data['id_order'] = $id;

		# Seller
		$seller = $this->_getSeller(json_decode($resp->user_id));
		$data['seller'] = $seller;
		$seller_country_iban = $seller->bank_number ? substr($seller->bank_number, 0, 2) : 'Error';
		$data['order_data'] = $resp;

		$data['order_data']->text_iva = 'Error';
		$data['order_data']->text_iva_2 = 'Error';
		$data['order_data']->valor_iva_cobrado = 0.00;

		if ($seller_country_iban == 'Error') {
			if ($this->_verificarLetra($seller->nif)) {
				$seller_country_iban = 'ES';
			} else {
				$seller_country_iban = 'PT';
			}
		}

		if ($seller_country_iban == 'PT') {
			$recebido_marketplace = $resp->total_price - $resp->total_commission;
			$porcentagem = 0.23;
			$data['order_data']->text_iva = 'IVA - 23%';
			$data['order_data']->text_iva_2 = 'C/IVA';
			$data['order_data']->valor_iva_cobrado = $recebido_marketplace * $porcentagem;
		}

		if ($seller_country_iban == 'ES') {
			$porcentagem = 0.21;
			$recebido_marketplace = $resp->total_price - $resp->total_commission;
			$data['order_data']->valor_iva_cobrado = $recebido_marketplace * $porcentagem;
			$data['order_data']->text_iva = '+ IVA - 21%';
			$data['order_data']->text_iva_2 = 'S/IVA';
		}

		$data['seller']->iban = $seller->bank_number;
		$data['seller']->seller_country_iban = $seller_country_iban;

		$data['customer'] = json_decode($resp->customer);
		$data['billing_address'] = $billingAddress;
		$data['shipping_address'] = $shippingAddress;
		$data['order_items'] = count(json_decode($resp->items));

		$data['payment'] = json_decode($resp->payment)->type ?? 'Awaiting payment';


		$data['fatura'] = false;
		$data['link_fatura'] = 'Ainda sem anexo.';

		if ($resp->attachment != null && !empty($resp->attachment)) {

			$raw = $resp->attachment;
			$array = json_decode($raw, true);

			$fatura = $array[0]['file_name'];
			$user = $this->db->where('id', $resp->user_id)->get('tbl_user_clients')->row();

			$token = $this->curl_get_jwttoken_user($user->user_token);

			$data['fatura'] = true;
			$data['link_fatura'] = 'https://app.bighub.store/api/v2/orders/' . $resp->id . '/attachments?file_name=' . $fatura . '&token=' . $token->data->api_token;
		}

		// // Converte o array para uma string
		// $responseString = implode(array_map("chr", $response));

		// // Desempacota a string convertida
		// $unpackedData = unpack('H*', $responseString);

		$this->load->view('orders/modal-order-details.php', $data);
	}

	private function curl_get_jwttoken_user($user_token)
	{
		$url  = "https://app.bighub.store/api/v1/authorize?app_key=$user_token";

		$handle   = curl_init($url);

		$request_headers     = [];
		$request_headers[] = 'Accept: application/json';
		$request_headers[] = 'Content-Type: application/json';

		curl_setopt($handle, CURLOPT_RETURNTRANSFER, TRUE);
		curl_setopt($handle, CURLOPT_HTTPHEADER, $request_headers);
		$response = curl_exec($handle);

		return json_decode($response);
	}



	// private function verificarLetra($valor) {
	// 	if (preg_match('/[a-zA-Z]/', $valor)) {
	// 		return true; // Contém uma letra
	// 	} else {
	// 		return false; // Não contém letras
	// 	}
	// }


	public function impersonate()
	{
		$token = $this->input->post('user');
		$resp = $this->curl_impersonate($token);
		echo json_encode($resp->data->api_token);
	}

	private function curl_impersonate($user_token)
	{
		$url  = "https://app.bighub.store/api/v1/authorize?app_key=$user_token";

		$handle   = curl_init($url);

		$request_headers     = [];
		$request_headers[] = 'Accept: application/json';
		$request_headers[] = 'Content-Type: application/json';

		curl_setopt($handle, CURLOPT_RETURNTRANSFER, TRUE);
		curl_setopt($handle, CURLOPT_HTTPHEADER, $request_headers);
		$response = curl_exec($handle);

		return json_decode($response);
	}

	public function update_purchased_order()
	{
		$order_id = $this->input->post('order');
		$action = $this->input->post('action');

		$this->db->where('id', $order_id);
		$data = [
			'purchased' => $action
		];
		if ($this->db->update('tbl_orders', $data)) {
			$resp = [
				'update' => 'success',
				'action' => $action
			];
			echo json_encode($resp);
		}
	}
}
