<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Import extends MY_Controller
{

	public function __construct()
	{
		parent::__construct();
	}

	public function index()
	{
		$foo = $this->getCSV2('clientes.csv');
		var_dump($foo);
		die();
	}

	public function getCSV($name)
	{
		$file = fopen($name, "r");
		$result = array();
		$i = 0;
		while (!feof($file)) :
			if (substr(($result[$i] = fgets($file)), 0, 10) !== ',,,,,,,,') :
				$i++;
			endif;
		endwhile;
		fclose($file);
		return $result;
	}



	public function getCSV2($name)
	{
		$handle = fopen($name, "r");
		$row = 0;

		while ($line = fgetcsv($handle, 1000, ";")) {
			if ($row++ == 0) {
				continue;
			}


		
			$client = array(
				'razao' => $line[0] ? $this->primeira_letra_maiuscula($this->removeaspas($line[0])) : null,
				'cpf' => $line[1] ? $this->removeaspas($line[1]) : null,
				'endereco' => $line[2] ? $this->primeira_letra_maiuscula($this->removeaspas($line[2])) : null,
				'numero' => $line[3] ? $this->removeaspas($line[3]) : null,
				'complemento' => $line[4] ? $this->primeira_letra_maiuscula($this->removeaspas($line[4])) : '',
				'cep' => $line[5] ? $this->removeaspas($line[5]) : null,
				'cidade' => $line[6] ? $this->primeira_letra_maiuscula($this->removeaspas($line[6])) : null,
				'uf' => $line[7] ? $this->removeaspas($line[7]) : null,
				'nascimento' => $line[8] ? date_human_to_mysql($this->removeaspas($line[8])) : null,
				'email' => $line[9] ? $this->removeaspas($line[9]) : null,
				//'fone' => $line[10] ? $this->removeaspas($line[10]) : null,
			);

			// $client = array(
			// 	'razao' => $line[1] ? $this->primeira_letra_maiuscula($line[1]) : null,
			// 	'cpf' => $line[8] ? $line[8] : null,
			// 	'endereco' => $line[1] ? $this->primeira_letra_maiuscula($line[1]) : null,
			// 	'numero' => $line[10] ? $line[10] : null,
			// 	'complemento' => $line[11] ? $this->primeira_letra_maiuscula($line[11]) : '',
			// 	'cep' => $line[4] ? $line[4] : null,
			// 	'cidade' => $line[2] ? $this->primeira_letra_maiuscula($line[2]) : null,
			// 	'uf' => $line[3] ? $line[3] : null,
			// 	'nascimento' => $line[9] ? date_human_to_mysql($line[9]) : null,
			// 	'email' => $line[6] ? $line[6] : null,
			// 	'fone' => $line[5] ? $line[5] : null,
			// );

			// var_dump($client);
			// die();

			//var_dump($line[1]?$line[1]:'null');
			$this->db->insert('tbl_clientes', $client);
		}
		var_dump('foi');
		fclose($handle);
	}


	private function removeaspas($string)
	{
		return str_replace('"', "'", $string);
	}

	private function primeira_letra_maiuscula($palavra)
	{
		return ucwords(strtolower($palavra));
	}
}
