<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Validacoes extends MY_Controller {

	public function __construct() {
        parent::__construct();
        $this->load->model('validacoes_model');
    }

	

	/**
     * Validando os campos da busca
     *
     */
	public function varificando_dados_filtro($etapa = 1) {
		$erros = array();


		$posto = $this->input->post('posto');
		if($posto == false){
			array_push($erros, array(
				'span' => '.posto-span',
				'mensagem' => 'Por favor selecionar um POSTO.'));
		}

		$apresentacao = $this->input->post('apresentacao');
		if($apresentacao == false){
			array_push($erros, array(
				'span' => '.apresentacao-span',
				'mensagem' => 'Por favor selecionar o tipo de consulta.'));
		}

		# Verificando se data é FINAL é maior que a DATA INICIAL, e verificando se a diferença para as DATA é superior a 6 meses ( Para caso de GRAFICOS)
		$inicial = strtotime(date_human_to_mysql($this->input->post('data_inicial')));
		$final   = strtotime(date_human_to_mysql($this->input->post('data_final')));
		
		if($final < $inicial){
			array_push($erros, array(
				'span' => '.data-inicial-span',
				'mensagem' => 'A DATA INICIAL não pode ser MAIOR que a DATA FINAL.'));
		}else{
			$data_inicial = $this->input->post('data_inicial');
			if($data_inicial == false){
				array_push($erros, array(
					'span' => '.data-inicial-span',
					'mensagem' => 'Por favor preencher DATA INICIAL.'));
			}
			$data_final = $this->input->post('data_final');
			if($data_final == false){
				array_push($erros, array(
					'span' => '.data-final-span',
					'mensagem' => 'Por favor preencher DATA FINAL.'));
			}else{
				$valor_tipo = $this->input->post('id_para_controlador');
				$tipos = [1,3,5,12];
				if (in_array($valor_tipo, $tipos)) {
					if( $this->input->post('apresentacao') == 'grafico'){
						$data_inicial = $this->input->post('data_inicial');
						$data_final   = $this->input->post('data_final');
						if(meses_entre_datas($data_inicial, $data_final) > 6){
							array_push($erros, array(
								'span' => '.data-final-span',
								'mensagem' => 'A escolha das DATAS não podem ultrapassar 6 MESES para GRÁFICO'));
						}
					}
				}	
			}
		}

		if (!$erros) {
				echo json_encode(array(
					'estatus' => 1,
					'mensagem' => 'Tudo certo',
					'redirecionar' => FALSE,
					'atualizar' => FALSE));
			} else {
				echo json_encode(array(
					'estatus' => 0,
					'mensagem' => FALSE,
					'redirecionar' => FALSE,
					'atualizar' => FALSE,
					'erros' => $erros));
			}
		}



		

		public function varificando_dados_filtro_sem_data($etapa = 1) {
			$erros = array();
	
			$posto = $this->input->post('posto');
			if($posto == false){
				array_push($erros, array(
					'span' => '.posto-span',
					'mensagem' => 'Por favor selecionar um POSTO.'));
			}
	
			$apresentacao = $this->input->post('apresentacao');
			if($posto == false){
				array_push($erros, array(
					'span' => '.apresentacao-span',
					'mensagem' => 'Por favor selecionar o tipo de consulta.'));
			}
	
	
				if (!$erros) {
					echo json_encode(array(
						'estatus' => 1,
						'mensagem' => 'Tudo certo',
						'redirecionar' => FALSE,
						'atualizar' => FALSE));
				} else {
					echo json_encode(array(
						'estatus' => 0,
						'mensagem' => FALSE,
						'redirecionar' => FALSE,
						'atualizar' => FALSE,
						'erros' => $erros));
				}
			}

   


	}
