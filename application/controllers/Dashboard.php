<?php

use PHPUnit\TextUI\XmlConfiguration\Php;

defined('BASEPATH') or exit('No direct script access allowed');

class Dashboard extends MY_Controller
{
	public function __construct() {
		parent::__construct();

		#$this->dbzap = $this->load->database('db_zap', TRUE);
	}

	public function index()
	{
		$data = $this->_getDataDashboard();
		$data['view'] = 'dashboard/dashboard';

		$data['messages'] = []; #$this->dbzap->select('*')->where('read', 2)->get('tbl_msgs_criticas')->result();
		// echo json_encode($data['messages']);
		// die();


		$this->load->view('includes/template', $data);
	}



	public function dados_para_fechar_atendimento($cod_agendamento = 0)
	{
		$this->db->select('a.data, a.hora, a.confirmacao, a.titulo,  a.open_email, c.nome as cliente, c.fone as fone_cliente, a.id_servico, a.cod_agendamento');
		$this->db->where('cod_agendamento', $cod_agendamento);
		$this->db->join('tbl_clientes c', 'c.id = a.id_cliente', 'left');
		$agendamento = $this->db->get('tbl_agendamentos a')->row();

		$data['dados_atendimento'] = array(
			'cliente' => $agendamento->cliente,
			'cod_atendimento' => $agendamento->cod_agendamento
		);

		$this->load->view('dashboard/modal-dados-atendido', $data);
	}


	public function salvar_dados_do_atendimento1()
	{
		$cod_agendamento = $this->input->post('cod_agendamento');

		// $dados = array(
		// 	'tipo' => $this->input->post('tipo_pagamento'),
		// 	'valor' => $this->input->post('valor_pagamento'),
		// 	'movimento' => 'E',
		// 	'titulo' => $cod_agendamento,
		// 	'data_movimento' => date('Y-m-d')
		// );
		// if ($this->db->insert('tbl_movimento_caixa', $dados)) {

		// 	$this->db->where('cod_agendamento', $cod_agendamento);
		// 	$data = array(
		// 		'atendido' => 1
		// 	);

		// 	$this->db->update('tbl_agendamentos', $data);
		// 	redirect(base_url());
		// }

		$this->db->select('a.*, c.*');
		$this->db->where('a.cod_agendamento', $cod_agendamento);
		$this->db->join('tbl_clientes c', 'c.id = a.id_cliente');
		$cliente = $this->db->get('tbl_agendamentos a')->row();

		# Verifica se existe o diretorio para salvar imagens no ADMIN
		if (!is_dir("./documentos/imagens/clientes/$cliente->codcliente")) {
			@mkdir("./documentos/imagens/clientes/$cliente->codcliente", 0777, $recursive = true);
		}

		$upload_path = './documentos/imagens/clientes/' . $cliente->codcliente;
		$files = $_FILES;

		# validando quantos arquivos estão sendo enviados no file
		$cpt = count($_FILES['files']['name']);

		// var_dump($files);
		// die();

		# Esse rocesso sera feito por imagem
		for ($i = 0; $i < $cpt; $i++) {

			# Pegando nome da imagem e extraindo extenção
			$img_name = $files['files']['name'][$i];
			$ext = pathinfo($img_name, PATHINFO_EXTENSION);

			# Gerando novo nome para a imagem
			$new_name_img = gerar_hash(50);
			$link_da_imagem = 'documentos/imagens/clientes/' . $cliente->codcliente . '/' . $new_name_img . '.' . $ext;

			# Validando informações para o upload
			$configuracao = array(
				'upload_path' => $upload_path,
				'allowed_types' => 'jpg|jpeg|png|gif',
				'file_name' => $new_name_img,
				'remove_spaces' => TRUE,
				'overwrite' => TRUE
			);

			# Inicializando a biblioteca
			$this->load->library('upload');
			$this->upload->initialize($configuracao);

			# Se fez o upload envia para os dados para a base de dados
			if ($this->upload->do_upload('gil')) {
				var_dump('entrou');
				die();

				$name_complete_img = $new_name_img . '.' . $ext;
				$dados_img = array(
					'cod_agendamento' => $cod_agendamento,
					'cod_img' => $new_name_img,
					'name_img' => $name_complete_img,
					'cod_cliente' => $cliente->codcliente,
					'link_img' => $link_da_imagem,
					'data' => date('Y-m-d')
				);

				$this->db->insert('tbl_imagens', $dados_img);
			} else {
				$error = array('error' => $this->upload->display_errors());
				var_dump($error);
				die();
			}
		}

		redirect(base_url());
	}


	public function salvar_dados_do_atendimento()
	{

		$cod_agendamento = $this->input->post('cod_agendamento');

		$this->db->select('a.*, c.*');
		$this->db->where('a.cod_agendamento', $cod_agendamento);
		$this->db->join('tbl_clientes c', 'c.id = a.id_cliente');
		$cliente = $this->db->get('tbl_agendamentos a')->row();

		$dados = array(
			'tipo' => $this->input->post('tipo_pagamento'),
			'valor' => $this->input->post('valor_pagamento'),
			'movimento' => 'E',
			'titulo' => $cod_agendamento,
			'data_movimento' => $cliente->data,
			'data_lancamento' => date('Y-m-d')
		);
		if ($this->db->insert('tbl_movimento_caixa', $dados)) {

			$this->db->where('cod_agendamento', $cod_agendamento);
			$data = array(
				'atendido' => 1
			);
			$this->db->update('tbl_agendamentos', $data);
		}

		# Verifica se existe o diretorio para salvar imagens no ADMIN
		if (!is_dir("./documentos/imagens/clientes/$cliente->codcliente")) {
			@mkdir("./documentos/imagens/clientes/$cliente->codcliente", 0777, $recursive = true);
		}

		$upload_path = './documentos/imagens/clientes/' . $cliente->codcliente;
		$files = $_FILES;

		$data = array();
		$errorUploadType = $statusMsg = '';

		// If files are selected to upload 
		if (!empty($_FILES['files']['name']) && count(array_filter($_FILES['files']['name'])) > 0) {
			$filesCount = count($_FILES['files']['name']);
			for ($i = 0; $i < $filesCount; $i++) {

				$_FILES['file']['name']     = $_FILES['files']['name'][$i];
				$_FILES['file']['type']     = $_FILES['files']['type'][$i];
				$_FILES['file']['tmp_name'] = $_FILES['files']['tmp_name'][$i];
				$_FILES['file']['error']     = $_FILES['files']['error'][$i];
				$_FILES['file']['size']     = $_FILES['files']['size'][$i];

				# Pegando nome da imagem e extraindo extenção
				$img_name = $files['files']['name'][$i];
				$ext = pathinfo($img_name, PATHINFO_EXTENSION);

				# Gerando novo nome para a imagem
				$new_name_img = $_FILES['file']['name']; //gerar_hash(50);

				$link_da_imagem = 'documentos/imagens/clientes/' . $cliente->codcliente . '/' . $new_name_img;

				// File upload configuration 
				$config['upload_path'] = $upload_path;
				$config['allowed_types'] = 'jpg|jpeg|png|gif';
				//$config['max_size']    = '100'; 
				$config['max_width'] = '5000';
				$config['max_height'] = '5000';

				// Load and initialize upload library 
				$this->load->library('upload', $config);
				$this->upload->initialize($config);

				// Upload file to server 
				if ($this->upload->do_upload('file')) {
			
					$name_complete_img = $new_name_img;
					$dados_img = array(
						'cod_agendamento' => $cod_agendamento,
						'cod_img' => $new_name_img,
						'name_img' => $name_complete_img,
						'cod_cliente' => $cliente->codcliente,
						'link_img' => $link_da_imagem,
						'data' => date('Y-m-d')
					);

					$this->db->insert('tbl_imagens', $dados_img);
				} else {
					$errorUploadType .= $_FILES['file']['name'] . ' | ';
				}
			}

			$errorUploadType = !empty($errorUploadType) ? '<br/>File Type Error: ' . trim($errorUploadType, ' | ') : '';
			if (!empty($uploadData)) {
				// Insert files data into the database 
				//$insert = $this->file->insert($uploadData); 

				// Upload status message 
				//$statusMsg = $insert?'Files uploaded successfully!'.$errorUploadType:'Some problem occurred, please try again.'; 
			} else {
				$statusMsg = "Sorry, there was an error uploading your file." . $errorUploadType;
			}
		} else {
			$statusMsg = 'Please select image files to upload.';
		}
		redirect(base_url());
	}




	private function set_upload_options()
	{
		//upload an image options
		$config = array();
		$config['upload_path'] = './resources/images/products/';
		$config['allowed_types'] = 'gif|jpg|png';
		$config['max_size']      = '0';
		$config['overwrite']     = FALSE;

		return $config;
	}

	# Tipo 1 Cancelado pelo salão
	# Tipo 2 Cancelado pelo Cliente
	# Tipo 5 não deu nenhuma satisfação e não atendeu telefone.

	public function cancelamento_cliente($id_agendamento)
	{
		$this->db->where('id', $id_agendamento);
		$data = array(
			'cancelamento' => 1,
			'tipo_cancelamento' => 2,
			'data_cancelamento' => date('Y-m-d')
		);

		$this->db->update('tbl_agendamentos', $data);
		redirect(base_url());
	}



	public function nao_compareceu($cod_agendamento)
	{
		$this->db->where('cod_agendamento', $cod_agendamento);
		$data = array(
			'cancelamento' => 1,
			'tipo_cancelamento' => 5,
			'data_cancelamento' => date('Y-m-d')
		);

		var_dump($data);
		die();
		$this->db->update('tbl_agendamentos', $data);
		redirect(base_url());
	}






	public function index_old()
	{
		$data = $this->_getDataDashboard();
		//$this->load->model('Financeiro_Model');

		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');

		// # Pegando planos cadastrados
		// $this->db->where('status_plano', 1);
		// $planos = $this->db->get('tbl_newseguros')->result();
		// $data['planos_cadastrados'] = $this->db->affected_rows($planos);

		// # Pegando seguros ativos
		// $this->db->where('status_pagamento', 'A');
		// $planos = $this->db->get('tbl_solicitacao_de_compra')->result();
		// $data['seguros_ativos'] = $this->db->affected_rows($planos);

		// # Pegando solicitações de compra
		// $this->db->where('data_autorizacao_compra', NULL);
		// $solicitadoes_de_ = $this->db->get('tbl_solicitacao_de_compra')->result();
		// $data['solicitacoes_de_compra'] = $this->db->affected_rows($solicitadoes_de_);

		// # Pegando dolar dia
		// $this->db->where('id', 1);
		// $encontrado = $this->db->get('tbl_configuracoes')->row();
		// $data['dolar_dia'] = $encontrado->dolar_dia;

		// $this->db->select('*')
		// 	->from('tbl_solicitacao_de_compra')
		// 	->order_by('data_compra', 'DESC')
		// 	->limit(10, 0);
		// $data['compras'] = $this->db->get()->result();

		$data['menuDashboard'] = true;
		$data['view'] = 'dashboard';
		$this->load->view('includes/template', $data);
	}


	public function filtro()
	{
		# carregando o model para utiliza-lo
		$this->load->model('Filtro_Model');

		#passando dados da session para variaveis
		$data['email_gerente'] = $this->session->userdata('email_gerente');
		$data['codigo_gerente'] = $this->session->userdata('token_gerente');

		//Pegando as postos salvos por gerente
		$data['posto_por_gerente'] = array();
		foreach ($this->Filtro_Model->buscando_posto_por_gerente($this->session->userdata('id_gerente')) as $k => $v)
			array_push($data['posto_por_gerente'], $v);

		$data['view'] = 'paginas/filtro';
		$this->load->view('includes/template', $data);
	}
}
