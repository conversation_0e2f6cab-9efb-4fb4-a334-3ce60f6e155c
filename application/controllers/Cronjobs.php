<?php

defined('BASEPATH') or exit('No direct script access allowed');

class <PERSON><PERSON><PERSON><PERSON> extends CI_Controller
{

	public function __construct()
	{
		parent::__construct();
		$this->load->model('Cronjob_model');
	}

	######################################################################### DISTRIBUIÇÃO #################################################################

	# Função para verificar todos os produtos aprovados, e importar para tabela onde será feito o filtro dos marketplaces
	public function enviar_produtos_aprovaddos_para_esteira_geral(){
		# Criar uma tabela para receber todos os produtos aprovados e seus marktplaces

		

		
	}


	# Função que vai verificar para qual esteira vai o produto
	public function enviar_para_esteira_de_marketplaces($marketplace){

		# Pega o produto


		# Pega os marketplaces desse produto


		# Foreach em todos os marketplaces, conforme a resposta será enviado para a esteira dos marketplaces


	}



	######################################################################### ESTEIRAS #####################################################################

	private function esteira_worten_pt(){

		# Criar uma tabela com toda a estrutura para receber o produto com seus dados - ultima etapa antes do envio para o marketplace.


		# Ao receber o produto com o valor ele vai ser colocado na esteira e aguardar a cron de envio.


	}

	private function esteira_worten_es(){


	}


	private function esteira_amazon(){

	}





	######################################################################### ENVIOS #####################################################################

	# Função que faz o envio para o marktplaces
	public function envio_worten_pt(){
		# Vai pecorrer a tabela criada e fazer o envio para o marketplace

	}


	public function envio_worten_es(){


	}

	public function amazon(){
	// {"user": "1",
	// "ean": "7332543175536",
	// "price": "60",
	// "quantity": "100",
	// "bh_sku": "SKU_TEST_999",

	// "leadtime_to_ship": "15",
	// "max_order_quantity": "3",
	// "min_order_quantity": "1",
	// "min_quantity_alert": "2",
	// "package_quantity": "2",
	// "available_started": "2022-11-01T22:00:00Z",
	// "available_ended": "2022-12-31T22:00:00Z",
	// "description": "Descrição",
	// "internal_description": "Descrição interna",
	// "discount_start_date": "2022-11-10T22:00:00Z",
	// "discount_end_date": "2022-12-29T22:00:00Z",
	// "discount_price": "50",
	// "discount_ranges_price": "45",
	// "discount_ranges_quantity_threshold": "6"}

	}


	######################################################################### ENVIOS #####################################################################















	// public function envio_de_emails()
	// {
	// 	echo "Begin \n";
	// 	$this->db->select('*');
	// 	$this->db->where('status_email', 0);
	// 	$this->db->from('tbl_alertas_cronjob');
	// 	$alertas = $this->db->get()->result();

	// 	foreach ($alertas as $a) {
	// 		if($a->data_alerta === date('Y-m-d')){
	// 			if ($a->tipo_alerta == 'R') {
	// 				$this->Cronjob_model->email_alerta_fim_seguro($a->nome_cliente, $a->email_cliente);
	// 			} elseif ($a->tipo_alerta == 'F') {
	// 				$this->Cronjob_model->email_feedback($a->nome_cliente, $a->email_cliente);
	// 			}
	// 			$this->email_enviado($a->id);
	// 			echo $a->nome_cliente;
	// 		}
			
	// 	}
	// 	echo 'End';

	// }


	// private function email_enviado($id)
	// {
	// 	$this->db->where('id', $id);
	// 	$dados = array(
	// 		'status_email' => '1',
	// 		'data_hora_enviado' =>  date('Y-m-d H:i:s')
	// 	);
	// 	return $this->db->update('tbl_alertas_cronjob', $dados);
	// }
}
