<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Config extends MY_Controller {

	public function __construct() {
		parent::__construct();
		$this->load->model('Config_model');
	}


	public function index()
	{
		$data = $this->_getDataDashboard();
		$data['hash'] = $this->_getToken();

		$data['profissionais'] = $this->_getProfissionais();

		$data['resp'] = $this->input->get('resp');
		$data += $this->_getRespAction();

		$data['menuConfigSystem'] = true;

		$data['view'] = 'gestao/configuracoes';
		$this->load->view('includes/template', $data);
	}


	public function cadastrar_logotipo() {
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');

		# Pegando usuários ativos
		$config = $this->db->get('tbl_site_config')->result();

		$data['config'] = $config;
		$data['view'] = 'config/cadastrar-logotipo';
		$this->load->view('includes/template', $data);
	}

	public function informacoes_de_contato() {
		if (isset($this->input->post()['system']) && $this->input->post()['system'] == 'true') {
			$post = $this->input->post();
			unset($post['system']);
			if (!empty($post['codigo'])) {
				$this->Config_model->update($post);
			} else {
				$this->Config_model->create($post);
			}
		}
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');

		# Pegando usuários ativos
		$config = $this->db->get('tbl_site_config')->result();

		$data['config'] = $config;
		$data['view'] = 'config/informacoes-de-contato';
		$this->load->view('includes/template', $data);
	}

	public function redes_sociais() {
		if (isset($this->input->post()['system']) && $this->input->post()['system'] == 'true') {
			$post = $this->input->post()['red'];
			unset($post['system']);
			$this->Config_model->truncateAndCrateRedesSociais($post);
			redirect('configs/redes-sociais');
		}

		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');

		# Pegando usuários ativos
		$config = $this->db->get('tbl_site_config')->result();

		$data['config'] = $config;
		$data['view'] = 'config/redes-sociais';

		$data['redes'] = $this->Config_model->findAllRedeSocial('true', array(), 'a.codigo', 'ASC');
		$data['icones'] = $this->Config_model->findAllIcone(true, array(), 'a.categoria', 'asc');

		$this->load->view('includes/template', $data);
	}

	public function jsFindAllIcones() {
		$arrIcones = $this->Config_model->findAllIcone(true, array(), 'a.categoria', 'asc');
		$_html = '';
		if (count($arrIcones) > 0) {
			$_html = '<option value="">-- Selecione</option>';
			foreach ($arrIcones as $icone) {
				$_html .= '<option value="' . $icone->icone . '">' . $icone->nome . '</option>';
			}
		}
		echo $_html;
	}

	public function webservice($param) {
		if ($param) {
			$xml = simplexml_load_file('http://viacep.com.br/ws/' . $param . '/xml/');
			if ($xml) {
				$rtn = (string) $xml->logradouro . '|' . (string) $xml->bairro . '|' . (string) $xml->localidade . '|' . (string) $xml->uf;
			}
		}
		echo $rtn;
	}

	public function configuracoes_smtp() {
		print_r('Configurações SMTP');
		exit;
	}

}
