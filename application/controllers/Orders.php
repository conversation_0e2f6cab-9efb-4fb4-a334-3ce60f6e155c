<?php

ini_set('memory_limit', '2048M'); // Defina o valor de memória desejado, como 1024M para 1GB

defined('BASEPATH') or exit('No direct script access allowed');

class Orders extends MY_Controller
{

	private $order_bySupport = false;

	public function __construct()
	{
		parent::__construct();
		$this->load->model('FlowOrders_model');
		$this->load->library('pagination');

		$this->dbmkts = $this->load->database('db_mkts', TRUE);
	}


	 // Esta função processará a solicitação de encaminhamento e fará a chamada externa
	 public function forward_order() {
        // Verificar se é uma requisição AJAX
        if (!$this->input->is_ajax_request()) {
            $this->output->set_status_header(400);
            echo json_encode(['status' => 'error', 'message' => 'Invalid request']);
            return;
        }

        try {
            // Marcar que esta é uma solicitação de suporte
            $this->order_bySupport = true;
            
            // Obter o corpo da requisição JSON
            $json = file_get_contents('php://input');
            $data = json_decode($json);
            
            if (!$data) {
                throw new Exception("Invalid JSON data");
            }
            
            // Extrair os dados da requisição
            $channel = $data->channel ?? '';
            $id_usuario = $data->id_usuario ?? null;
            $order_type = $data->order_type ?? '';
            $order_id = $data->order_id ?? null;
            $client_id = $data->client_id ?? null;
            
            // Validar dados obrigatórios
            if (!$order_id || !$client_id) {
                throw new Exception("Missing required parameters");
            }
            
            // Determinar o endpoint baseado no tipo de ordem
            $endpoint = isset($data->order_type) && $data->order_type == "new_forward" 
                ? "https://apolus.bighub.store/forward.php"
                : "https://apolo.bighub.store/set-user-to-order";
            
            // Substituir pelo endpoint real
            $endpoint = "";
            
            // Iniciar a chamada curl para o endpoint externo
            $curl = curl_init();
            
            // Preparar os dados para envio ao endpoint externo
            $post_data = [
                'channel' => $channel,
                'id_usuario' => $id_usuario,
                'order_id' => $order_id,
                'client_id' => $client_id
            ];

            
            // Adicionar order_type apenas se for do tipo 'new_forward'
            if (isset($data->order_type) && $data->order_type == "new_forward") {
                $post_data['order_type'] = "new_forward";
            }
            
            // Configurar o curl
            curl_setopt_array($curl, [
                CURLOPT_URL => $endpoint,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => json_encode($post_data),
				CURLOPT_USERAGENT => 'Mozilla/5.0 (compatible; MeuScript/1.0)',
                CURLOPT_HTTPHEADER => [
                    'Content-Type: application/json',
                    'Accept: application/json',
                    'X-Requested-With: XMLHttpRequest',
                    'Origin: ' . base_url()
                ],
            ]);
            
            // Executar a chamada
            $response = curl_exec($curl);
            $err = curl_error($curl);
            $httpcode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            
            curl_close($curl);
            
            // Verificar se houve erro
            if ($err) {
                throw new Exception("cURL Error: " . $err);
            }
            
            // Processar a resposta
            $response_data = json_decode($response);
            
            // Verificar se a resposta é válida
            if (!$response_data && $httpcode != 200) {
                throw new Exception("Invalid response from external API. Status code: " . $httpcode);
            }
            
            // Registrar a operação em log
            log_message('info', 'Order forwarded: ' . json_encode($post_data) . ' to endpoint: ' . $endpoint);
            
            // Retornar a resposta do servidor externo ou uma mensagem de sucesso
            echo json_encode([
                'status' => 'success',
                'message' => 'Order successfully assigned to user',
                'response' => $response_data
            ]);
            
        } catch (Exception $e) {
            log_message('error', 'Order forward error: ' . $e->getMessage());
            $this->output->set_status_header(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'Failed to process order: ' . $e->getMessage()
            ]);
        }
    }
    


	public function list_orders()
	{
		$data = $this->_getDataDashboard();
		$data += $this->_getRespAction();

		$data['session'] = 'Orders';
		$data['view'] = 'orders/list_orders';
		$this->load->view('includes/template', $data);
	}


	public function list_orders_shopping()
	{
		$data = $this->_getDataDashboard();

		$arrayValoresExcluir = array('CANCELED', 'CLOSED', 'RECEIVED', 'SHIPPED');

		$this->db->select('id, user_id, reference, channel, country, state, total_price, total_commission, purchased, shipping_time, created_date, created_at, customer, items');
		#$this->db->select('*');
		$this->db->from('tbl_orders');
		$this->db->where_not_in('state', $arrayValoresExcluir);
		#$this->db->where('id', '1704557');
		$this->db->order_by('id', 'desc');
		$orders = $this->db->get()->result();

		$data['quantity_orders'] = count($orders);

		$ord = [];
		$i = 0;
		foreach ($orders as $key => $order) {

			// echo json_encode($order);
			// die();

			foreach (json_decode($order->items) as $it => $item) {

				$ord[$i] = $item;
				$ord[$i]->data_compra = $order->created_date;
				$ord[$i]->channel = $order->channel;
				$ord[$i]->country = $order->country;
				$ord[$i]->title = $item->title;
				$ord[$i]->order_id = $order->reference;
				$i = $i + 1;
			}
		}

		// foreach ($ord as $key => $or){
		// 	$all_items[$key] = $or['items'];
		// }

		$data['items'] = $ord;
		// echo json_encode($data);
		// die();

		$data += $this->_getRespAction();


		$data['session'] = 'Orders';
		$data['view'] = 'orders/list_orders_shopping';
		$this->load->view('includes/template', $data);
	}


	private function fim_de_linha($inputString)
	{
		if (mb_strlen($inputString) <= 30) {
			return $inputString; // Retorna a string original se tiver 20 caracteres ou menos
		} else {
			return mb_substr($inputString, 0, 30) . '...'; // Retorna os primeiros 20 caracteres seguidos de "..."
		}
	}

	public function order_details($id)
	{
		$data = $this->_getDataDashboard();

		$query = $this->db->where('id', $id)->get('tbl_orders'); #342325
		$resp = $query->row();

		$address = $resp->customer;
		$data = json_decode($address, true);

		$billingAddress = $data['billing_address'];
		$shippingAddress = $data['shipping_address'];

		// if($billingAddress == null){
		// 	$billingAddress = [
		// 		'first_name' => 'No name',
		// 		'last_name' => 'No last name',
		// 		'phone' => 'No phone',
		// 		'street_1' => ' No address',
		// 		'zip_code' => 'no zip_code',
		// 		'city' => 'No city'
		// 	];
		// }

		// if($shippingAddress == null){
		// 	$shippingAddress = [
		// 		'first_name' => 'No name',
		// 		'last_name' => 'No last name',
		// 		'phone' => 'No phone',
		// 		'street_1' => ' No address',
		// 		'zip_code' => 'no zip_code',
		// 		'city' => 'No city'
		// 	];
		// }
		$img_default = 'https://cdn.bighub.store/image/product-placeholder.png';
		$image = $img_default;

		#$data['attachment'] = $resp->attachment;
		$itens = json_decode($resp->items);
		$total_taxes = 0;

		$state_neuter = ['SHIPPING'];
		$state_positive = ['RECEIVED', 'TO_COLLECT', 'SHIPPED'];
		$state_negative = ['WAITING_ACCEPTANCE', 'WAITING_DEBIT', 'WAITING_DEBIT_PAYMENT', 'CLOSED', 'REFUNDED', 'CANCELED', 'REFUSED', 'AUTO_REFUSED', 'AUTO_CLOSED'];
		$state_class = 'state-neutra';

		foreach ($itens as $key => $item) {

			$taxes = $item->taxes[0]->amount ?? 0;
			$total_taxes = $total_taxes + $taxes;

			if (in_array($item->state, $state_positive)) {
				$state_class = 'state-positive';
			} elseif (in_array($item->state, $state_negative)) {
				$state_class = 'state-negative';
			} elseif (in_array($item->state, $state_neuter)) {
				$state_class = 'state-neuter';
			}

			if (empty($item->image) || $item->image != 'N/A') {
				$image = $item->image;
			}

			$new_itens[$key] = [
				'title' => $item->title,
				'ean' => $item->ean,
				'sku' => $item->sku,
				'image' => $image,
				'image_real' => $item->image,
				'state' => $item->state,
				'class_state' => $state_class,
				'quantity' => $item->quantity,
				'leadtime_to_ship' => $item->leadtime_to_ship,
				'shipping_price' => $item->shipping_price,
				'taxe_amount' => $taxes,
				'commission_fee' => $item->commission_fee,
				'commission_rate_vat' => $item->commission_rate_vat,
				'total_commission' => $item->total_commission,
				'total_price' => $item->total_price,
				'total_price_no_taxe' => round($item->total_price - $taxes, 2),
				'price_unit' => $item->price_unit,
			];
		}

		$data['items'] = json_decode(json_encode($new_itens));
		$data['total_taxes'] = $total_taxes;

		$data['id_order'] = $id;
		$data['seller'] = $this->get_seller(json_decode($resp->user_id));
		$data['customer'] = json_decode($resp->customer);
		$data['billing_address'] = $billingAddress;
		$data['shipping_address'] = $shippingAddress;
		$data['order_items'] = count(json_decode($resp->items));

		$data['payment'] = json_decode($resp->payment)->type ?? 'Awaiting payment';
		$data['order_data'] = $resp;

		// $raw = $resp->attachment;
		// $array = json_decode($raw, true);
		// $response = $array[0]['file_content']['data'];

		// // Converte o array para uma string
		// $responseString = implode(array_map("chr", $response));

		// // Desempacota a string convertida
		// $unpackedData = unpack('H*', $responseString);

		// echo json_encode($data);
		// die();

		$this->load->view('orders/modal-order-details.php', $data);
	}

	private function get_seller($user_id)
	{
		$this->db->where('id', $user_id);
		$query = $this->db->get('tbl_user_clients');
		return $query->row();
	}


	public function impersonate()
	{
		$token = $this->input->post('user');
		$resp = $this->curl_impersonate($token);
		echo json_encode($resp->data->api_token);
	}

	private function curl_impersonate($user_token)
	{
		$url  = "https://app.bighub.store/api/v1/authorize?app_key=$user_token";

		$handle   = curl_init($url);

		$request_headers     = [];
		$request_headers[] = 'Accept: application/json';
		$request_headers[] = 'Content-Type: application/json';

		curl_setopt($handle, CURLOPT_RETURNTRANSFER, TRUE);
		curl_setopt($handle, CURLOPT_HTTPHEADER, $request_headers);
		$response = curl_exec($handle);

		return json_decode($response);
	}

	public function update_purchased_order()
	{
		$order_id = $this->input->post('order');
		$action = $this->input->post('action');

		$this->db->where('id', $order_id);
		$data = [
			'purchased' => $action
		];
		if ($this->db->update('tbl_orders', $data)) {
			$resp = [
				'update' => 'success',
				'action' => $action
			];
			echo json_encode($resp);
		}
	}


	# Implementando paginação #######################################################

	public function get_orders()
	{
		$draw = intval($this->input->post("draw"));
		$start = intval($this->input->post("start"));
		$length = intval($this->input->post("length"));
		$search = $this->input->post("search")['value'];
		$order_column = intval($this->input->post("order")[0]['column']);
		$order_dir = $this->input->post("order")[0]['dir'];

		$columns = [
			null,
			'reference',
			'created_date',
			'items',
			'channel',
			'user_id',
			'state',
			'total_price',
			null
		];

		$order_by = isset($columns[$order_column]) ? $columns[$order_column] : 'id';

		$this->db->select('ord.id, ord.reference, ord.created_date, ord.items, ord.channel, ord.country, ord.user_id, ord.state, ord.total_price, IFNULL(inv.id, 0) as isinvoice, inv.id_invoice');
		$this->db->from('tbl_orders ord');

		if (!empty($search)) {
			$this->db->like('ord.reference', $search);
			$this->db->or_like('ord.channel', $search);
		}

		$this->db->join('tbl_invoices inv', 'ord.id = inv.order_id', 'left');
		$this->db->order_by('ord.id', 'DESC');



		$totalFiltered = $this->db->count_all_results('', FALSE);

		$this->db->limit($length, $start);
		$this->db->order_by($order_by, $order_dir);

		$query = $this->db->get();
		$orders = $query->result();

		$data = array();
		foreach ($orders as $order) {
			$items_count = count(json_decode($order->items));
			$channel_country = $order->channel . '/' . $order->country;

			$stateClass = $this->getStateClass($order->state);

			$actions = "<a class='link-modal-dados-order btn btn-info btn-sm new-button' onclick='setId($order->id)' href='" . base_url() . "order-details/$order->id' data-bs-toggle='modal' data-bs-target='#modal-dados-order'>Detalhes</a>";
			$actions .= "<a class='link-modal-dados-ship btn btn-secondary btn-sm new-button' style='left: 2px' onclick='setId($order->id)' href='" . base_url() . "order-shipment-details/$order->id' data-bs-toggle='modal' data-bs-target='#modal-dados-ship'>Shipment</a>";

			$allowedChannels = ['pccomp', 'pccompes', 'carrefour', 'pixmania'];
			if (in_array($order->channel, $allowedChannels)) {
				if ($order->isinvoice) {
					$actions .= "<a target='_blank' class='link-modal-dados-order btn btn-success btn-sm new-button' style='font-size: 10px;' href='https://prd-mkp.bighub.store/invoices/$order->id_invoice.pdf'>Ver Fatura</a>";
				} else {
					$actions .= "<div id='invoice-loading-$order->id' class='spinner-border esconder' role='status' style='width: 15px; height: 15px;'>
                                    <span class='sr-only'>Loading...</span>
                                 </div>
                                 <a id='see-invoice-$order->id' target='_blank' class='link-modal-dados-order btn btn-success btn-sm new-button esconder' style='font-size: 10px;'>Ver Fatura</a>
                                 <button id='generate-invoice-$order->id' class='link-modal-dados-order btn btn-primary btn-sm new-button' onclick='generateInvoice($order->id)' style='font-size: 10px;'>Gerar Fatura</button>";
				}
			}

			$data[] = array(
				"",
				$order->reference,
				date("d/m/Y", strtotime($order->created_date)),
				$items_count,
				$channel_country,
				$order->user_id,
				"<span class='$stateClass'>$order->state</span>",
				$order->total_price,
				$actions
			);
		}

		$result = array(
			"draw" => $draw,
			"recordsTotal" => $this->get_total_orders(),
			"recordsFiltered" => $totalFiltered,
			"data" => $data
		);

		echo json_encode($result);
	}

	private function get_total_orders()
	{
		$this->db->select("COUNT(*) as num");
		$this->db->from("tbl_orders");
		$query = $this->db->get();
		$result = $query->row();
		return $result->num;
	}

	private function getStateClass($state)
	{
		$statePositive = array('RECEIVED', 'TO_COLLECT', 'SHIPPED');
		$stateNegative = array('WAITING_ACCEPTANCE', 'WAITING_DEBIT', 'WAITING_DEBIT_PAYMENT', 'CLOSED', 'REFUNDED', 'CANCELED', 'REFUSED', 'AUTO_REFUSED', 'AUTO_CLOSED');
		$stateNeuter = array('SHIPPING');

		if (in_array($state, $statePositive)) {
			return 'badge-success badge-sm';
		} else if (in_array($state, $stateNegative)) {
			return 'badge-negative badge-sm';
		} else if (in_array($state, $stateNeuter)) {
			return 'badge-neuter badge-sm';
		}
		return '';
	}

	private function getStateClass_old($state)
	{
		$state_neuter = ['SHIPPING'];
		$state_positive = ['RECEIVED', 'TO_COLLECT', 'SHIPPED'];
		$state_negative = ['WAITING_ACCEPTANCE', 'WAITING_DEBIT', 'WAITING_DEBIT_PAYMENT', 'CLOSED', 'REFUNDED', 'CANCELED', 'REFUSED', 'AUTO_REFUSED', 'AUTO_CLOSED'];

		if (in_array($state, $state_positive)) {
			return 'badge-success badge-sm';
		} elseif (in_array($state, $state_negative)) {
			return 'badge-negative badge-sm';
		} elseif (in_array($state, $state_neuter)) {
			return 'badge-neuter badge-sm';
		} else {
			return '';
		}
	}

	public function shipment_details($id)
	{
		$query = $this->db->where('id', $id)->get('tbl_orders'); #342325
		$resp = $query->row();

		$address = $resp->customer;
		$data = json_decode($address, true);
		$shippingAddress = $data['shipping_address'];

		$data['shipping_address'] = $shippingAddress;
		$data['order_number_in_logistic'] = $resp->reference . ' ' . $resp->channel . '/' . $resp->country;
		$data['order_number'] = $resp->reference;
		$data['channel'] = $resp->channel;
		$data['country'] = $resp->country;

		$totalQuantity = 0;

		foreach (json_decode($resp->items) as $item) {
			$totalQuantity += $item->quantity;
		}

		$data['total_items'] = $totalQuantity;
		$data['total_price'] = $resp->total_price;

		$this->load->view('orders/modal-shipment-details.php', $data);
	}

	public function shipment_methods($senderAddress, $country, $zipCode)
	{
		header('Content-Type: application/json');
		$curl = curl_init();

		curl_setopt_array($curl, array(
			CURLOPT_URL => "https://prd-mkp-1.bighub.store/app-logistic/api/label/carriers/$senderAddress/$country/$zipCode",
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => '',
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 0,
			CURLOPT_FOLLOWLOCATION => true,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => 'GET',
			CURLOPT_HTTPHEADER => array(
				'Authorization: Basic YmlnaHViX2xvZ2lzdGljOmZXeCVHa0AzclAheg=='
			),
		));

		$response = curl_exec($curl);

		curl_close($curl);

		echo $response;
	}

	public function get_labels()
	{
		$data = json_decode(file_get_contents('php://input'), true);

		$curl = curl_init();

		curl_setopt_array($curl, array(
			CURLOPT_URL => 'https://prd-mkp-1.bighub.store/app-logistic/api/label',
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => '',
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 0,
			CURLOPT_FOLLOWLOCATION => true,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => 'POST',
			CURLOPT_POSTFIELDS => $data,
			CURLOPT_HTTPHEADER => array(
				'Content-Type: application/json',
				'Authorization: Basic YmlnaHViX2xvZ2lzdGljOmZXeCVHa0AzclAheg=='
			),
		));

		$response = curl_exec($curl);

		curl_close($curl);

		echo $response;
	}

	public function get_tracking()
	{
		$data = json_encode(['order_number' => json_decode(file_get_contents('php://input'), true)]);

		return $this->curlGetTracking($data);
	}

	public function get_origins()
	{
		$curl = curl_init();

		curl_setopt_array($curl, [
			CURLOPT_URL => "https://panel.sendcloud.sc/api/v2/user/addresses/sender",
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => "",
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 30,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => "GET",
			CURLOPT_HTTPHEADER => [
				"Accept: application/json",
				"Authorization: Basic YWFjYjMyMTEtZDA1OC00MTc1LWE1NmItZDUzMzlmNmFiZTJhOjkzYzBmNmJhM2ZlNTRmMGE4NGExYjRjMDE2MWQyODc5"
			],
		]);

		$response = curl_exec($curl);
		$err = curl_error($curl);

		curl_close($curl);

		if ($err) {
			echo "cURL Error #:" . $err;
		} else {
			echo $response;
		}
	}

	public function shippment()
	{
		$data = json_decode(file_get_contents('php://input'), true);

		$dataCurlGetTracking = json_encode(['order_number' => $data['order_number_in_logistic']]);
		$getTrackingInLogistic = $this->curlGetTracking($dataCurlGetTracking);

		$trackingData = json_decode($getTrackingInLogistic, true);

		$this->db->select('carriers.id carrierId, countries.id countryId, channels.id channelId, carriers.carrier_name ');
		$this->db->from('tbl_carriers carriers');
		$this->db->like('carriers.carrier_name', $data['carrier']);
		$this->db->where('carriers.channel', $data['channel']);
		$this->db->where('carriers.country', $data['country']);
		$this->db->join('tbl_countries countries', 'carriers.country = countries.code', 'right');
		$this->db->join('tbl_channels channels', 'carriers.channel = channels.code', 'right');

		$dataToShip = $this->db->get()->result();

		$dataToLogistic = [
			"carrier_id" => (int)$dataToShip[0]->carrierId,
			"channel_id" => (int)$dataToShip[0]->channelId,
			"country_id" => (int)$dataToShip[0]->countryId,
			"reference" => $data['order_number'],
			"tracking_number" => $trackingData['tracking_number']
		];

		$user = $this->db->where('id', 33)->get('tbl_user_clients')->row();
		$token = $this->curl_impersonate($user->user_token);

		$this->curlBighubLogistic($dataToLogistic, $token->data->api_token);

		$dataToOrderShipment = [
			"company" => $dataToShip[0]->carrier_name,
			"price" => 0,
			"tracking" => $trackingData['tracking_number'],
			"zone" => ""
		];

		$this->curlBighubOrderShipment($data['order_number'], $dataToOrderShipment, $token->data->api_token);
	}

	private function curlBighubLogistic($data, $token)
	{
		$curl = curl_init();

		curl_setopt_array($curl, array(
			CURLOPT_URL => "https://app.bighub.store/api/v2/logistics/trackings?token=$token",
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => '',
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 0,
			CURLOPT_FOLLOWLOCATION => true,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => 'POST',
			CURLOPT_POSTFIELDS => json_encode($data),
			CURLOPT_HTTPHEADER => array(
				'Content-Type: application/json'
			),
		));

		$response = curl_exec($curl);

		curl_close($curl);
	}

	private function curlBighubOrderShipment($order, $data, $token)
	{
		$this->db->select('*');
		$this->db->from('tbl_orders');
		$this->db->where('reference', $order);
		$orders = $this->db->get()->result();

		$orderId = $orders[0]->id;
		$curl = curl_init();

		curl_setopt_array($curl, array(
			CURLOPT_URL => "https://app.bighub.store/api/v2/orders/{$orderId}/shipment?token={$token}",
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => '',
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 0,
			CURLOPT_FOLLOWLOCATION => true,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => 'PUT',
			CURLOPT_POSTFIELDS => json_encode($data),
			CURLOPT_HTTPHEADER => array(
				'Content-Type: application/json'
			),
		));

		$response = curl_exec($curl);

		curl_close($curl);
	}

	private function curlGetTracking($data)
	{
		$curl = curl_init();

		curl_setopt_array($curl, array(
			CURLOPT_URL => "https://prd-mkp-1.bighub.store/app-logistic/api/label/get-tracking",
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => '',
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 0,
			CURLOPT_FOLLOWLOCATION => true,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => 'POST',
			CURLOPT_POSTFIELDS => $data,
			CURLOPT_HTTPHEADER => array(
				'Content-Type: application/json',
				'Authorization: Basic YmlnaHViX2xvZ2lzdGljOmZXeCVHa0AzclAheg=='
			),
		));

		$response = curl_exec($curl);

		curl_close($curl);

		return $response;
	}



	###########################################################################
	#Orders No Seller
	public function orders_noseller()
	{
		$data = $this->_getDataDashboard();

		$data['ordernouser'] = $this->dbmkts->select('*')->where('user_id', 25)->get('orders')->result();
		

		// Supondo que $news contém sua resposta JSON
		$news = $this->getOrdersByUserId();
		
		// Decodificar o JSON para um array associativo PHP
		$newsArray = json_decode($news, true);
		
		// Inicializar o array de saída
		$data['ordernouser_new_forward'] = [];
		
		// Verificar se a decodificação foi bem-sucedida e se há dados
		if (isset($newsArray['success']) && $newsArray['success'] === true && isset($newsArray['data'])) {
			// Loop através dos itens de dados
			foreach ($newsArray['data'] as $order) {
				// Criar objeto para adequar ao formato usado na tabela
				$orderObj = new stdClass();
				$orderObj->id = $order['id'];
				$orderObj->marketplace = $order['marketplace']['name'];
				$orderObj->order_id = $order['order_id'];
				$orderObj->products = $order['product_quantity']; // Quantidade de produtos
				$orderObj->product_sku = isset($order['sku'][0]) ? $order['sku'][0] : '';
				$orderObj->price = $order['price'];
				$orderObj->created_at = $order['created_at'];
				
				// Adicionar ao array de saída
				$data['ordernouser_new_forward'][] = $orderObj;
			}
		}
		
		// Agora $data['ordernouser_new_forward'] contém os dados formatados
		// echo json_encode($data);
		// die();

		// Configurações adicionais da estrutura
		$data['menuListCliente'] = false;
		$data['session'] = 'Listar orders no seller';
		$data['view'] = 'orders/list_orders_nouser';

		// Carrega a view com o template e os dados filtrados
		$this->load->view('includes/template', $data);
	}


	private function getOrdersByUserId($userId = 25) {
		$token = "bCZpeQAcwEIcm7y1hYCK3VspKQfWJ5IlgalrUqq2f60552dd"; 
		
		$url = "https://connect-big.bighub.store/api/orders/user/" . $userId;
		
		$curl = curl_init();
		
		curl_setopt_array($curl, [
			CURLOPT_URL => $url,
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_HTTPHEADER => [
				'Authorization: Bearer ' . $token
			]
		]);
		
		$response = curl_exec($curl);
		$error = curl_error($curl);
		
		curl_close($curl);
		
		if ($error) {
			return "Erro cURL: " . $error;
		}
		
		return $response;
	}
	
	// Exemplo de uso:
	// $result = getOrdersByUserId();
	// echo $result;

	// public function setUserToOrder() 
	// {
    //     // Recebe os dados JSON enviados no corpo da requisição
    //     $data = json_decode($this->input->raw_input_stream, true);

    //     // Valide ou use os dados conforme necessário
    //     $orderId = $data['order_id'];
    //     $orderChannel = $data['channel'];
    //     $userId = $data['client_id'];
    //     $id_usuario = $data['id_usuario'];

		
		
    //     // Aqui, você pode implementar a lógica para lidar com esses dados, como salvar no banco

    //     // Retorna uma resposta JSON
    //     $response = array('status' => 'success', 'message' => 'Usuário associado ao pedido');
    //     $this->output
    //         ->set_content_type('application/json')
    //         ->set_output(json_encode($response));
    // }
}
