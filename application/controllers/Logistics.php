<?php

ini_set('memory_limit', '2048M'); // Defina o valor de memória desejado, como 1024M para 1GB

defined('BASEPATH') or exit('No direct script access allowed');

class Logistics extends MY_Controller
{

	const XAPIKEY = '7e0913c6-015d-4c60-9c49-3e4837922e53';

	public $LIST_USERS_ID = array(); // Inicializando o array como uma propriedade pública

    public function __construct()
    {
        parent::__construct();
        $this->load->library('pagination');
        $this->load->library('upload');

        $this->setListUsersID(); // Chama o método para popular o array
    }

    private function setListUsersID()
    {
        // Faz a query para pegar os user_ids com name = 'is_supplier' e value = 1
        $query = $this->db->select('user_id')
                          ->from('tbl_user_settings')
                          ->where('name', 'is_supplier')
                          ->where('value', 1)
                          ->get();

        // Converte os resultados da query em um array
        $result = $query->result_array();

        // Extrai os valores de 'user_id' e armazena em LIST_USERS_ID
        $this->LIST_USERS_ID = array_column($result, 'user_id');
    }


	# Compras
	################################################################################

	public function list_orders()
	{
		$data = $this->_getDataDashboard();
		$data += $this->_getRespAction();

		$data['session'] = 'purchase-orders';
		$data['view'] = 'logistic/purchases/list_orders';
		$this->load->view('includes/template', $data);
	}

	/**
	 * Exibe a página de listagem de pedidos usando a versão melhorada da função get_orders
	 */
	public function list_orders_new()
	{
		$data = $this->_getDataDashboard();
		$data += $this->_getRespAction();

		$data['session'] = 'purchase-orders';
		$data['view'] = 'logistic/purchases/list_orders_new';
		$this->load->view('includes/template', $data);
	}

	public function order_details($id)
	{
		$data = $this->_getDataDashboard();

		$this->create_line_order($id);
		// echo json_encode($data);
		// // die();

		$query = $this->db->where('id', $id)->get('tbl_orders');
		$resp = $query->row();

		$address = $resp->customer;
		$data = json_decode($address, true);

		$billingAddress = $data['billing_address'];
		$shippingAddress = $data['shipping_address'];

		$img_default = 'https://cdn.bighub.store/image/product-placeholder.png';
		$image = $img_default;

		#$data['attachment'] = $resp->attachment;
		$itens = json_decode($resp->items);
		$total_taxes = 0;

		$state_neuter = ['SHIPPING'];
		$state_positive = ['RECEIVED', 'TO_COLLECT', 'SHIPPED'];
		$state_negative = ['WAITING_ACCEPTANCE', 'WAITING_DEBIT', 'WAITING_DEBIT_PAYMENT', 'CLOSED', 'REFUNDED', 'CANCELED', 'REFUSED', 'AUTO_REFUSED', 'AUTO_CLOSED'];
		$state_class = 'state-neutra';

		$total_cost_price = 0;

		foreach ($itens as $key => $item) {

			// echo json_decode($item->finance);
			// die();

			$taxes = $item->taxes[0]->amount ?? 0;
			$total_taxes = $total_taxes + $taxes;

			if (in_array($item->state, $state_positive)) {
				$state_class = 'state-positive';
			} elseif (in_array($item->state, $state_negative)) {
				$state_class = 'state-negative';
			} elseif (in_array($item->state, $state_neuter)) {
				$state_class = 'state-neuter';
			}

			if (empty($item->image) || $item->image != 'N/A') {
				$image = $item->image;
			}

			$this->db->select('*');
			$this->db->where('order_id', $id);
			$this->db->where('item_id', $key);
			$this->db->from('tbl_purchase_details_itens');
			$itemRow = $this->db->get()->row();

			if (isset($itemRow->cost_price)) {
				$cost_price_item = $itemRow->cost_price;
			} else {
				$cost_price_item = $this->get_price_supplier($item->ean, $resp->user_id);
			}
			$total_cost_price = $total_cost_price + $cost_price_item;

			######################################################################
			# Verify supplier
			# COnsulta base de dados e ver se já existe json com suppliers

			# Se sim apenas devolver para front end

			# Se não, fazer a consulta, gravar na base de dados e devolver para o frontend.
			$suppliers = $this->get_supplier($item->ean);



			######################################################################


			$new_itens[$key] = [
				'title' => $item->title,
				'ean' => $item->ean,
				'sku' => $item->sku,
				'image' => $image,
				'image_real' => $item->image,
				'state' => $item->state,
				'class_state' => $state_class,
				'quantity' => $item->quantity,
				'leadtime_to_ship' => $item->leadtime_to_ship,
				'shipping_price' => $item->shipping_price,
				'taxe_amount' => $taxes,
				'commission_fee' => $item->commission_fee,
				'commission_rate_vat' => $item->commission_rate_vat,
				'total_commission' => $item->total_commission,
				'total_price' => $item->total_price,
				'total_price_no_taxe' => round($item->total_price - $taxes, 2),
				'price_unit' => $item->price_unit,
				'cost_price' => $cost_price_item,
				'supplier_id' => $itemRow->supplier_id ?? '',
				'status_id' => $itemRow->status_id ?? '',
				'obs_item' => $itemRow->obs_item ?? '',
				'finance' => isset($item->finance) ? json_encode($item->finance) : '',
				'invoice' => false,
				'link_invoice' => 'no_link',
				'suppliers' => json_encode($suppliers)
			];

			if ($this->check_invoice_exist($id . '_' . $key . '.pdf')) {
				$new_itens[$key]['link_invoice'] = base_url('/uploads/' . $id . '_' . $key . '.pdf');
				$new_itens[$key]['invoice'] = true;
			}
		}

		#https://materialui.co/icons

		$data['total_cost_price'] = $total_cost_price;

		$data['items'] = json_decode(json_encode($new_itens));
		$data['total_taxes'] = $total_taxes;

		$data['id_order'] = $id;
		$data['seller'] = $this->get_seller(json_decode($resp->user_id));
		$data['customer'] = json_decode($resp->customer);
		$data['billing_address'] = $billingAddress;
		$data['shipping_address'] = $shippingAddress;
		$data['order_items'] = count(json_decode($resp->items));

		$data['payment'] = json_decode($resp->payment)->type ?? 'Awaiting payment';
		$data['order_data'] = $resp;


		// # Busacar todo o historico referente a essa order
		// $data['purchase_history'] = $this->get_purchase_history($id);

		# Buscar todos os detalhes de interação dessa compra
		$data['purchase_details'] = $this->get_purchase_details($id);

		$data['finance_order'] = json_decode($resp->finance);

		$data['price_cost'] = 0.00;



		// var_dump($data['order_data']->finance);

		$this->db->select('*');
		$this->db->from('tbl_suppliers');
		$data['suppliers'] = $this->db->get()->result();

		$this->db->select('*');
		$this->db->from('tbl_usuarios');
		$this->db->where('ativo', 1);
		$data['users'] = $this->db->get()->result();

		// echo json_encode($data);
		// die();

		$this->load->view('logistic/purchases/modal-order-details.php', $data);
	}

	private function create_line_order($order_id)
	{
		// Verifica se já existe um pedido com o order_id fornecido
		$this->db->select('*');
		$this->db->from('tbl_purchase_details');
		$this->db->where('order_id', $order_id);
		$order = $this->db->get()->row();

		if (empty($order)) {
			// Se não encontrar nenhum pedido, tenta criar um novo registro
			$purchase_details = [
				'order_id' => $order_id
			];

			// Insere o novo pedido e retorna true se a inserção for bem-sucedida
			if ($this->db->insert('tbl_purchase_details', $purchase_details)) {
				return true;
			} else {
				// Retorna false se a inserção falhar
				return false;
			}
		}

		// Retorna false se o pedido já existir
		return false;
	}

	private function get_price_supplier($ean, $seller_id)
	{
		$this->db->select('*');
		$this->db->from('tbl_user_products');
		$this->db->where('barCode', $ean);
		$this->db->where('user_id', $seller_id);
		$product = $this->db->get()->row();
		return $product->cost_price ?? 0.00;
	}

	public function get_timeline_history()
	{
		$order_id = $this->input->post('order_id');
		$resp = $this->get_purchase_history($order_id);
		echo json_encode($resp);
	}

	private function save_data_timeline($datainfo)
	{
		if ($this->db->insert('tbl_purchase_order_timeline', $datainfo)) {
			return true;
		}
	}

	public function save_details_order()
	{
		$order_status = $this->input->post('order_status');
		$order_id = $this->input->post('order_id');
		$obs_order = $this->input->post('obs_order');

		$user_id = $this->session->userdata('id_usuario');

		$this->db->select('*');
		$this->db->from('tbl_purchase_details');
		$this->db->where('order_id', $order_id);
		$order = $this->db->get()->row();

		$purchase_details = [
			'order_id' => $order_id,
			'status' => $order_status,
			'author_id' => $user_id,
			'comments' => $obs_order
		];

		if ($order) {
			$this->db->where('id', $order->id);
			if ($this->db->update('tbl_purchase_details', $purchase_details)) {
				$resp = [
					'status' => 'success',
					'message' => 'Dados salvos com sucesso'
				];
				echo json_encode($resp);
			}
		} else {
			if ($this->db->insert('tbl_purchase_details', $purchase_details)) {
				$resp = [
					'status' => 'success',
					'message' => 'Dados salvos com sucesso'
				];
				echo json_encode($resp);
			}
		}
	}

	public function save_costprice_item()
	{
		$cost_price = $this->input->post('price_cost');
		$order_id = $this->input->post('order_id');
		$key_number = $this->input->post('key_number');

		$user_id = $this->session->userdata('id_usuario');

		$this->db->select('*');
		$this->db->from('tbl_purchase_details_itens');
		$this->db->where('order_id', $order_id);
		$this->db->where('item_id', $key_number);
		$item = $this->db->get()->row();

		$change_costprice = [
			'order_id' => $order_id,
			'cost_price' => $cost_price,
			'item_id' => $key_number
		];

		if (!empty($item)) {
			$this->db->where('id', $item->id);
			$this->db->update('tbl_purchase_details_itens', $change_costprice);
		} else {
			$this->db->insert('tbl_purchase_details_itens', $change_costprice);
		}

		$status_item = 6;

		# Gravar os dados para serem apresentados na timeline
		$data_timeline = [
			'order_id' => $order_id,
			'author_id' => $user_id,
			'status_id' => $status_item,
			'complement' => 'Item: ' . $key_number . ' - Valor: ' . $cost_price
		];

		if ($this->save_data_timeline($data_timeline)) {
			$resp = [
				'status' => 'success'
			];
			echo json_encode($resp);
		}
	}

	# Salvar os detalhes de um intem, status, fornecedor e etc
	public function save_details_item()
	{
		$supplier_item = $this->input->post('supplier_item');
		$status_item = $this->input->post('status_item');
		$order_id = $this->input->post('order_id');
		$obsItem = $this->input->post('obs_item');
		$key_number = $this->input->post('key_number');
		$price_cost = $this->input->post('price_cost');

		$user_id = $this->session->userdata('id_usuario');

		$this->db->select('*');
		$this->db->from('tbl_purchase_details_itens');
		$this->db->where('order_id', $order_id);
		$this->db->where('item_id', $key_number);
		$item = $this->db->get()->row();

		if ($status_item == 2) {
			$status_item = 4;
		}

		# Verifico se já existe esse item, se sim somente atualiza.
		# Caso contrario é inserido.
		if (!empty($item)) {
			$this->db->where('id', $item->id);
			$data_info = [
				'supplier_id' => $supplier_item,
				'status_id' => $status_item,
				'obs_item' => $obsItem,
				'user_system_id' => $user_id,
				'cost_price' => $price_cost
			];
			$this->db->update('tbl_purchase_details_itens', $data_info);
		} else {

			# Gravar informações do item na tbl_purchase_details_itens
			$data_info = [
				'order_id' => $order_id,
				'supplier_id' => $supplier_item,
				'status_id' => $status_item,
				'item_id' => $key_number,
				'obs_item' => $obsItem,
				'user_system_id' => $user_id,
				'cost_price' => $price_cost
			];
			$this->db->insert('tbl_purchase_details_itens', $data_info);
		}


		$status_item = $this->input->post('status_item');

		# Gravar os dados para serem apresentados na timeline
		$data_timeline = [
			'order_id' => $order_id,
			'author_id' => $user_id,
			'status_id' => $status_item,
			'complement' => 'Item: ' . $key_number . ' - Fornecedor: ' . $this->get_name_supplier($supplier_item)
		];

		if ($this->save_data_timeline($data_timeline)) {
			if ($status_item == 2) {
				$data_timeline['status_id'] = 4;
				$this->save_data_timeline($data_timeline);
			}
		}

		$resp = [
			'status' => 'success'
		];
		echo json_encode($resp);
	}

	# Ainda falta colocar para gravar os fornecedores em algum canto
	public function get_supplier($ean)
	{
		$url_api = 'https://app.bighub.store/container/feed/' . $ean;

		$handle = curl_init($url_api);

		$request_headers = [];
		$request_headers[] = 'Accept: application/json';
		$request_headers[] = 'Content-Type: application/json';
		$request_headers[] = 'x-apikey: b3f7d32c-b3e9-4abd-9967-11381fa07ab2';

		curl_setopt($handle, CURLOPT_RETURNTRANSFER, TRUE);
		curl_setopt($handle, CURLOPT_HTTPHEADER, $request_headers);

		$response = curl_exec($handle);

		if (curl_errno($handle)) {
			$error_msg = curl_error($handle);
			curl_close($handle);
			throw new Exception('Erro na requisição cURL: ' . $error_msg);
		}

		curl_close($handle);
		$resp = $this->extract_bighub_objects($response);
		return $resp;
	}

	private function extract_bighub_objects($response)
	{
		// Inicializa o array para armazenar as palavras encontradas
		$bighub_objects = [];

		// Define a expressão regular para encontrar "bighub-" seguido de uma palavra
		$pattern = '/bighub-(\w+)/';

		// Executa a expressão regular no texto da resposta
		preg_match_all($pattern, $response, $matches);

		// Se houver correspondências, adiciona a palavra seguinte ao array
		if (!empty($matches[1])) {
			$bighub_objects = $matches[1];
		}

		return $bighub_objects;
	}

	private function get_purchase_details($order_id)
	{
		# Buscar todo o historico dessa compra
		$this->db->select('pd.*');
		$this->db->from('tbl_purchase_details pd');
		$this->db->where('pd.order_id', $order_id);
		return $this->db->get()->row();
	}

	private function get_purchase_history($order_id)
	{
		# Buscar todo o historico dessa compra
		$this->db->select('ph.*');
		$this->db->from('tbl_purchase_order_timeline ph');
		$this->db->where('ph.order_id', $order_id);
		$this->db->order_by('ph.id', 'asc');
		$purchase_history = $this->db->get()->result();


		foreach ($purchase_history as $key => $value) {
			$purchase_history[$key]->status_name = $this->get_purchase_status($value->status_id);
			$purchase_history[$key]->author_name  = $this->get_author($value->author_id);

			// Cria um objeto DateTime a partir da string
			$dateTime = new DateTime($value->created_at);

			// Formata a data e hora no formato desejado
			$purchase_history[$key]->created_at  = $dateTime->format('d/m/Y H:i:s');
		}

		return $purchase_history;
	}

	private function get_author($user_id)
	{
		// Seleciona todas as colunas da tabela tbl_usuarios onde id_usuario é igual ao user_id
		$this->db->select('*');
		$this->db->from('tbl_usuarios');
		$this->db->where('id_usuario', $user_id);
		$user = $this->db->get()->row(); // Usa row() para obter um único resultado

		// Verifica se um usuário foi encontrado
		if ($user) {
			$response = $user->nome; // Acessa a coluna 'nome' do resultado
		} else {
			$response = 'não encontrado';
		}

		// Retorna o resultado
		return $response;
	}

	##################################### INVOICE ###################################

	public function upload_order_invoice()
	{
		// Configuração do upload
		$config['upload_path'] = './uploads/';
		$config['allowed_types'] = 'pdf|doc|docx'; // Tipos de arquivos permitidos
		$config['max_size'] = 2048; // Tamanho máximo do arquivo em KB

		// Obtém os parâmetros enviados
		$item_key = $this->input->post('item_key');
		$order_id = $this->input->post('order_id');

		// Obtém a data atual
		$date = new DateTime();
		$formatted_date = $date->format('d_m_Y'); // Formato dd_mm_aaaa

		// Configura o nome do arquivo
		$new_filename = "{$order_id}_{$item_key}.pdf";
		$file_path = $config['upload_path'] . $new_filename;

		// Verifica se o arquivo já existe e remove-o se necessário
		if (file_exists($file_path)) {
			unlink($file_path);
		}

		// Configura o nome do arquivo
		$config['file_name'] = $new_filename;
		$this->upload->initialize($config);

		// Verifica se o upload do arquivo foi bem-sucedido
		if (!$this->upload->do_upload('file')) {
			$error = array('status' => 'error', 'message' => $this->upload->display_errors());
			echo json_encode($error);
		} else {

			# Gravar o log
			$user_id = $this->session->userdata('id_usuario');

			# Gravar os dados para serem apresentados na timeline
			$data_timeline = [
				'order_id' => $order_id,
				'author_id' => $user_id,
				'status_id' => 7,
				'complement' => 'Item: ' . $item_key . ' Fatura: ' . $order_id . '_' . $item_key . '.pdf'
			];

			$file_url = base_url('uploads/' . $new_filename);

			if ($this->save_data_timeline($data_timeline)) {
				// Retorna uma resposta de sucesso
				$data = array(
					'status' => 'success',
					'message' => 'Arquivo enviado com sucesso!',
					'item_key' => $item_key,
					'order_id' => $order_id,
					'file_name' => $new_filename,
					'file_url' => $file_url
				);
				echo json_encode($data);
			}
		}
	}

	function check_invoice_exist($filename)
	{
		// Define o caminho da pasta de uploads
		$folder_path = './uploads/';

		// Certifique-se de que o caminho da pasta termine com uma barra
		$folder_path = rtrim($folder_path, '/') . '/';

		// Cria o caminho completo do arquivo
		$file_path = $folder_path . $filename;

		// Verifica se o arquivo existe e se é um arquivo
		if (file_exists($file_path) && is_file($file_path)) {
			return true;
		} else {
			return false;
		}
	}

	##################################################################################


	private function get_name_supplier($supplier_id)
	{
		$this->db->select('*');
		$this->db->from('tbl_suppliers');
		$this->db->where('id', $supplier_id);
		$supplier = $this->db->get()->row();

		// Verifica se um usuário foi encontrado
		if ($supplier) {
			$response = $supplier->name_supplier;
		} else {
			$response = 'não encontrado';
		}
		return $response;
	}

	private function get_purchase_status($status)
	{
		if ($status == 1) {
			$resp = 'Sem Stock';
		}
		if ($status == 2) {
			$resp = 'Comprado';
		}
		if ($status == 3) {
			$resp = 'Pago';
		}
		if ($status == 4) {
			$resp = 'Pendente de pagamento';
		}
		if ($status == 5) {
			$resp = 'Sugestão Enviada';
		}
		if ($status == 6) {
			$resp = 'Alteração P.Custo';
		}
		if ($status == 7) {
			$resp = 'Anexar Fatura';
		}

		return $resp;
	}

	private function get_seller($user_id)
	{
		$this->db->where('id', $user_id);
		$query = $this->db->get('tbl_user_clients');
		return $query->row();
	}

	public function impersonate()
	{
		$token = $this->input->post('user');
		$resp = $this->curl_impersonate($token);
		echo json_encode($resp->data->api_token);
	}

	private function curl_impersonate($user_token)
	{
		$url  = "https://app.bighub.store/api/v1/authorize?app_key=$user_token";

		$handle   = curl_init($url);

		$request_headers     = [];
		$request_headers[] = 'Accept: application/json';
		$request_headers[] = 'Content-Type: application/json';

		curl_setopt($handle, CURLOPT_RETURNTRANSFER, TRUE);
		curl_setopt($handle, CURLOPT_HTTPHEADER, $request_headers);
		$response = curl_exec($handle);

		return json_decode($response);
	}

	public function update_purchased_order()
	{
		$order_id = $this->input->post('order');
		$action = $this->input->post('action');

		$this->db->where('id', $order_id);
		$data = [
			'purchased' => $action
		];
		if ($this->db->update('tbl_orders', $data)) {
			$resp = [
				'update' => 'success',
				'action' => $action
			];
			echo json_encode($resp);
		}
	}


	# Implementando paginação #######################################################
	private function check_order_status($order_id)
	{
		if($order_id == 5){
			return 'Pronto para Enviar';
		}
		// Obtém o status do pedido
		$query = $this->db->select('status')
			->where('order_id', $order_id)
			->get('tbl_purchase_details')
			->result(); // Obtém o resultado da consulta como um array de objetos

		// Verifica se a query retornou algum resultado
		if (empty($query)) {
			// Caso não encontre nenhum registro
			return 'Aberta';
		}

		$items = $this->db->select('status_id')
			->where('order_id', $order_id)
			->get('tbl_purchase_details_itens')
			->result();

		// Concatena as siglas dos status dos itens
		$siglas = array();
		foreach ($items as $item) {
			$siglas[] = $this->get_sigla_status($item->status_id);
		}

		// Verifica se há siglas concatenadas
		$complemento = !empty($siglas) ? ' - ' . implode('/', $siglas) : '';

		// Obtém a única linha de resultado
		$row = $query[0]; // Obtém a única linha como um objeto (assumindo que há apenas um resultado)

		// Verifica o status_id e retorna o valor correspondente
		switch ($row->status) {
			case 1:
				return 'Aberta' . $complemento;
			case 2:
				return 'Concluída';
			case 3:
				return 'Cancelada';
			case 4:
				return 'Em separação';
			case 5:
				return 'Pronto para Enviar';
			case 6:
				return 'Enviado';
			case 7:
				return 'Entregue';
			default:
				return 'Aberta'; // Ou qualquer outro valor padrão se necessário
		}

	}


	private function get_sigla_status($status)
	{
		switch ($status) {
			case 1:
				return 'SS';
			case 2:
				return 'CO';
			case 3:
				return 'PG';
			case 4:
				return 'PP';
			case 5:
				return 'SE';
			default:
				return 'AB'; // Ou qualquer outro valor padrão se necessário
		}
	}

	public function get_orders_old()
	{
		$draw = intval($this->input->post("draw"));
		$start = intval($this->input->post("start"));
		$length = intval($this->input->post("length"));
		$search = $this->input->post("search")['value'];
		$order_column = intval($this->input->post("order")[0]['column']);
		$order_dir = $this->input->post("order")[0]['dir'];

		$columns = [
			null,
			null,
			'reference',
			'created_date',
			'items',
			'channel',
			'user_id',
			'total_price',
			null,
			null
		];

		$order_by = isset($columns[$order_column]) ? $columns[$order_column] : 'id';

		$this->db->select('ord.id, ord.reference, ord.created_date, ord.items, ord.channel, ord.country, ord.user_id, ord.state, ord.total_price, ord.created_date, pd.status');
		$this->db->from('tbl_orders ord');

		// JOIN com a tabela tbl_purchase_details (sempre presente)
		$this->db->join('tbl_purchase_details pd', 'pd.order_id = ord.id', 'left');

		$status_text = strtolower(trim($search));
		$status_value = null;

		$status_map = [
			'aberta'             => 1, 'abertas'            => 1, 'aberto'             => 1, 'pendente'           => 1,
			'concluída'          => 2, 'concluido'          => 2, 'finalizada'         => 2, 'finalizado'         => 2, 'resolvido'          => 2,
			'cancelada'          => 3, 'cancelado'          => 3, 'anulado'            => 3, 'anulada'            => 3, 'anuladas'           => 3,
			'em separação'       => 4, 'separando'          => 4, 'separação'          => 4, 'em preparação'      => 4, 'preparando'         => 4,
			'pronto para enviar' => 5, 'pronto envio'       => 5, 'preparado'          => 5, 'pronto'             => 5,
			'enviado'            => 6, 'remetido'           => 6, 'despachado'         => 6,
			'entregue'           => 7, 'recebido'           => 7, 'finalizado'         => 7, 'chegou'             => 7
		];

		if (isset($status_map[$status_text])) {
			if ($status_map[$status_text] == 1) {
				// Verifica se a ordem está na tbl_purchase_details com status = 1
				$this->db->where('pd.status', 1);

				// Agora faz o JOIN com tbl_purchase_details_itens e só retorna se não houver registros lá
				$this->db->join('tbl_purchase_details_itens pdi', 'pdi.order_id = ord.id', 'left');
				$this->db->where('pdi.order_id IS NULL'); // Só retorna se não houver registros na tbl_purchase_details_itens
			} else {
				$status_value = $status_map[$status_text];
			}
		}

		$list_users_id = $this->LIST_USERS_ID;
		$this->db->where_in('ord.user_id', $list_users_id);

		$start_date = '2024-08-01 00:00:00';
		$this->db->where('ord.created_date >=', $start_date);

		$date = DateTime::createFromFormat('d/m/Y', $search);
		$search_date = $date ? $date->format('Y-m-d') : '';

		if (!empty($search) && (!isset($status_map[$status_text]) || $status_map[$status_text] != 1)) {
			$this->db->group_start();

			$this->db->like('ord.reference', $search);
			$this->db->or_like('ord.channel', $search);

			if ($search_date) {
				$this->db->or_like('DATE(ord.created_date)', $search_date);
			}

			$this->db->group_end();
		}

		$this->db->order_by('ord.created_at', 'DESC');

		$totalFiltered = $this->db->count_all_results('', FALSE);
		$this->db->limit($length, $start);

		$query = $this->db->get();
		$orders = $query->result();




		$data = array();
		foreach ($orders as $order) {
			$items_count = count(json_decode($order->items));
			$channel_country = $order->channel . '/' . $order->country;

			$actions = "<a class='link-modal-dados-order btn btn-info btn-sm new-button' onclick='setId($order->id)' href='" . base_url() . "order-details-purchase/$order->id' data-bs-toggle='modal' data-bs-target='#modal-orders-purchase'>Detalhes</a>";

			$data[] = array(
				"",
				$actions,
				$order->reference,
				date("d/m/Y", strtotime($order->created_date)),
				$items_count,
				$channel_country,
				$order->user_id,
				$order->total_price,
				$this->check_order_status($order->id)
			);
		}

		$result = array(
			"draw" => $draw,
			"recordsTotal" => $this->get_total_orders(),
			"recordsFiltered" => $totalFiltered,
			"data" => $data
		);

		echo json_encode($result);
	}

	/**
	 * Backup da versão anterior da função get_orders - mantida para referência
	 */
	public function get_orders_backup()
	{
		// Inicialização de variáveis a partir dos dados POST
		$draw = intval($this->input->post("draw"));
		$start = intval($this->input->post("start"));
		$length = intval($this->input->post("length"));
		$search = $this->input->post("search")['value'];
		$order_column = intval($this->input->post("order")[0]['column']);
		$order_dir = $this->input->post("order")[0]['dir'];

		// Definição das colunas disponíveis para ordenação
		$columns = [
			null,
			null,
			'reference',
			'created_date',
			'items',
			'channel',
			'user_id',
			'total_price',
			null,
			null
		];

		// Determina a coluna de ordenação
		$order_by = isset($columns[$order_column]) ? $columns[$order_column] : 'id';

		// Consulta principal - seleção de campos com aliases claros
		$this->db->select('
			ord.id,
			ord.reference,
			ord.created_date,
			ord.items,
			ord.channel,
			ord.country,
			ord.user_id,
			ord.state,
			ord.total_price,
			COALESCE(pd.status, 0) as pd_status
		');
		$this->db->from('tbl_orders ord');

		// JOIN com a tabela tbl_purchase_details - usando LEFT JOIN para garantir que todos os pedidos sejam retornados
		$this->db->join('tbl_purchase_details pd', 'pd.order_id = ord.id', 'left');

		// Processamento do termo de busca para verificar se é um status
		$status_text = strtolower(trim($search));

		// Mapeamento de termos de status para valores numéricos
		$status_map = [
			'aberta'             => 1, 'abertas'            => 1, 'aberto'             => 1, 'pendente'           => 1,
			'concluída'          => 2, 'concluido'          => 2, 'finalizada'         => 2, 'finalizado'         => 2, 'resolvido'          => 2,
			'cancelada'          => 3, 'cancelado'          => 3, 'anulado'            => 3, 'anulada'            => 3, 'anuladas'           => 3,
			'em separação'       => 4, 'separando'          => 4, 'separação'          => 4, 'em preparação'      => 4, 'preparando'         => 4,
			'pronto para enviar' => 5, 'pronto envio'       => 5, 'preparado'          => 5, 'pronto'             => 5,
			'enviado'            => 6, 'remetido'           => 6, 'despachado'         => 6,
			'entregue'           => 7, 'recebido'           => 7, 'finalizado'         => 7, 'chegou'             => 7
		];

		// Filtro por status, se aplicável
		if (isset($status_map[$status_text])) {
			$status_value = $status_map[$status_text];

			if ($status_value == 1) {
				// Caso especial para status "aberta" - pedidos com status 1 e sem itens
				$this->db->group_start();

				// Opção 1: Pedidos com status 1 explícito
				$this->db->group_start();
				$this->db->where('pd.status', 1);

				// Subquery para verificar se não há itens para este pedido
				$subquery = $this->db->select('1')
									->from('tbl_purchase_details_itens pdi')
									->where('pdi.order_id = ord.id')
									->get_compiled_select();

				$this->db->where("NOT EXISTS ($subquery)", null, false);
				$this->db->group_end();

				// Opção 2: Pedidos sem registro em tbl_purchase_details (NULL)
				$this->db->or_where('pd.status IS NULL', null, false);

				$this->db->group_end();
			} else {
				// Para outros status, filtro direto
				$this->db->where('pd.status', $status_value);
			}
		}

		// Filtro por fornecedores específicos
		$list_users_id = $this->LIST_USERS_ID;
		if (!empty($list_users_id)) {
			$this->db->where_in('ord.user_id', $list_users_id);
		} else {
			// Log ou tratamento para caso a lista esteja vazia
			log_message('error', 'LIST_USERS_ID está vazio na função get_orders_new');
		}

		// Filtro por data de criação
		$start_date = '2025-01-01 00:00:00';
		$this->db->where('ord.created_date >=', $start_date);

		// Processamento de busca por texto (referência, canal ou data)
		if (!empty($search) && (!isset($status_map[$status_text]))) {
			// Tenta converter a busca para formato de data
			$date = DateTime::createFromFormat('d/m/Y', $search);
			$search_date = $date ? $date->format('Y-m-d') : '';

			$this->db->group_start();

			// Busca por referência
			$this->db->like('ord.reference', $search);

			// Busca por canal
			$this->db->or_like('ord.channel', $search);

			// Busca por data
			if ($search_date) {
				$this->db->or_like('DATE(ord.created_date)', $search_date);
			}

			$this->db->group_end();
		}

		// Ordenação pelo ID da tabela em ordem decrescente
		$this->db->order_by('ord.id', 'DESC');

		// Contagem de registros filtrados
		$totalFiltered = $this->db->count_all_results('', FALSE);

		// Aplicação de limites para paginação
		$this->db->limit($length, $start);

		// Execução da consulta
		$query = $this->db->get();

		// Log da consulta SQL para depuração
		log_message('debug', 'SQL Query: ' . $this->db->last_query());

		// Processamento dos resultados
		$orders = $query->result();
		$data = array();

		foreach ($orders as $order) {
			// Tratamento seguro para JSON inválido
			try {
				$items_json = json_decode($order->items);
				$items_count = is_array($items_json) || is_object($items_json) ? count($items_json) : 0;
			} catch (Exception $e) {
				log_message('error', 'Erro ao decodificar JSON de itens: ' . $e->getMessage());
				$items_count = 0;
			}

			$channel_country = $order->channel . '/' . $order->country;

			// Botões de ação
			$actions = "<a class='link-modal-dados-order btn btn-info btn-sm new-button' onclick='setId($order->id)' href='" . base_url() . "order-details-purchase/$order->id' data-bs-toggle='modal' data-bs-target='#modal-orders-purchase'>Detalhes</a>";

			// Formatação da data com verificação de validade
			$created_date = $order->created_date ? date("d/m/Y", strtotime($order->created_date)) : 'Data inválida';

			// Obtenção do status com tratamento de erro
			try {
				$status = $this->check_order_status_new($order->id, $order->pd_status);
			} catch (Exception $e) {
				log_message('error', 'Erro ao obter status do pedido: ' . $e->getMessage());
				$status = 'Erro ao obter status';
			}

			// Montagem do array de dados
			$data[] = array(
				"",
				$actions,
				$order->reference,
				$created_date,
				$items_count,
				$channel_country,
				$order->user_id,
				$order->total_price,
				$status
			);
		}

		// Montagem do resultado final
		$result = array(
			"draw" => $draw,
			"recordsTotal" => $this->get_total_orders(),
			"recordsFiltered" => $totalFiltered,
			"data" => $data
		);

		// Retorno como JSON
		echo json_encode($result);
	}

	/**
	 * Função get_orders otimizada com tratamento aprimorado para JOINs, valores NULL e melhor manipulação de erros
	 * Esta versão substitui a função anterior mantendo compatibilidade total com o código existente
	 */
	public function get_orders()
	{
		try {
			// Inicialização de variáveis a partir dos dados POST
			$draw = intval($this->input->post("draw"));
			$start = intval($this->input->post("start"));
			$length = intval($this->input->post("length"));
			$search = $this->input->post("search")['value'];
			$order_column = intval($this->input->post("order")[0]['column']);
			$order_dir = $this->input->post("order")[0]['dir'];

			// Definição das colunas disponíveis para ordenação
			$columns = [
				null,
				null,
				'reference',
				'created_date',
				'items',
				'channel',
				'user_id',
				'total_price',
				null,
				null
			];

			// Determina a coluna de ordenação
			$order_by = isset($columns[$order_column]) ? $columns[$order_column] : 'id';

			// Consulta principal - seleção de campos com aliases claros
			$this->db->select('
				ord.id,
				ord.reference,
				ord.created_date,
				ord.items,
				ord.channel,
				ord.country,
				ord.user_id,
				ord.state,
				ord.total_price,
				COALESCE(pd.status, 0) as pd_status
			');
			$this->db->from('tbl_orders ord');

			// JOIN com a tabela tbl_purchase_details - usando LEFT JOIN para garantir que todos os pedidos sejam retornados
			$this->db->join('tbl_purchase_details pd', 'pd.order_id = ord.id', 'left');

			// Processamento do termo de busca para verificar se é um status
			$status_text = strtolower(trim($search));

			// Mapeamento de termos de status para valores numéricos
			$status_map = [
				'aberta'             => 1, 'abertas'            => 1, 'aberto'             => 1, 'pendente'           => 1,
				'concluída'          => 2, 'concluido'          => 2, 'finalizada'         => 2, 'finalizado'         => 2, 'resolvido'          => 2,
				'cancelada'          => 3, 'cancelado'          => 3, 'anulado'            => 3, 'anulada'            => 3, 'anuladas'           => 3,
				'em separação'       => 4, 'separando'          => 4, 'separação'          => 4, 'em preparação'      => 4, 'preparando'         => 4,
				'pronto para enviar' => 5, 'pronto envio'       => 5, 'preparado'          => 5, 'pronto'             => 5,
				'enviado'            => 6, 'remetido'           => 6, 'despachado'         => 6,
				'entregue'           => 7, 'recebido'           => 7, 'finalizado'         => 7, 'chegou'             => 7
			];

			// Filtro por status, se aplicável
			if (isset($status_map[$status_text])) {
				$status_value = $status_map[$status_text];

				if ($status_value == 1) {
					// Caso especial para status "aberta" - pedidos com status 1 e sem itens
					$this->db->group_start();

					// Opção 1: Pedidos com status 1 explícito
					$this->db->group_start();
					$this->db->where('pd.status', 1);

					// Subquery para verificar se não há itens para este pedido
					$subquery = $this->db->select('1')
										->from('tbl_purchase_details_itens pdi')
										->where('pdi.order_id = ord.id')
										->get_compiled_select();

					$this->db->where("NOT EXISTS ($subquery)", null, false);
					$this->db->group_end();

					// Opção 2: Pedidos sem registro em tbl_purchase_details (NULL)
					$this->db->or_where('pd.status IS NULL', null, false);

					$this->db->group_end();
				} else {
					// Para outros status, filtro direto
					$this->db->where('pd.status', $status_value);
				}
			}

			// Filtro por fornecedores específicos
			$list_users_id = $this->LIST_USERS_ID;
			if (!empty($list_users_id)) {
				$this->db->where_in('ord.user_id', $list_users_id);
			} else {
				// Log ou tratamento para caso a lista esteja vazia
				log_message('error', 'LIST_USERS_ID está vazio na função get_orders');
			}

			// Filtro por data de criação
			$start_date = '2025-01-01 00:00:00';
			$this->db->where('ord.created_date >=', $start_date);

			// Processamento de busca por texto (referência, canal ou data)
			if (!empty($search) && (!isset($status_map[$status_text]))) {
				// Tenta converter a busca para formato de data
				$date = DateTime::createFromFormat('d/m/Y', $search);
				$search_date = $date ? $date->format('Y-m-d') : '';

				$this->db->group_start();

				// Busca por referência
				$this->db->like('ord.reference', $search);

				// Busca por canal
				$this->db->or_like('ord.channel', $search);

				// Busca por data
				if ($search_date) {
					$this->db->or_like('DATE(ord.created_date)', $search_date);
				}

				$this->db->group_end();
			}

			// Ordenação pelo ID da tabela em ordem decrescente
			$this->db->order_by('ord.id', 'DESC');

			// Contagem de registros filtrados
			$totalFiltered = $this->db->count_all_results('', FALSE);

			// Aplicação de limites para paginação
			$this->db->limit($length, $start);

			// Execução da consulta
			$query = $this->db->get();

			// Log da consulta SQL para depuração
			log_message('debug', 'SQL Query get_orders: ' . $this->db->last_query());

			// Processamento dos resultados
			$orders = $query->result();
			$data = array();

			foreach ($orders as $order) {
				// Tratamento seguro para JSON inválido
				try {
					$items_json = json_decode($order->items);
					$items_count = is_array($items_json) || is_object($items_json) ? count($items_json) : 0;
				} catch (Exception $e) {
					log_message('error', 'Erro ao decodificar JSON de itens: ' . $e->getMessage());
					$items_count = 0;
				}

				$channel_country = $order->channel . '/' . $order->country;

				// Botões de ação
				$actions = "<a class='link-modal-dados-order btn btn-info btn-sm new-button' onclick='setId($order->id)' href='" . base_url() . "order-details-purchase/$order->id' data-bs-toggle='modal' data-bs-target='#modal-orders-purchase'>Detalhes</a>";

				// Formatação da data com verificação de validade
				$created_date = $order->created_date ? date("d/m/Y", strtotime($order->created_date)) : 'Data inválida';

				// Obtenção do status com tratamento de erro
				try {
					$status = $this->check_order_status_new($order->id, $order->pd_status);
				} catch (Exception $e) {
					log_message('error', 'Erro ao obter status do pedido: ' . $e->getMessage());
					$status = 'Erro ao obter status';
				}

				// Montagem do array de dados
				$data[] = array(
					"",
					$actions,
					$order->reference,
					$created_date,
					$items_count,
					$channel_country,
					$order->user_id,
					$order->total_price,
					$status
				);
			}

			// Montagem do resultado final
			$result = array(
				"draw" => $draw,
				"recordsTotal" => $this->get_total_orders(),
				"recordsFiltered" => $totalFiltered,
				"data" => $data
			);

			// Retorno como JSON
			echo json_encode($result);

		} catch (Exception $e) {
			log_message('error', 'Erro na função get_orders: ' . $e->getMessage());

			// Retorna uma resposta de erro em caso de falha
			$error_result = array(
				"draw" => intval($this->input->post("draw")),
				"recordsTotal" => 0,
				"recordsFiltered" => 0,
				"data" => array(),
				"error" => "Erro ao carregar dados"
			);

			echo json_encode($error_result);
		}
	}

	private function get_total_orders()
	{
		$this->db->select("COUNT(*) as num");
		$this->db->from("tbl_orders");

		$list_users_id = $this->LIST_USERS_ID;

		$this->db->where_in('user_id', $list_users_id);

		$start_date = '2024-08-01 00:00:00';
		$this->db->where('created_date >=', $start_date);

		$query = $this->db->get();
		$result = $query->row();
		return $result->num;
	}

	/**
	 * Versão melhorada da função check_order_status com tratamento de erros
	 *
	 * @param int $order_id ID do pedido
	 * @param int $pd_status Status do pedido na tabela tbl_purchase_details (pode ser NULL)
	 * @return string Status formatado do pedido
	 */
	private function check_order_status_new($order_id, $pd_status = 0)
	{
		// Caso especial para pedidos específicos
		if ($order_id == 5) {
			return 'Pronto para Enviar';
		}

		// Se não tiver status definido na tabela tbl_purchase_details
		if (empty($pd_status)) {
			return 'Aberta';
		}

		// Busca os status dos itens com tratamento de erro
		try {
			$items = $this->db->select('status_id')
				->where('order_id', $order_id)
				->get('tbl_purchase_details_itens')
				->result();

			// Concatena as siglas dos status dos itens
			$siglas = array();
			foreach ($items as $item) {
				if (isset($item->status_id)) {
					$siglas[] = $this->get_sigla_status($item->status_id);
				}
			}

			// Verifica se há siglas concatenadas
			$complemento = !empty($siglas) ? ' - ' . implode('/', $siglas) : '';

			// Retorna o status baseado no valor de pd_status
			switch ($pd_status) {
				case 1:
					return 'Aberta' . $complemento;
				case 2:
					return 'Concluída';
				case 3:
					return 'Cancelada';
				case 4:
					return 'Em separação';
				case 5:
					return 'Pronto para Enviar';
				case 6:
					return 'Enviado';
				case 7:
					return 'Entregue';
				default:
					return 'Aberta'; // Valor padrão
			}
		} catch (Exception $e) {
			log_message('error', 'Erro ao obter status dos itens: ' . $e->getMessage());
			return 'Status indisponível';
		}
	}

	private function getStateClass($state)
	{
		$statePositive = array('RECEIVED', 'TO_COLLECT', 'SHIPPED');
		$stateNegative = array('WAITING_ACCEPTANCE', 'WAITING_DEBIT', 'WAITING_DEBIT_PAYMENT', 'CLOSED', 'REFUNDED', 'CANCELED', 'REFUSED', 'AUTO_REFUSED', 'AUTO_CLOSED');
		$stateNeuter = array('SHIPPING');

		if (in_array($state, $statePositive)) {
			return 'badge-success badge-sm';
		} else if (in_array($state, $stateNegative)) {
			return 'badge-negative badge-sm';
		} else if (in_array($state, $stateNeuter)) {
			return 'badge-neuter badge-sm';
		}
		return '';
	}

	private function getStateClass_old($state)
	{
		$state_neuter = ['SHIPPING'];
		$state_positive = ['RECEIVED', 'TO_COLLECT', 'SHIPPED'];
		$state_negative = ['WAITING_ACCEPTANCE', 'WAITING_DEBIT', 'WAITING_DEBIT_PAYMENT', 'CLOSED', 'REFUNDED', 'CANCELED', 'REFUSED', 'AUTO_REFUSED', 'AUTO_CLOSED'];

		if (in_array($state, $state_positive)) {
			return 'badge-success badge-sm';
		} elseif (in_array($state, $state_negative)) {
			return 'badge-negative badge-sm';
		} elseif (in_array($state, $state_neuter)) {
			return 'badge-neuter badge-sm';
		} else {
			return '';
		}
	}

	public function get_labels()
	{
		$data = json_decode(file_get_contents('php://input'), true);

		$curl = curl_init();

		curl_setopt_array($curl, array(
			CURLOPT_URL => 'https://prd-mkp-1.bighub.store/app-logistic/api/label',
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => '',
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 0,
			CURLOPT_FOLLOWLOCATION => true,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => 'POST',
			CURLOPT_POSTFIELDS => $data,
			CURLOPT_HTTPHEADER => array(
				'Content-Type: application/json',
				'Authorization: Basic YmlnaHViX2xvZ2lzdGljOmZXeCVHa0AzclAheg=='
			),
		));

		$response = curl_exec($curl);

		curl_close($curl);

		echo $response;
	}

	public function get_tracking()
	{
		$data = json_encode(['order_number' => json_decode(file_get_contents('php://input'), true)]);

		return $this->curlGetTracking($data);
	}

	public function get_origins()
	{
		$curl = curl_init();

		curl_setopt_array($curl, [
			CURLOPT_URL => "https://panel.sendcloud.sc/api/v2/user/addresses/sender",
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => "",
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 30,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => "GET",
			CURLOPT_HTTPHEADER => [
				"Accept: application/json",
				"Authorization: Basic YWFjYjMyMTEtZDA1OC00MTc1LWE1NmItZDUzMzlmNmFiZTJhOjkzYzBmNmJhM2ZlNTRmMGE4NGExYjRjMDE2MWQyODc5"
			],
		]);

		$response = curl_exec($curl);
		$err = curl_error($curl);

		curl_close($curl);

		if ($err) {
			echo "cURL Error #:" . $err;
		} else {
			echo $response;
		}
	}

	public function shippment()
	{
		$data = json_decode(file_get_contents('php://input'), true);

		$dataCurlGetTracking = json_encode(['order_number' => $data['order_number_in_logistic']]);
		$getTrackingInLogistic = $this->curlGetTracking($dataCurlGetTracking);

		$trackingData = json_decode($getTrackingInLogistic, true);

		$this->db->select('carriers.id carrierId, countries.id countryId, channels.id channelId, carriers.carrier_name ');
		$this->db->from('tbl_carriers carriers');
		$this->db->like('carriers.carrier_name', $data['carrier']);
		$this->db->where('carriers.channel', $data['channel']);
		$this->db->where('carriers.country', $data['country']);
		$this->db->join('tbl_countries countries', 'carriers.country = countries.code', 'right');
		$this->db->join('tbl_channels channels', 'carriers.channel = channels.code', 'right');

		$dataToShip = $this->db->get()->result();

		$dataToLogistic = [
			"carrier_id" => (int)$dataToShip[0]->carrierId,
			"channel_id" => (int)$dataToShip[0]->channelId,
			"country_id" => (int)$dataToShip[0]->countryId,
			"reference" => $data['order_number'],
			"tracking_number" => $trackingData['tracking_number']
		];

		$user = $this->db->where('id', 33)->get('tbl_user_clients')->row();
		$token = $this->curl_impersonate($user->user_token);

		$this->curlBighubLogistic($dataToLogistic, $token->data->api_token);

		$dataToOrderShipment = [
			"company" => $dataToShip[0]->carrier_name,
			"price" => 0,
			"tracking" => $trackingData['tracking_number'],
			"zone" => ""
		];

		$this->curlBighubOrderShipment($data['order_number'], $dataToOrderShipment, $token->data->api_token);
	}

	private function curlBighubLogistic($data, $token)
	{
		$curl = curl_init();

		curl_setopt_array($curl, array(
			CURLOPT_URL => "https://app.bighub.store/api/v2/logistics/trackings?token=$token",
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => '',
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 0,
			CURLOPT_FOLLOWLOCATION => true,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => 'POST',
			CURLOPT_POSTFIELDS => json_encode($data),
			CURLOPT_HTTPHEADER => array(
				'Content-Type: application/json'
			),
		));

		$response = curl_exec($curl);

		curl_close($curl);
	}

	private function curlBighubOrderShipment($order, $data, $token)
	{
		$this->db->select('*');
		$this->db->from('tbl_orders');
		$this->db->where('reference', $order);
		$orders = $this->db->get()->result();

		$orderId = $orders[0]->id;
		$curl = curl_init();

		curl_setopt_array($curl, array(
			CURLOPT_URL => "https://app.bighub.store/api/v2/orders/{$orderId}/shipment?token={$token}",
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => '',
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 0,
			CURLOPT_FOLLOWLOCATION => true,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => 'PUT',
			CURLOPT_POSTFIELDS => json_encode($data),
			CURLOPT_HTTPHEADER => array(
				'Content-Type: application/json'
			),
		));

		$response = curl_exec($curl);

		curl_close($curl);
	}

	private function curlGetTracking($data)
	{
		$curl = curl_init();

		curl_setopt_array($curl, array(
			CURLOPT_URL => "https://prd-mkp-1.bighub.store/app-logistic/api/label/get-tracking",
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => '',
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 0,
			CURLOPT_FOLLOWLOCATION => true,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => 'POST',
			CURLOPT_POSTFIELDS => $data,
			CURLOPT_HTTPHEADER => array(
				'Content-Type: application/json',
				'Authorization: Basic YmlnaHViX2xvZ2lzdGljOmZXeCVHa0AzclAheg=='
			),
		));

		$response = curl_exec($curl);

		curl_close($curl);

		return $response;
	}



	# INICIO DA PARTE LOGISTICA
	################################################################################


	public function list_orders_logistics()
	{
		$data = $this->_getDataDashboard();
		$data += $this->_getRespAction();

		$data['session'] = 'purchase-orders';
		$data['view'] = 'logistic/logistics/list_orders';
		$this->load->view('includes/template', $data);
	}

	public function get_logistics_orders()
	{
		$draw = intval($this->input->post("draw"));
		$start = intval($this->input->post("start"));
		$length = intval($this->input->post("length"));
		$search = $this->input->post("search")['value'];
		$order_column = intval($this->input->post("order")[0]['column']);
		$order_dir = $this->input->post("order")[0]['dir'];

		$columns = [
			null,
			null,
			'reference',
			'created_date',
			'items',
			'channel',
			'user_id',
			'total_price',
			null,
			null
		];

		$this->db->select('*');
		$this->db->where('status', 2);
		$result = $this->db->get('tbl_purchase_details')->result();
		$order_ids = array_column($result, 'order_id');

		$order_by = isset($columns[$order_column]) ? $columns[$order_column] : 'id';

		$this->db->select('ord.id, ord.reference, ord.created_date, ord.items, ord.channel, ord.country, ord.user_id, ord.state, ord.total_price, ord.created_date');
		$this->db->from('tbl_orders ord');
		$this->db->where_in('ord.id', $order_ids);

		// Verifica se $search está no formato DD/MM/YYYY e converte para YYYY-MM-DD
		$date = DateTime::createFromFormat('d/m/Y', $search);
		$search_date = $date ? $date->format('Y-m-d') : '';

		if (!empty($search)) {
			$this->db->group_start(); // Inicia um grupo de condições

			// Busca por referência
			$this->db->like('ord.reference', $search);

			// Busca por canal
			$this->db->or_like('ord.channel', $search);

			// Busca pela data (apenas a parte da data)
			if ($search_date) {
				$this->db->or_like('DATE(ord.created_date)', $search_date);
			}

			$this->db->group_end(); // Finaliza o grupo de condições
		}


		$this->db->order_by('ord.id', 'DESC');

		$totalFiltered = $this->db->count_all_results('', FALSE);

		$this->db->limit($length, $start);
		#$this->db->order_by($order_by, $order_dir);

		$query = $this->db->get();
		$orders = $query->result();

		$data = array();
		foreach ($orders as $order) {
			$items_count = count(json_decode($order->items));
			$channel_country = $order->channel . '/' . $order->country;

			$actions = "<a class='link-modal-dados-order btn btn-info btn-sm new-button' onclick='setId($order->id)' href='" . base_url() . "order-details-logistic/$order->id' data-bs-toggle='modal' data-bs-target='#modal-logistic'>Detalhes</a>";
			$actions .= "<a class='link-modal-dados-ship btn btn-secondary btn-sm new-button' style='left: 2px' onclick='setId($order->id)' href='" . base_url() . "order-shipment-details/$order->id' data-bs-toggle='modal' data-bs-target='#modal-dados-ship'>Shipment</a>";

			if($order->id == 2){
				$order_id = 5;
			}

			$data[] = array(
				"",
				$actions,
				$order->reference,
				date("d/m/Y", strtotime($order->created_date)),
				$items_count,
				$channel_country,
				$order->user_id,
				$order->total_price,
				$this->check_order_status(5)
			);
		}

		$result = array(
			"draw" => $draw,
			"recordsTotal" => $this->get_total_logistics_orders($order_ids),
			"recordsFiltered" => $totalFiltered,
			"data" => $data
		);

		echo json_encode($result);
	}

	private function get_total_logistics_orders($order_ids)
	{
		$this->db->select("COUNT(*) as num");
		$this->db->from("tbl_orders");
		$this->db->where_in('id', $order_ids);

		$query = $this->db->get();
		$result = $query->row();
		return $result->num;
	}

	public function order_details_logistic($id)
	{
		$data = $this->_getDataDashboard();

		$this->create_line_order($id);
		// echo json_encode($data);
		// // die();

		$query = $this->db->where('id', $id)->get('tbl_orders');
		$resp = $query->row();

		$address = $resp->customer;
		$data = json_decode($address, true);

		$billingAddress = $data['billing_address'];
		$shippingAddress = $data['shipping_address'];

		$img_default = 'https://cdn.bighub.store/image/product-placeholder.png';
		$image = $img_default;

		#$data['attachment'] = $resp->attachment;
		$itens = json_decode($resp->items);
		$total_taxes = 0;

		$state_neuter = ['SHIPPING'];
		$state_positive = ['RECEIVED', 'TO_COLLECT', 'SHIPPED'];
		$state_negative = ['WAITING_ACCEPTANCE', 'WAITING_DEBIT', 'WAITING_DEBIT_PAYMENT', 'CLOSED', 'REFUNDED', 'CANCELED', 'REFUSED', 'AUTO_REFUSED', 'AUTO_CLOSED'];
		$state_class = 'state-neutra';

		$total_cost_price = 0;

		foreach ($itens as $key => $item) {

			// echo json_decode($item->finance);
			// die();

			$taxes = $item->taxes[0]->amount ?? 0;
			$total_taxes = $total_taxes + $taxes;

			if (in_array($item->state, $state_positive)) {
				$state_class = 'state-positive';
			} elseif (in_array($item->state, $state_negative)) {
				$state_class = 'state-negative';
			} elseif (in_array($item->state, $state_neuter)) {
				$state_class = 'state-neuter';
			}

			if (empty($item->image) || $item->image != 'N/A') {
				$image = $item->image;
			}

			$this->db->select('*');
			$this->db->where('order_id', $id);
			$this->db->where('item_id', $key);
			$this->db->from('tbl_purchase_details_itens');
			$itemRow = $this->db->get()->row();

			if (isset($itemRow->cost_price)) {
				$cost_price_item = $itemRow->cost_price;
			} else {
				$cost_price_item = $this->get_price_supplier($item->ean, $resp->user_id);
			}
			$total_cost_price = $total_cost_price + $cost_price_item;

			######################################################################
			# Verify supplier
			# COnsulta base de dados e ver se já existe json com suppliers

			# Se sim apenas devolver para front end

			# Se não, fazer a consulta, gravar na base de dados e devolver para o frontend.
			$suppliers = $this->get_supplier($item->ean);



			######################################################################


			$new_itens[$key] = [
				'title' => $item->title,
				'ean' => $item->ean,
				'sku' => $item->sku,
				'image' => $image,
				'image_real' => $item->image,
				'state' => $item->state,
				'class_state' => $state_class,
				'quantity' => $item->quantity,
				'leadtime_to_ship' => $item->leadtime_to_ship,
				'shipping_price' => $item->shipping_price,
				'taxe_amount' => $taxes,
				'commission_fee' => $item->commission_fee,
				'commission_rate_vat' => $item->commission_rate_vat,
				'total_commission' => $item->total_commission,
				'total_price' => $item->total_price,
				'total_price_no_taxe' => round($item->total_price - $taxes, 2),
				'price_unit' => $item->price_unit,
				'cost_price' => $cost_price_item,
				'supplier_id' => $itemRow->supplier_id ?? '',
				'status_id' => $itemRow->status_id ?? '',
				'obs_item' => $itemRow->obs_item ?? '',
				'finance' => json_encode($item->finance) ?? '',
				'invoice' => false,
				'link_invoice' => 'no_link',
				'suppliers' => json_encode($suppliers)
			];

			if ($this->check_invoice_exist($id . '_' . $key . '.pdf')) {
				$new_itens[$key]['link_invoice'] = base_url('/uploads/' . $id . '_' . $key . '.pdf');
				$new_itens[$key]['invoice'] = true;
			}
		}

		#https://materialui.co/icons

		$data['total_cost_price'] = $total_cost_price;

		$data['items'] = json_decode(json_encode($new_itens));
		$data['total_taxes'] = $total_taxes;

		$data['id_order'] = $id;
		$data['seller'] = $this->get_seller(json_decode($resp->user_id));
		$data['customer'] = json_decode($resp->customer);
		$data['billing_address'] = $billingAddress;
		$data['shipping_address'] = $shippingAddress;
		$data['order_items'] = count(json_decode($resp->items));

		$data['payment'] = json_decode($resp->payment)->type ?? 'Awaiting payment';
		$data['order_data'] = $resp;


		// # Busacar todo o historico referente a essa order
		// $data['purchase_history'] = $this->get_purchase_history($id);

		# Buscar todos os detalhes de interação dessa compra
		$data['purchase_details'] = $this->get_purchase_details($id);

		$data['finance_order'] = json_decode($resp->finance);

		$data['price_cost'] = 0.00;

		# Get carriers conforme order id

		$this->db->select('*');
		$this->db->from('tbl_carriers');
		$this->db->where('channel', $data['order_data']->channel);
		$this->db->where('country', $data['order_data']->country);
		$data['carriers'] = $this->db->get()->result();

		#var_dump($data['order_data']);

		$this->db->select('*');
		$this->db->from('tbl_suppliers');
		$data['suppliers'] = $this->db->get()->result();

		$this->db->select('*');
		$this->db->from('tbl_usuarios');
		$this->db->where('ativo', 1);
		$data['users'] = $this->db->get()->result();

		$this->load->view('logistic/logistics/modal-order-details.php', $data);
	}


	# SEND TRACKING NUMBER
	################################################################################

	public function send_tracking_number()
	{
		$user_id = $this->input->post("user_id");
		$reference = $this->input->post("reference");
		$carrier_id = $this->input->post("carrier_id");
		$tracking_number = $this->input->post("tracking_number");

		// $user_id = 190;
		// $reference = '12344-A';
		// $carrier_id = 6577;
		// $tracking_number = '121212121212';

		# Vai receber o id da tabela tbl_carriers e pegar e fazer uma consulta
		$this->db->select('*');
		$this->db->from('tbl_carriers');
		$this->db->where('id', $carrier_id);
		$carrier = $this->db->get()->row();

		# Buscar o country da tabela tbl_countries
		$this->db->select('*');
		$this->db->from('tbl_countries');
		$this->db->where('code', $carrier->country);
		$country_id = $this->db->get()->row()->id;

		# Buscar o country da tabela tbl_channels
		$this->db->select('*');
		$this->db->from('tbl_channels');
		$this->db->where('code', $carrier->channel);
		$channel_id = $this->db->get()->row()->id;


		$payload = [
			"user_id" => $user_id,
			"reference" => $reference,
			"tracking_number" => $tracking_number,

			"carrier_id" => $carrier_id,
			"channel_id" => floatval($channel_id),
			"country_id" => floatval($country_id)
		];

		echo json_encode($payload);
	}


	# Function send payload para backend
	public function send_tracking_endpoint($payload)
	{
		// URL da API
		$url_api = 'https://app.bighub.store/api/v2/logistics/trackings';

		$handle  = curl_init($url_api);

		$request_headers = [];
		$request_headers[] = 'Accept: application/json';
		$request_headers[] = 'Content-Type: application/json';
		$request_headers[] = 'x-api-key:'.self::XAPIKEY;

		curl_setopt($handle, CURLOPT_POST, true);
		curl_setopt($handle, CURLOPT_POSTFIELDS, json_encode($payload));
		curl_setopt($handle, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($handle, CURLOPT_HTTPHEADER, $request_headers);

		$response = curl_exec($handle);

		if (curl_errno($handle)) {
			echo 'Erro na requisição: ' . curl_error($handle);
			return;
		}

		// Fecha a conexão cURL
		curl_close($handle);

		// Decodifica a resposta JSON para um array associativo
		$response_data = json_decode($response, true);

		var_dump($response_data);
		die();

	}


	public function teste_gil() {
		// Consulta otimizada com JOIN
		$query = $this->db->select('pd.*, pdi.id AS item_id') // Pegamos apenas o ID do item para verificar se há itens
						  ->from('tbl_purchase_details pd')
						  ->join('tbl_purchase_details_itens pdi', 'pdi.order_id = pd.order_id', 'left')
						  ->where('pd.status', 1)
						  ->get();

		$results = $query->result_array();

		// Inicializa os arrays
		$final_results = [];
		$all_order_ids = []; // Lista com todos os order_id retornados

		foreach ($results as $row) {
			$order_id = $row['order_id']; // Supondo que 'order_id' seja a chave primária

			// Adiciona o order_id à lista total de pedidos
			$all_order_ids[] = $order_id;

			if (!isset($final_results[$order_id])) {
				// Adiciona o pedido no array final
				$final_results[$order_id] = [
					'order_id' => $row['order_id'],
					'status'   => $row['status'],
					'itens'    => [] // Criando um array para os itens
				];
			}

			// Se houver um item, adicionamos ao array
			if (!empty($row['item_id'])) {
				$final_results[$order_id]['itens'][] = $row['item_id']; // Apenas armazenamos os IDs dos itens
			}
		}

		// Filtra apenas as orders que **não têm itens**
		$orders_sem_itens = array_filter($final_results, function($order) {
			return empty($order['itens']); // Só mantém pedidos sem itens
		});

		// Monta a resposta final
		$response = [
			'orders_sem_itens' => array_values($orders_sem_itens), // Lista de pedidos sem itens
		];

		// Retorna como JSON
		echo json_encode($response);
	}



}
