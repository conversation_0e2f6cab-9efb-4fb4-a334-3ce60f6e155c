<?php

defined('BASEPATH') OR exit('No direct script access allowed');

$route['default_controller'] = 'Dashboard';
$route['404_override'] = 'Erro_404'; // Redirecionando o erro 404 para a pagina 404
$route['translate_uri_dashes'] = FALSE;

$route['logout'] = 'login/logout';

# Clientes
$route['customers'] = 'Clientes/listar_clientes';
$route['update-status-user'] = 'Clientes/update_status_user';
$route['impersonate'] = 'Clientes/impersonate';
$route['customer-payments'] = 'Clientes/customer_payments';
$route['customer-timelime'] = 'Clientes/customer_timeline';
$route['insert-timeline'] = 'Clientes/insert_history_timeline';
$route['check-payment-customer'] = 'Clientes/check_payment_customer';
$route['insert-payment-customer'] = 'Clientes/inser_payment_customer';

# Orders
$route['orders'] = 'Orders/list_orders';
$route['order-details/(:num)'] = 'Orders/order_details/$1';
$route['update-purchased-order'] = 'Orders/update_purchased_order/$1';
$route['order-shipment-details/(:num)'] = 'Orders/shipment_details/$1';
$route['shipment-methods/(.*)/(.*)/(.*)'] = 'Orders/shipment_methods/$1/$2/$3';
$route['get-labels'] = 'Orders/get_labels';
$route['get-tracking'] = 'Orders/get_tracking';
$route['get-origins'] = 'Orders/get_origins';
$route['shippment'] = 'Orders/shippment';
$route['orders-no-seller'] = 'Orders/orders_noseller';

$route['forward-order'] = 'ForwardOrders/forward_order';
//$route['set-user-to-order'] = 'Orders/setUserToOrder';


#Orders Shopping
$route['orders-shopping'] = 'Orders/list_orders_shopping';

#BIGbot
$route['zap-check-message'] = 'Bigbot/check_message';

# Invoices
$route['generate-invoice'] = 'Invoices/generate_invoice';


$route['list-clients-pending'] = 'Clientes/listar_clientes_pendente_aprovacao';
$route['data-client/(.*)'] = 'Clientes/dados/$1';
$route['approve-client/(.*)'] = 'Clientes/approve_client/$1';
$route['list-user-products/(.*)'] = 'Clientes/list_user_products/$1';

$route['product-details'] = 'Clientes/details_user_products';

$route['send-revision'] = 'Clientes/send_product_to_revision';
$route['send-approval'] = 'Clientes/send_product_to_approval';
$route['change-status'] = 'Clientes/change_status_product';


# Products
$route['list-products-approval'] = 'Products/list_products_to_approval';
$route['list-products-pending'] = 'Products/listar_produtos_pendentes';
$route['data-products/(.*)'] = 'Products/dados/$1';


# Pagination de orders used in orders / compras
$route['get-orders'] = 'Orders/get_orders';


# purchase
$route['purchase-orders'] = 'Logistics/list_orders';
$route['purchase-orders-new'] = 'Logistics/list_orders_new';
$route['get-purchase-orders'] = 'Logistics/get_orders';
$route['get-purchase-orders-new'] = 'Logistics/get_orders_new';
$route['order-details-purchase/(:num)'] = 'Logistics/order_details/$1';
$route['save-details-order'] = 'Logistics/save_details_order';

# logistics
$route['logistics-orders'] = 'Logistics/list_orders_logistics';
$route['get-logistics-orders'] = 'Logistics/get_logistics_orders';
$route['order-details-logistic/(:num)'] = 'Logistics/order_details_logistic/$1';

# send tracking number
$route['send-tracking'] = 'Logistics/send_tracking_number';

$route['get-timeline-history'] = 'Logistics/get_timeline_history';
$route['save-details-item'] = 'Logistics/save_details_item';
$route['save-costprice-item'] = 'Logistics/save_costprice_item';
$route['upload-order-invoice'] = 'Logistics/upload_order_invoice';

# notifications
$route['get-notifications/(.*)'] = 'Notifications/get_latest_notification/$1';
$route['read-notification/(.*)'] = 'Notifications/mark_as_read/$1';
$route['send-notification'] = 'Notifications/send_notification';

# Suppliers
$route['suppliers'] = 'Suppliers/register_suppliers';
$route['save-suppliers'] = 'Suppliers/save_suppliers';
$route['delete-suppliers/(:num)'] = 'Suppliers/dalete_suppliers/$1';

# Endpoint criado para Pedro Julio, consultar dados cliente
$route['customers-all'] = 'Clientes/listar_clientes_all';


$route['daily-scrum'] = 'Daily/register_daily';
$route['save-daily'] = 'Daily/save_daily';

$route['status'] = 'Logistics/teste_gil';






