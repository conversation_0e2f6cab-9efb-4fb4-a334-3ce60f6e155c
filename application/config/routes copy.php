<?php

defined('BASEPATH') OR exit('No direct script access allowed');

$route['default_controller'] = 'Dashboard';
$route['404_override'] = 'Erro_404'; // Redirecionando o erro 404 para a pagina 404
$route['translate_uri_dashes'] = FALSE;

# Clientes
$route['list-clients'] = 'Clientes/listar_clientes';
$route['list-clients-pending'] = 'Clientes/listar_clientes_pendente_aprovacao';
$route['data-client/(.*)'] = 'Clientes/dados/$1';
$route['approve-client/(.*)'] = 'Clientes/approve_client/$1';
$route['list-user-products-pending/(.*)'] = 'Clientes/list_user_products_pending/$1';
$route['product-datails/(.*)'] = 'Clientes/details_user_product/$1';

# Produtos

$route['list-products-clients'] = 'Produtos/listar_produtos';
$route['list-products-pending'] = 'Produtos/listar_produtos_pendentes';
$route['data-products/(.*)'] = 'Produtos/dados/$1';








# Agendamentos
$route['schedule'] = 'Agendamentos';
$route['list-schedules'] = 'Agendamentos/listar_agendamentos';
$route['new-scheduling'] = 'Agendamentos/cadastrar_seguradora';
$route['save-scheduling'] = 'Agendamentos/salvar_agendamento';
$route['edit-scheduling/(.*)'] = 'Agendamentos/editar/$1';
$route['save-edit-scheduling'] = 'Agendamentos/salvar_edicao';
$route['delete-sheduling/(:num)'] = 'Agendamentos/excluir_seguradora/$1';

# Agendamentos - Calendario
$route['calendar'] = 'Agendamentos/calendario';
$route['agendamentos'] = 'Agendamentos/getAgendamentos';


# Agendamentos - Lembretes
$route['sticky-notes'] = 'Lembretes';
$route['delete-sticky-notes/(.*)'] = 'Lembretes/delete/$1';
$route['check-sticky-notes/(.*)'] = 'Lembretes/check/$1';

# Agendamentos - Lembretes
$route['cashier'] = 'Caixa/get_mes_values';
$route['delete-sticky-notes/(.*)'] = 'Lembretes/delete/$1';
$route['check-sticky-notes/(.*)'] = 'Lembretes/check/$1';


# Gestão
$route['professionals'] = 'Profissionais';
$route['settings'] = 'Config';
$route['user-system'] = 'Usuarios';


$route['new-client'] = 'Clientes/cadastrar_cliente';
$route['save-client'] = 'Clientes/salvar';
$route['edit-client/(.*)'] = 'Clientes/editar/$1';
$route['save-edit-client'] = 'Clientes/salvar_edicao';
$route['delete-client/(.*)'] = 'Clientes/excluir/$1';


$route['history-client/(.*)'] = 'Clientes/historico/$1';

$route['save-service/(.*)'] = 'Dashboard/dados_para_fechar_atendimento/$1';
$route['save-service-now'] = 'Dashboard/salvar_dados_do_atendimento';
$route['not-attend/(.*)'] = 'Dashboard/nao_compareceu/$1';

# Ambiente dev
$route['migrar'] = 'Migracoes/migracao_clientes';
$route['migrar-agenda'] = 'Migracoes/migracao_agendamentos';


# Teste videos
$route['video'] = 'Agendamentos/videos';












$route['cadastrar-cliente/(:num)'] = 'Clientes/cadastrar_cliente/$1';





$route['import'] = 'Import';

$route['perfil'] = 'Profile/troca_senha';
$route['alteracao-de-senha-sucesso'] = 'Profile/alterar_senha';



$route['listar-destinos'] = 'Seguros/listar_destinos';
$route['cadastrar-destinos'] = 'Seguros/cadastrar_destinos';
$route['cadastrar-destinos/(:num)'] = 'Seguros/cadastrar_destinos/$1';
$route['excluir-destinos/(:num)'] = 'Seguros/excluir_destinos/$1';

$route['cadastrar-plano'] = 'Seguros/cadastrar_seguros';
$route['listar-planos'] = 'Seguros/listar_seguros';
$route['salvar-plano'] = 'Seguros/salvar';
$route['editar-plano'] = 'Seguros/salvar_edicao';


$route['listar-cupons'] = 'Cupom/listar_cupom';
$route['cadastrar-cupom'] = 'Cupom/cadastrar_cupom';
$route['cadastrar-cupom/(:num)'] = 'Cupom/editar_cupom/$1';
$route['excluir-cupom/(:num)'] = 'Cupom/excluir_cupom/$1';
$route['salvar-cupom'] = 'Cupom/salvar';
$route['editar-cupom'] = 'Cupom/salvar_edicao';

$route['listar-testemunhos'] = 'Testemunhos/listar_testemunhos';
$route['cadastrar-testemunho'] = 'Testemunhos/cadastrar_testemunho';
$route['salvar-testemunho'] = 'Testemunhos/salvar';
$route['editar-testemunho'] = 'Testemunhos/salvar_edicao';
$route['cadastrar-testemunho/(:num)'] = 'Testemunhos/cadastrar_testemunho/$1';
$route['excluir-testemunho/(:num)'] = 'Testemunhos/excluir_testemunho/$1';



$route['salvar-dolar-dia'] = 'Seguros/salvar_dolar';

$route['compras-efetuadas'] = 'Compras/listar_compras';
$route['autorizar-compra/(.*)'] = 'Compras/dados_compra/$1';
$route['excluir-compra/(.*)'] = 'Compras/excluir_compra/$1';
$route['salvar-informacoes-compra'] = 'Compras/salvar_dados_para_autorizacao';

//$route['solicitar-cotacao/(.*)']      = 'cotacao/index/$1';
$route['enviar-email-autorizacao/(.*)'] = 'Compras/enviar_email_autorizacao';

$route['filtro'] = 'Dashboard/filtro';

$route['backup-banco-dados'] = 'BackupBD/index';
$route['logout'] = 'login/logout';

$route['quem-somos'] = 'Empresa/cadastrar_quem_somos';
$route['condicoes-gerais'] = 'Condicoes/cadastrar_condicoes';

$route['cadastrar-termo'] = 'Termo/cadastrar_termo';

$route['listar-faq'] = 'Faq/listar_faq';
$route['cadastrar-faq'] = 'Faq/cadastrar_faq';
$route['cadastrar-faq/(:num)'] = 'Faq/cadastrar_faq/$1';
$route['excluir-faq/(:num)'] = 'Faq/excluir_faq/$1';

$route['listar-contato'] = 'Contato/listar_contato';
$route['visualizar-contato/(:num)'] = 'Contato/visualizar_contato/$1';
$route['excluir-contato/(:num)'] = 'Contato/excluir_contato/$1';

$route['configs/cadastrar-logotipo'] = 'Config/cadastrar_logotipo';
$route['configs/informacoes-de-contato'] = 'Config/informacoes_de_contato';
$route['configs/configs-smtp'] = 'Config/configuracoes_smtp';
$route['configs/redes-sociais'] = 'Config/redes_sociais';
$route['configs/integracoes'] = 'Config/integracoes';

$route['configs/web-service/(.*)'] = 'Config/webservice/$1';
$route['configs/js-find-all-icones'] = 'Config/jsFindAllIcones';

$route['gerenciar-categoria'] = 'Blog/gerenciar_categoria';
$route['cadastrar-categoria'] = 'Blog/cadastrar_categoria';
$route['cadastrar-categoria/(:num)'] = 'Blog/cadastrar_categoria/$1';
$route['excluir-categoria/(:num)'] = 'Blog/excluir_categoria/$1';

$route['gerenciar-post'] = 'Blog/gerenciar_post';
$route['cadastrar-post'] = 'Blog/cadastrar_post';
$route['cadastrar-post/(:num)'] = 'Blog/cadastrar_post/$1';
$route['excluir-post/(:num)'] = 'Blog/excluir_post/$1';



$route['gerenciar-tabelas'] = 'Tabelas/cadastrar_tabela';
$route['salvar-tabela'] = 'Tabelas/salvar';
$route['excluir-tabela/(:num)'] = 'Tabelas/excluir_tabela/$1';

// $route['listar-testemunhos'] = 'Testemunhos/listar_testemunhos';

// $route['editar-testemunho'] = 'Testemunhos/salvar_edicao';
// $route['cadastrar-testemunho/(:num)'] = 'Testemunhos/cadastrar_testemunho/$1';


// $route['gerenciar-tabela'] = 'Tabela/gerenciar_tabela';
// $route['cadastrar-tabela/(:num)'] = 'Tabela/cadastrar_tabela/$1';
// $route['excluir-tabela/(:num)'] = 'Tabela/excluir_tabela/$1';




//Calcuadora
$route['listar-clientes-ancora'] = 'Getcli/listar_clientes';
$route['contactado-cliente-ancora/(:num)'] = 'Getcli/cliente_contactado/$1';

$route['excluir-cotacao/(:num)'] = 'Calculadora/excluir_cotacao/$1';
$route['enviar-cotacao'] = 'Calculadora/enviar_propostas';
$route['listar-seguros'] = 'Calculadora/listar_seguros';

$route['detalhes-cotacao/(.*)'] = 'Calculadora/detalhes_cotacao/$1';

$route['alertas'] = 'Cronjobs/envio_de_emails';

$route['cotacao'] = 'Calculadora/index';
$route['listar-cotacoes'] = 'Calculadora/listar_cotacoes';
$route['ajax'] = 'Seguros/teste';
