<?php

class Uteis {

	public static function formatar_data($data, $divisorEntrada = '/', $divisor = '-') {
		$aux = '';
		if (!empty($data)) {
			if (is_array($data)) {
				foreach ($data[0] as $var) {
					if (array_key_exists($var, $data[1])) {
						$arrData = explode($divisorEntrada, $data[1][$var]);
						$data[1][$var] = $arrData[2] . $divisor . $arrData[1] . $divisor . $arrData[0];
					}
				}
			} else {
				$arrData = explode($divisorEntrada, $data);
				$aux = $arrData[2] . $divisor . $arrData[1] . $divisor . $arrData[0];
			}
		}
		return $aux;
	}

	public static function space2like($termo) {
		return addslashes(str_replace(' ', '%', '%' . $termo . '%'));
	}

	public static function formatar_cpf($fcpf) {
		$fcpf = substr($fcpf, 0, 3) . '.' . substr($fcpf, 3, 3) . '.' . substr($fcpf, 6, 3) . '-' . substr($fcpf, 9, 2);
		return $fcpf;
	}

	public static function formatar_cnpj($fcnpj) {
		$fcnpj = substr($fcnpj, 0, 2) . '.' . substr($fcnpj, 2, 3) . '.' . substr($fcnpj, 5, 3) . '/' . substr($fcnpj, 9, 4) . '-' . substr($fcnpj, 13, 2);
		return $fcnpj;
	}

	public static function formatar_telefone($tel) {
		switch (strlen($tel)) {
			case '8':
				$tel = substr($tel, 0, 4) . ' - ' . substr($tel, 4, 4);
				break;
			case '10':
				$tel = '(' . substr($tel, 0, 2) . ') ' . substr($tel, 2, 4) . '-' . substr($tel, 6, 4);
				break;
			case '12':
				$tel = '+' . substr($tel, 0, 2) . ' (' . substr($tel, 2, 2) . ') ' . substr($tel, 4, 4) . '-' . substr($tel, 8, 4);
				break;
		}
		return $tel;
	}

	public static function formatar_cep($cep) {
		$cep = substr($cep, 0, 5) . '-' . substr($cep, 5, 3);
		return $cep;
	}

	public static function formatar_data_extenso($data, $modo = 1) {
		if ($data) {
			$mes = date('m', strtotime($data));
		} else {
			$mes = date('m');
			$data = date('Y-m-d');
		}
		$meses = array('01' => 'Janeiro', '02' => 'Fevereiro', '03' => 'Março', '04' => 'Abril', '05' => 'Maio', '06' => 'Junho', '07' => 'Julho', '08' => 'Agosto', '09' => 'Setembro', '10' => 'Outubro', '11' => 'Novembro', '12' => 'Dezembro');
		$dias = array(0 => 'Domingo', 1 => 'Segunda-feira', 2 => 'Terça-feira', 3 => 'Quarta-feira', 4 => 'Quinta-feira', 5 => 'Sexta-feira', 6 => 'Sábado');

		$dia_da_semana = $dias[date('w', strtotime($data))];
		$dia_do_mes = date('d', strtotime($data));
		$mes_extenso = $meses[$mes];
		$ano = date('Y', strtotime($data));

		switch ($modo) {
			case 1:
				$rtn = $dia_da_semana . ', ' . $dia_do_mes . ' de ' . $mes_extenso . ' de ' . $ano;
				break;

			case 2:
				$rtn = $dia_da_semana . ' - ' . $dia_do_mes . '/' . $mes_extenso;
				break;

			case 3:
				$rtn = $dia_do_mes . '/' . $mes . ' - ' . $dia_da_semana;
				break;
			case 4:
				$rtn = date('d', strtotime($data)) . ' de ' . $meses[$mes] . ' de ' . $ano;
				break;
		}
		return $rtn;
	}

	public static function validar_cpf($cpf) {
		$nulos = array('12345678909', '11111111111', '22222222222', '33333333333',
			'44444444444', '55555555555', '66666666666', '77777777777',
			'88888888888', '99999999999', '00000000000');
		$cpf = ereg_replace("[^0-9]", "", $cpf);

		if (!(ereg("[0-9]", $cpf)))
			return 0;

		if (in_array($cpf, $nulos))
			return 0;

		$acum = 0;
		for ($i = 0; $i < 9; $i++) {
			$acum += $cpf[$i] * (10 - $i);
		}
		$x = $acum % 11;
		$acum = ($x > 1) ? (11 - $x) : 0;
		if ($acum != $cpf[9]) {
			return 0;
		}
		$acum = 0;
		for ($i = 0; $i < 10; $i++) {
			$acum += $cpf[$i] * (11 - $i);
		}
		$x = $acum % 11;
		$acum = ($x > 1) ? (11 - $x) : 0;
		if ($acum != $cpf[10]) {
			return 0;
		}
		return 1;
	}

	public static function validar_cnpj($cnpj) {
		$cnpj = ereg_replace("[^0-9]", "", $cnpj);

		if (strlen($cnpj) != 14) {
			return false;
		}

		$soma = 0;
		$soma += ($cnpj[0] * 5);
		$soma += ($cnpj[1] * 4);
		$soma += ($cnpj[2] * 3);
		$soma += ($cnpj[3] * 2);
		$soma += ($cnpj[4] * 9);
		$soma += ($cnpj[5] * 8);
		$soma += ($cnpj[6] * 7);
		$soma += ($cnpj[7] * 6);
		$soma += ($cnpj[8] * 5);
		$soma += ($cnpj[9] * 4);
		$soma += ($cnpj[10] * 3);
		$soma += ($cnpj[11] * 2);

		$d1 = $soma % 11;
		$d1 = $d1 < 2 ? 0 : 11 - $d1;

		$soma = 0;
		$soma += ($cnpj[0] * 6);
		$soma += ($cnpj[1] * 5);
		$soma += ($cnpj[2] * 4);
		$soma += ($cnpj[3] * 3);
		$soma += ($cnpj[4] * 2);
		$soma += ($cnpj[5] * 9);
		$soma += ($cnpj[6] * 8);
		$soma += ($cnpj[7] * 7);
		$soma += ($cnpj[8] * 6);
		$soma += ($cnpj[9] * 5);
		$soma += ($cnpj[10] * 4);
		$soma += ($cnpj[11] * 3);
		$soma += ($cnpj[12] * 2);

		$d2 = $soma % 11;
		$d2 = $d2 < 2 ? 0 : 11 - $d2;

		if ($cnpj[12] == $d1 && $cnpj[13] == $d2) {
			return true;
		} else {
			return false;
		}
	}

	public static function get_all_estados($i = 0) {
		if ($i === 0) {
			$uf = array("AC" => "AC", "AL" => "AL", "AM" => "AM", "AP" => "AP", "BA" => "BA", "CE" => "CE", "DF" => "DF", "ES" => "ES", "GO" => "GO", "MA" => "MA", "MG" => "MG", "MS" => "MS", "MT" => "MT", "PA" => "PA", "PB" => "PB", "PE" => "PE", "PI" => "PI", "PR" => "PR", "RJ" => "RJ", "RN" => "RN", "RO" => "RO", "RR" => "RR", "RS" => "RS", "SC" => "SC", "SE" => "SE", "SP" => "SP", "TO" => "TO");
		} else if ($i === 1) {
			$uf = array(
				array('codigo' => 'AC', 'uf' => 'AC', 'descricao' => 'Acre'),
				array('codigo' => 'AL', 'uf' => 'AL', 'descricao' => 'Alagoas'),
				array('codigo' => 'AM', 'uf' => 'AM', 'descricao' => 'Amazonas'),
				array('codigo' => 'AP', 'uf' => 'AP', 'descricao' => 'Amapá'),
				array('codigo' => 'BA', 'uf' => 'BA', 'descricao' => 'Bahia'),
				array('codigo' => 'CE', 'uf' => 'CE', 'descricao' => 'Ceará'),
				array('codigo' => 'DF', 'uf' => 'DF', 'descricao' => 'Distrito Federal'),
				array('codigo' => 'ES', 'uf' => 'ES', 'descricao' => 'Espírito Santo'),
				array('codigo' => 'GO', 'uf' => 'GO', 'descricao' => 'Goiás'),
				array('codigo' => 'MA', 'uf' => 'MA', 'descricao' => 'Maranhão'),
				array('codigo' => 'MG', 'uf' => 'MG', 'descricao' => 'Minas Gerais'),
				array('codigo' => 'MS', 'uf' => 'MS', 'descricao' => 'Mato Grosso do Sul'),
				array('codigo' => 'MT', 'uf' => 'MT', 'descricao' => 'Mato Grosso'),
				array('codigo' => 'PA', 'uf' => 'PA', 'descricao' => 'Pará'),
				array('codigo' => 'PB', 'uf' => 'PB', 'descricao' => 'Paraiba'),
				array('codigo' => 'PE', 'uf' => 'PE', 'descricao' => 'Pernambuco'),
				array('codigo' => 'PI', 'uf' => 'PI', 'descricao' => 'Piauí'),
				array('codigo' => 'PR', 'uf' => 'PR', 'descricao' => 'Paraná'),
				array('codigo' => 'RJ', 'uf' => 'RJ', 'descricao' => 'Rio de Janeiro'),
				array('codigo' => 'RN', 'uf' => 'RN', 'descricao' => 'Rio Grande do Norte'),
				array('codigo' => 'RO', 'uf' => 'RO', 'descricao' => 'Rondônia'),
				array('codigo' => 'RR', 'uf' => 'RR', 'descricao' => 'Roraima'),
				array('codigo' => 'RS', 'uf' => 'RS', 'descricao' => 'Rio Grande do Sul'),
				array('codigo' => 'SC', 'uf' => 'SC', 'descricao' => 'Santa Catarina'),
				array('codigo' => 'SE', 'uf' => 'SE', 'descricao' => 'Sergipe'),
				array('codigo' => 'SP', 'uf' => 'SP', 'descricao' => 'São Paulo'),
				array('codigo' => 'TO', 'uf' => 'TO', 'descricao' => 'Tocantins')
			);
		}
		return $uf;
	}

	public static function load_estado($codigo) {
		$arrEstado = self::findAllEstados(1);
		foreach ($arrEstado as $var) {
			if ($var['codigo'] == $codigo) {
				return $var;
			}
		}
	}

	public static function get_all_dia_semana($i = 0) {
		switch ($i) {
			case 0:
				return array(0 => 'Domingo', 1 => 'Segunda', 2 => 'Terça', 3 => 'Quarta', 4 => 'Quinta', 5 => 'Sexta', 5 => 'Sábado');
				break;
			case 1:
				return array('domingo' => 'Domingo', 'segunda' => 'Segunda', 'terça' => 'Terça', 'quarta' => 'Quarta', 'quinta' => 'Quinta', 'sexta' => 'Sexta', 'sabado' => 'Sábado');
				break;
			case 2:
				return array(0 => 'Domingo', 1 => 'Segunda-feira', 2 => 'Terça-feira', 3 => 'Quarta-feira', 4 => 'Quinta-feira', 5 => 'Sexta-feira', 6 => 'Sábado');
				break;
			case 3:
				return array('domingo' => 'Domingo', 'segunda' => 'Segunda-feira', 'terça' => 'Terça-feira', 'quarta' => 'Quarta-feira', 'quinta' => 'Quinta-feira', 'sexta' => 'Sexta-feira', 'sabado' => 'Sábado');
				break;
		}
	}

	public static function load_dia_semana($codigo, $i = 0) {
		$arr = self::findAllDiasSemana($i);
		return $arr[$codigo];
	}

	public static function get_all_mes($i = 0) {
		switch ($i) {
			case 0:
				return array(1 => 'Janeiro', 2 => 'Fevereiro', 3 => 'Março', 4 => 'Abril', 5 => 'Maio', 6 => 'Junho', 7 => 'Julho', 8 => 'Agosto', 9 => 'Setembro', 10 => 'Outubro', 11 => 'Novembro', 12 => 'Dezembro');
				break;
			case 1:
				return array('jan' => 'Janeiro', 'fev' => 'Fevereiro', 'mar' => 'Março', 'abr' => 'Abril', 'maio' => 'Maio', 'jun' => 'Junho', 'jul' => 'Julho', 'ago' => 'Agosto', 'set' => 'Setembro', 'out' => 'Outubro', 'nov' => 'Novembro', 'dez' => 'Dezembro');
				break;
			case 2:
				return array(1 => 'Jan', 2 => 'Fev', 3 => 'Mar', 4 => 'Abr', 5 => 'Mai', 6 => 'Jun', 7 => 'Jul', 8 => 'Ago', 9 => 'Set', 10 => 'Out', 11 => 'Nov', 12 => 'Dez');
				break;
		}
	}

	public static function load_mes($codigo, $i = 0) {
		$arr = self::findAllMes($i, $idioma);
		return $arr[(int) $codigo];
	}

	public static function get_saudacao() {
		$hora = date('H');
		$saud = 'Olá';
		if ($hora >= 6 && $hora < 12) {
			$saud = 'Bom dia';
		} else if ($hora >= 12 && $hora < 18) {
			$saud = 'Boa tarde';
		} else if ($hora >= 18 || $hora < 6) {
			$saud = 'Boa noite';
		}
		return $saud;
	}

	public static function get_codigo_aleatorio($n = 6, $sensitive = false, $alpha = true) {

		if ($alpha) {
			if ($sensitive) {
				$caracteres = 'abcdefghijklmnpqrstuvwxy0123456789';
			} else {
				$caracteres = 'abcdefghjkmnpqrstuvwxyzABCDEFGHJKMNPQRSTUVWXYZ0123456789';
			}
		} else {
			$caracteres = '0123456789';
		}

		$max = strlen($caracteres) - 1;
		$password = null;
		for ($i = 0; $i < $n; $i++) {
			$password .= $caracteres{mt_rand(0, $max)};
		}
		return $password;
	}

	public static function remove_caracter($arr, $fields = '') {
		if (is_array($arr)) {
			foreach ($fields as $field) {
				if (!empty($arr[$field])) {
					$arr[$field] = preg_replace('/[-()\s\.]/', '', $arr[$field]);
				}
			}
		} else {
			$arr = preg_replace('/[-()\s\.]/', '', $arr);
		}

		return $arr;
	}

	public static function limita_string($texto, $n = 50, $limitador = '...') {
		if (strlen($texto) > $n) {
			return substr($texto, 0, $n) . $limitador;
		} else {
			return $texto;
		}
	}

	public static function corta_palavra($texto, $n = 50) {
		$texto = self::unhtmlentities($texto);
		return strlen($texto) > $n ? substr($texto, 0, ($n - 0)) : $texto;
	}

	function abrevia_string($string) {
		$explode = explode(" ", $string);
		if (count($explode) > 2) {
			for ($i = 0; $i <= count($explode); $i++) {
				if ($i == 1) {
					$sobrenome = self::cortaPalavra($explode[1], 1);
					$nome .= $sobrenome . '. ';
				} else {
					$nome .= ' ' . $explode[$i] . ' ';
				}
			}
		} else {
			$nome = $string;
		}
		return trim($nome);
	}

	function remove_acentos($palavra = '') {
		$palavra = ereg_replace("[^.|/:(a-zA-Z0-9_)]-", "", strtr($palavra, utf8_decode("áàãâéêíóôõúüçÁÀÃÂÉÊÍÓÔÕÚÜÇ!"), "aaaaeeiooouucAAAAEEIOOOUUC"));
		return $palavra;
	}

	function substitui_acentos($string = '') {
		$a = array('À', 'Á', 'Â', 'Ã', 'Ä', 'Å', 'Æ', 'Ç', 'È', 'É', 'Ê', 'Ë', 'Ì', 'Í', 'Î', 'Ï', 'Ð', 'Ñ', 'Ò', 'Ó', 'Ô', 'Õ', 'Ö', 'Ø', 'Ù', 'Ú', 'Û', 'Ü', 'Ý', 'ß', 'à', 'á', 'â', 'ã', 'ä', 'å', 'æ', 'ç', 'è', 'é', 'ê', 'ë', 'ì', 'í', 'î', 'ï', 'ñ', 'ò', 'ó', 'ô', 'õ', 'ö', 'ø', 'ù', 'ú', 'û', 'ü', 'ý', 'ÿ', 'A', 'a', 'A', 'a', 'A', 'a', 'C', 'c', 'C', 'c', 'C', 'c', 'C', 'c', 'D', 'd', 'Ð', 'd', 'E', 'e', 'E', 'e', 'E', 'e', 'E', 'e', 'E', 'e', 'G', 'g', 'G', 'g', 'G', 'g', 'G', 'g', 'H', 'h', 'H', 'h', 'I', 'i', 'I', 'i', 'I', 'i', 'I', 'i', 'I', 'i', '?', '?', 'J', 'j', 'K', 'k', 'L', 'l', 'L', 'l', 'L', 'l', '?', '?', 'L', 'l', 'N', 'n', 'N', 'n', 'N', 'n', '?', 'O', 'o', 'O', 'o', 'O', 'o', 'Œ', 'œ', 'R', 'r', 'R', 'r', 'R', 'r', 'S', 's', 'S', 's', 'S', 's', 'Š', 'š', 'T', 't', 'T', 't', 'T', 't', 'U', 'u', 'U', 'u', 'U', 'u', 'U', 'u', 'U', 'u', 'U', 'u', 'W', 'w', 'Y', 'y', 'Ÿ', 'Z', 'z', 'Z', 'z', 'Ž', 'ž', '?', 'ƒ', 'O', 'o', 'U', 'u', 'A', 'a', 'I', 'i', 'O', 'o', 'U', 'u', 'U', 'u', 'U', 'u', 'U', 'u', 'U', 'u', '?', '?', '?', '?', '?', '?', ' + ', ' - ', ' º ');
		$b = array('A', 'A', 'A', 'A', 'A', 'A', 'AE', 'C', 'E', 'E', 'E', 'E', 'I', 'I', 'I', 'I', 'D', 'N', 'O', 'O', 'O', 'O', 'O', 'O', 'U', 'U', 'U', 'U', 'Y', 's', 'a', 'a', 'a', 'a', 'a', 'a', 'ae', 'c', 'e', 'e', 'e', 'e', 'i', 'i', 'i', 'i', 'n', 'o', 'o', 'o', 'o', 'o', 'o', 'u', 'u', 'u', 'u', 'y', 'y', 'A', 'a', 'A', 'a', 'A', 'a', 'C', 'c', 'C', 'c', 'C', 'c', 'C', 'c', 'D', 'd', 'D', 'd', 'E', 'e', 'E', 'e', 'E', 'e', 'E', 'e', 'E', 'e', 'G', 'g', 'G', 'g', 'G', 'g', 'G', 'g', 'H', 'h', 'H', 'h', 'I', 'i', 'I', 'i', 'I', 'i', 'I', 'i', 'I', 'i', 'IJ', 'ij', 'J', 'j', 'K', 'k', 'L', 'l', 'L', 'l', 'L', 'l', 'L', 'l', 'l', 'l', 'N', 'n', 'N', 'n', 'N', 'n', 'n', 'O', 'o', 'O', 'o', 'O', 'o', 'OE', 'oe', 'R', 'r', 'R', 'r', 'R', 'r', 'S', 's', 'S', 's', 'S', 's', 'S', 's', 'T', 't', 'T', 't', 'T', 't', 'U', 'u', 'U', 'u', 'U', 'u', 'U', 'u', 'U', 'u', 'U', 'u', 'W', 'w', 'Y', 'y', 'Y', 'Z', 'z', 'Z', 'z', 'Z', 'z', 's', 'f', 'O', 'o', 'U', 'u', 'A', 'a', 'I', 'i', 'O', 'o', 'U', 'u', 'U', 'u', 'U', 'u', 'U', 'u', 'U', 'u', 'A', 'a', 'AE', 'ae', 'O', 'o', ' mais ', ' menos ', ' graus ');
		return str_replace($a, $b, $string);
	}

	function slug($string, $complemento = false) {
		$a = array('À', 'Á', 'Â', 'Ã', 'Ä', 'Å', 'Æ', 'Ç', 'È', 'É', 'Ê', 'Ë', 'Ì', 'Í', 'Î', 'Ï', 'Ð', 'Ñ', 'Ò', 'Ó', 'Ô', 'Õ', 'Ö', 'Ø', 'Ù', 'Ú', 'Û', 'Ü', 'Ý', 'ß', 'à', 'á', 'â', 'ã', 'ä', 'å', 'æ', 'ç', 'è', 'é', 'ê', 'ë', 'ì', 'í', 'î', 'ï', 'ñ', 'ò', 'ó', 'ô', 'õ', 'ö', 'ø', 'ù', 'ú', 'û', 'ü', 'ý', 'ÿ', 'A', 'a', 'A', 'a', 'A', 'a', 'C', 'c', 'C', 'c', 'C', 'c', 'C', 'c', 'D', 'd', 'Ð', 'd', 'E', 'e', 'E', 'e', 'E', 'e', 'E', 'e', 'E', 'e', 'G', 'g', 'G', 'g', 'G', 'g', 'G', 'g', 'H', 'h', 'H', 'h', 'I', 'i', 'I', 'i', 'I', 'i', 'I', 'i', 'I', 'i', '?', '?', 'J', 'j', 'K', 'k', 'L', 'l', 'L', 'l', 'L', 'l', '?', '?', 'L', 'l', 'N', 'n', 'N', 'n', 'N', 'n', '?', 'O', 'o', 'O', 'o', 'O', 'o', 'Œ', 'œ', 'R', 'r', 'R', 'r', 'R', 'r', 'S', 's', 'S', 's', 'S', 's', 'Š', 'š', 'T', 't', 'T', 't', 'T', 't', 'U', 'u', 'U', 'u', 'U', 'u', 'U', 'u', 'U', 'u', 'U', 'u', 'W', 'w', 'Y', 'y', 'Ÿ', 'Z', 'z', 'Z', 'z', 'Ž', 'ž', '?', 'ƒ', 'O', 'o', 'U', 'u', 'A', 'a', 'I', 'i', 'O', 'o', 'U', 'u', 'U', 'u', 'U', 'u', 'U', 'u', 'U', 'u', '?', '?', '?', '?', '?', '?', ' + ', ' - ', ' º ');
		$b = array('A', 'A', 'A', 'A', 'A', 'A', 'AE', 'C', 'E', 'E', 'E', 'E', 'I', 'I', 'I', 'I', 'D', 'N', 'O', 'O', 'O', 'O', 'O', 'O', 'U', 'U', 'U', 'U', 'Y', 's', 'a', 'a', 'a', 'a', 'a', 'a', 'ae', 'c', 'e', 'e', 'e', 'e', 'i', 'i', 'i', 'i', 'n', 'o', 'o', 'o', 'o', 'o', 'o', 'u', 'u', 'u', 'u', 'y', 'y', 'A', 'a', 'A', 'a', 'A', 'a', 'C', 'c', 'C', 'c', 'C', 'c', 'C', 'c', 'D', 'd', 'D', 'd', 'E', 'e', 'E', 'e', 'E', 'e', 'E', 'e', 'E', 'e', 'G', 'g', 'G', 'g', 'G', 'g', 'G', 'g', 'H', 'h', 'H', 'h', 'I', 'i', 'I', 'i', 'I', 'i', 'I', 'i', 'I', 'i', 'IJ', 'ij', 'J', 'j', 'K', 'k', 'L', 'l', 'L', 'l', 'L', 'l', 'L', 'l', 'l', 'l', 'N', 'n', 'N', 'n', 'N', 'n', 'n', 'O', 'o', 'O', 'o', 'O', 'o', 'OE', 'oe', 'R', 'r', 'R', 'r', 'R', 'r', 'S', 's', 'S', 's', 'S', 's', 'S', 's', 'T', 't', 'T', 't', 'T', 't', 'U', 'u', 'U', 'u', 'U', 'u', 'U', 'u', 'U', 'u', 'U', 'u', 'W', 'w', 'Y', 'y', 'Y', 'Z', 'z', 'Z', 'z', 'Z', 'z', 's', 'f', 'O', 'o', 'U', 'u', 'A', 'a', 'I', 'i', 'O', 'o', 'U', 'u', 'U', 'u', 'U', 'u', 'U', 'u', 'U', 'u', 'A', 'a', 'AE', 'ae', 'O', 'o', ' mais ', ' menos ', ' graus ');

		$string = strtolower(preg_replace(array('/[^a-zA-Z0-9 -]/', '/[ -]+/', '/^-|-$/'), array('', '-', ''), str_replace($a, $b, $string)));
		if ($complemento == true) {
			return strtolower($string) . '.html';
		} else {
			return strtolower($string);
		}

		return $string;
	}

	public static function tratar_nome($nome) {
		$nome = explode(" ", strtolower($nome)); // Separa o nome por espaços
		for ($i = 0; $i < count($nome); $i++) {
			if ($nome[$i] == "de" or $nome[$i] == "da" or $nome[$i] == "e" or $nome[$i] == "dos" or $nome[$i] == "do") {
				$saida .= $nome[$i] . ' '; // Se a palavra estiver dentro das complementares mostrar toda em minÃºsculo
			} else {
				$saida .= ucfirst($nome[$i]) . ' '; // Se for um nome, mostrar a primeira letra maiÃºscula
			}
		}
		return $saida;
	}

	public static function get_parametro($param = 'pg', $url = '') {
		$itURL = empty($_SERVER['REDIRECT_URI']) ? $_SERVER['REQUEST_URI'] : $_SERVER['REDIRECT_URI'];
		$explode_url = explode('/', $itURL);
		$as = array_search($param, $explode_url);
		if ($as) {
			$pagina = $explode_url[$as + 1];
			return $pagina;
		}
	}

	function check_email($email) {
		$email = explode('@', $email);
		$domain = $email[1];
		if (!checkdnsrr($domain)) {
			$rtn = false;
		} else {
			$rtn = true;
		}
		return $rtn;
	}

	public static function busca_endereco($cep) {
		$xml = simplexml_load_file('http://viacep.com.br/ws/' . $cep . '/xml/');
		if ($xml) {
			foreach ($xml as $id => $field) {
				$arr[$id] = (string) $xml->$id;
			}
			return $arr;
		} else {
			return false;
		}
	}

	public static function time_ago($time) {
		$time = strtotime(str_replace('-', '/', $time));
		$diff = time() - $time;
		$seconds = $diff;
		$minutes = round($diff / 60);
		$hours = round($diff / 3600);
		$days = round($diff / 86400);
		$weeks = round($diff / 604800);
		$months = round($diff / 2419200);
		$years = round($diff / 29030400);

		if ($seconds <= 60) {
			$rtn = "$seconds segundos atras";
		} else if ($minutes <= 60) {
			$rtn = $minutes == 1 ? '1 minuto' : $minutes . ' minutos';
		} else if ($hours <= 24) {
			$rtn = $hours == 1 ? '1 hora' : $hours . ' horas';
		} else if ($days <= 7) {
			$rtn = $days == 1 ? '1 dia' : $days . ' dias';
		} else if ($weeks <= 4) {
			$rtn = $weeks == 1 ? '1 semana' : $weeks . ' semanas';
		} else if ($months <= 12) {
			$rtn = $months == 1 ? '1 mes' : $months . ' meses';
		} else {
			$rtn = $years == 1 ? '1 ano' : $years . ' anos';
		}
		return $rtn;
	}

	function validate_email($email) {
//verifica se e-mail esta no formato correto de escrita
		if (!ereg('^([a-zA-Z0-9.-_])*([@])([a-z0-9]).([a-z]{2,3})', $email)) {
			$rtn = false;
		} else {
			$dominio = explode('@', $email);
			if (!checkdnsrr($dominio[1], 'A')) {
				$rtn = false;
			} else {
				$rtn = true;
			}
		}
		return $rtn;
	}

	function check_id_words($string) {
		return preg_match('/^[a-zA-Z0-9\d\.\-_]+$/', $string);
	}

	function warning_sistema($arrMsg, $op = true) {
		$msg = '';
		if (count($arrMsg) > 0) {
			foreach ($arrMsg as $id => $arr) {
				foreach ($arr as $var) {
					$msg.= '<p>' . $var . '</p>';
				}
			}
		}
		return $msg;
	}

	public static function isMobile() {
		$useragent = $_SERVER['HTTP_USER_AGENT'];
		return preg_match('/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows (ce|phone)|xda|xiino/i', $useragent) || preg_match('/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i', substr($useragent, 0, 4));
	}

	public static function get_extensao($string) {
		$aux = explode(".", $string);
		$ext = strtolower(end($aux));
		return $ext;
	}

	public static function remove_tmp_file($nome, $pasta = '') {
		if ($pasta) {
			$pasta = DIRECTORY_SEPARATOR . $pasta;
		}
		$file = '../uploads' . $pasta . DIRECTORY_SEPARATOR . $nome;
		if (!empty($file) && file_exists($file)) {
			@unlink($file);
		}
	}

}

?>
