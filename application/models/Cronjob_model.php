<?php

class Cronjob_model extends CI_Model
{

	function __construct()
	{
		parent::__construct();
	}


	public function email_alerta_fim_seguro($nome, $email_cliente) {
		$config = configemail();
		$this->load->library('email', $config);
		$this->email->set_newline("\r\n");

		$this->email->from('<EMAIL>', 'Otripulante - Seu seguro viagem');

		$data = array(
			'saudacao' => saudacao(),
			'nome'     => $nome
		);

		$this->email->subject('Contato: ' . 'Renovaço do Seguro Viagem');
		$this->email->to($email_cliente);
		//$this->email->to('<EMAIL>');

		$body = $this->load->view('cronjob/renovacao.html', $data, TRUE);
		$this->email->message($body);
		$this->email->send();
		return TRUE;
	}



	public function email_feedback($nome, $email_cliente) {
		$config = configemail();
		$this->load->library('email', $config);
		$this->email->set_newline("\r\n");

		$this->email->from('<EMAIL>', 'Otripulante - Mensagem enviada');

		$data = array(
			'saudacao' => saudacao(),
			'nome'     => $nome
		);

		$this->email->subject('Contato: ' . 'Obrigado'.$nome.'por escolher Otripulante !');
		$this->email->to($email_cliente);
		//$this->email->to('<EMAIL>');

		$body = $this->load->view('cronjob/pos_venda.html', $data, TRUE);
		$this->email->message($body);
		$this->email->send();
		return TRUE;
	}
}

