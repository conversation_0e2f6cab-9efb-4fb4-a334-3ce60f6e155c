<?php

class FlowOrders_model extends CI_Model
{
	const URL_DATA_ORDERS = 'https://prd-mkp.bighub.store/orders';

	# ai buscar todas as vendas que ainda não existem user
	public function curl_get_orders_no_user_tests()
	{
		#https://prd-mkp.bighub.store/orders/noUser22
		$url_api = self::URL_DATA_ORDERS . "/noUser22";

		$handle   = curl_init($url_api);

		$request_headers     = [];
		$request_headers[] = 'Content-Type: application/json';

		curl_setopt($handle, CURLOPT_RETURNTRANSFER, TRUE);
		curl_setopt($handle, CURLOPT_HTTPHEADER, $request_headers);

		$response = curl_exec($handle);
		return json_decode($response);
	}

	# ai buscar todas as vendas que ainda não existem user
	public function curl_get_orders_no_user()
	{
		$url_api = self::URL_DATA_ORDERS . "/noUser";
		$handle = curl_init($url_api);
	
		// Headers para simular uma requisição de navegador
		$request_headers = [
			'Content-Type: application/json',
			'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36',
			'Connection: keep-alive'
		];
	
		// Configurações de cURL para melhorar o desempenho
		curl_setopt($handle, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($handle, CURLOPT_HTTPHEADER, $request_headers);
		curl_setopt($handle, CURLOPT_FOLLOWLOCATION, true);  // Segue redirecionamentos automaticamente
		curl_setopt($handle, CURLOPT_TIMEOUT, 10);           // Limite de tempo de resposta
		curl_setopt($handle, CURLOPT_CONNECTTIMEOUT, 5);     // Limite de tempo de conexão
	
		// Executa a requisição
		$response = curl_exec($handle);
	
		// Verifica se ocorreu algum erro
		if (curl_errno($handle)) {
			$error_msg = curl_error($handle);
			curl_close($handle);
			throw new Exception("Erro na requisição cURL: " . $error_msg);
		}
	
		curl_close($handle);
	
		return json_decode($response);
	}
	

	# Get order por ID
	public function get_order($payload)
	{
		$url_api = self::URL_DATA_ORDERS . "/orderId";

		$handle   = curl_init($url_api);

		$request_headers     = [];
		$request_headers[] = 'Accept: application/json';
		$request_headers[] = 'Content-Type: application/json';

		curl_setopt($handle, CURLOPT_POST, true);
		curl_setopt($handle, CURLOPT_POSTFIELDS, json_encode($payload));
		curl_setopt($handle, CURLOPT_RETURNTRANSFER, TRUE);
		curl_setopt($handle, CURLOPT_HTTPHEADER, $request_headers);

		$response = curl_exec($handle);
		return json_decode($response);
	}

}
