<?php

class Faq_model extends CI_Model {

	public $table = '';

	function __construct() {
		parent::__construct();
	}

	function setTable($tabela) {
		$this->table = $tabela;
	}

	function getTable() {
		return $this->table;
	}

	function getAll($order_by = 'id', $ordem = 'DESC') {
		$this->db->select('*')
			->from($this->table)
			->order_by($order_by, $ordem);
		$q = $this->db->get()->result();
		return $q;
	}

	function Cadastrar($data) {
		$data['data_cadastro'] = date('Y-m-d');
		if (!isset($data))
			return false;
		return $this->db->insert($this->table, $data);
	}

	function Atualizar($id, $data) {
		if (is_null($id) || !isset($data))
			return false;
		$this->db->where('id', $id);
		return $this->db->update($this->table, $data);
	}

	function Deletar($id) {
		return $this->db->where('id', $id)
				->delete($this->table);
	}

}
