<?php

class Cadastro_model extends CI_Model {

    function __construct() {

        parent::__construct();
    }

    public function cadastrar_usuario_model() {
   
        $data['nos_conheceu']     = $this->input->post('conheceu');
        $data['tipo_cliente']     = $this->input->post('tipo');
        $data['tempo_desejo']     = $this->input->post('desejo');
        $data['porque_o_site']    = $this->input->post('porque_ter_site');
        $data['site_referencia']  = $this->input->post('site_referencia');
        $data['tem_site']         = $this->input->post('ja_tem_site');
        $data['url_site_cliente'] = $this->input->post('url_site_cliente');
        $data['nome_cliente']     = $this->input->post('nome');
        $data['ddd_cliente']      = $this->input->post('ddd');
        $data['telefone_cliente'] = $this->input->post('telefone');
        $data['email_cliente']    = $this->input->post('email');
        $data['msg_cliente']      = $this->input->post('msg');

        $data['data_solicitacao'] = date('Y/m/d');
        $data['ip_cliente']       = $this->input->ip_address();
       
        return $this->db->insert('tbl_orcamento', $data);
   
    }


    public function inserir_pedido_de_cotacao() {
       $data = array(
            'nome_cliente'      => $this->input->post('nome_cliente'),
            'email_cliente'     => TudoMinusculo($this->input->post('email_cliente')),
            'telefone_cliente'  => $_POST['telefone_cliente'],
            'msg_cliente'       => $_POST['msg_cliente'],
            'tipo_plano'        => $_POST['modalidade_plano'],
            'cidade_cliente'    => $_POST['cidade_cliente'],
            'operadora_plano'    => 'Bradesco',
            'data_solicitacao'  => date('Y-m-d')
        );
        
        return $this->db->insert('tbl_captacao', $data);
    }




# Aqui é feito a gravação dos dados preenchidos para envio do email
     public function gravar_envio_de_email() {
       $data = array(
            'operadora_email'  => TudoMinusculo($this->input->post('operadora')),
            'email_cliente'    => TudoMinusculo($this->input->post('email_cliente')),
            'nome_cliente'     => ucwords(strtolower($this->input->post('nome_cliente'))),
            'tipo_cliente'     => $this->input->post('tipo_de_email'),
            'cidade_cliente'   => $this->input->post('cidade_cliente'),
            'data_envio'       => date('Y-m-d')
        );
        return $this->db->insert('tbl_email_enviados', $data);
    }
}
