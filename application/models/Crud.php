<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Crud extends CI_Model {

	private $table;

	public function __construct() {
		parent::__construct();
	}

	/**
	 *
	 * @param type $table : Indica a tabela a ser utilizada no CRUD
	 */
	public function setTable($table) {
		$this->table = $table;
	}

	/**
	 *
	 * @return type : Retorna a tabela utilizada nas operações
	 */
	public function getTable() {
		return $this->table;
	}

	/**
	 *
	 * @param type $field : campo a ser utilizado na ordenação
	 * @param type $order : Indicação de ordenação (ASC | DESC)
	 * @return type Array : 1 ou mais registros localizados na tabela $table
	 */
	public function getAll($field = 'id', $order = 'DESC') {
		$this->db->select('*')
			->from($this->table)
			->order_by($field, $order);
		$query = $this->db->get()->result();
		return $query;
	}

	/**
	 *
	 * @param type $modo :
	 * @param type $arr
	 * @param type $campo
	 * @param type $ord
	 * @param type $pagina
	 * @return type
	 */
	public function findAllByFilter($arr = '', $campo = 'id', $ord = 'desc') {
		$query = '';
		if (isset($arr['busca'])) {
			$termos = explode(' ', str_replace('  ', ' ', $arr['busca']));
			$i = 1;
			foreach ($termos as $item) {
				$termo = Uteis::space2like($item);
				if ($i < 2) {
					$query = "AND (titulo LIKE '" . $termo . "') ";
				} else {
					$query .= "OR (titulo LIKE '" . $termo . "') ";
				}
				$i++;
			}
		}
		if (isset($arr['ativo'])) {
			$query .= "AND ativo = '" . $arr['ativo'] . "' ";
		}
		if (isset($arr['itens'])) {
			$n_de_resultados = $arr['itens'];
		}
		if (isset($query) && !empty($query)) {
			$query = 'WHERE' . strstr($query, ' ');
		}

		$this->db->select("*,
				DATE_FORMAT('data_cadastro', '%d/%m/%Y') AS data_cadastro_formatada,
				DATE_FORMAT('data_cadastro', '%H:%i') AS hora_cadastro_formatada,
				DATE_FORMAT('data_atualizacao', '%d/%m/%Y') AS data_atualizacao_formatada,
				DATE_FORMAT('data_atualizacao', '%H:%i') AS hora_atualizacao_formatada")
			->from($this->table);
		if (isset($query) && !empty($query)) {
			$query = strstr($query, ' ');
			$this->db->where($query);
		}
		$this->db->order_by($campo, $ord);
		$query = $this->db->get()->result();

		return $query;
	}

	/**
	 *
	 * @param type $field : compo a ser utilizado na pesquisa do registro
	 * @param type $value : valor a ser pesquisado no campo $field
	 * @return type : registro encontrado na tabela $table mediante o valor $value informado
	 */
	public function get($field = 'id', $value) {
		$this->db->select('*')
			->from($this->table)
			->where($field, $value);
		$query = $this->db->get()->result();
		return $query;
	}

	/**
	 *
	 * @param type $order : campo a ser utilizado para a ordenação em order_by
	 * @return type : retorna o último registro inserido ordenado de acordo com $order de forma DESC
	 */
	public function last($order = 'id') {
		return $this->db->select('*')
				->from($this->table)
				->order_by($order, 'DESC')
				->get()
				->result();
	}

	/**
	 *
	 * @param type $post : Insere um novo registro de $post (Array) na tabela $table
	 * e retorna o ID do registro inserido
	 */
	public function create($post) {
		$this->db->insert($this->table, $post);
		return $this->db->insert_id();
	}

	/**
	 *
	 * @param type $post
	 * @param type $value
	 * @param type $field
	 */
	public function update($post, $value, $field = 'id') {
		$this->db->where($field, $value);
		$this->db->update($this->table, $post);
		return $value;
	}

	/**
	 *
	 * @param type $value
	 * @param type $field
	 */
	public function delete($value, $field = 'id') {
		$this->db->where($field, $value);
		$this->db->delete($this->table);
		return true;
	}

	/**
	 * implementar paginacao
	 */
}
