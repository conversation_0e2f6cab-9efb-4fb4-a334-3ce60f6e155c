<?php

class Compras_model extends CI_Model
{

	function __construct()
	{

		parent::__construct();
	}

	public function dados_da_autorizacao()
	{
		$idcompra = $this->input->post('id_compra');
		$codcompra = $this->input->post('cod_compra');

		$this->db->where('id', $idcompra);

		$data['obs_interna'] = $this->input->post('obs_interna');

		$data['data_autorizacao_compra'] = date('Y-m-d');
		$data['status_pagamento'] = $this->input->post('status_pagamento');

		$this->db->update('tbl_solicitacao_de_compra', $data);

		$this->insert_alertas($idcompra);

		if ($_FILES) {
			$this->load->library('upload');

			foreach ($_FILES as $key => $doc) {
				if ($key != 'condicoes_gerais' && $doc['name'] != "") {
					if ($this->enviar_doc_para_pasta($idcompra, (object) $doc, $key, $codcompra)) {
						$this->indexarApolice($key, (object) $doc, $codcompra, $idcompra);
					}
				}
			}
		}

		return true;
	}


	private function insert_alertas($idcompra)
	{
		$dadosRenovacao = [];
		if ($this->input->post('nome_alerta_renovacao') && $this->input->post('email_alerta_renovacao') && $this->input->post('data_alerta_renovacao')) {

			$dadosRenovacao = [
				'nome_cliente' => $this->input->post('nome_alerta_renovacao'),
				'email_cliente' => $this->input->post('email_alerta_renovacao'),
				'data_alerta' => date_human_to_mysql($this->input->post('data_alerta_renovacao')),
				'tipo_alerta' => 'R',
				'id_venda' => $idcompra
			];
		}

		$dadosFeedback = [];
		if ($this->input->post('nome_alerta_feedback') && $this->input->post('email_alerta_feedback') && $this->input->post('data_alerta_feedback')) {
			$dadosFeedback = [
				'nome_cliente' => $this->input->post('nome_alerta_feedback'),
				'email_cliente' => $this->input->post('email_alerta_feedback'),
				'data_alerta' => date_human_to_mysql($this->input->post('data_alerta_feedback')),
				'tipo_alerta' => 'F',
				'id_venda' => $idcompra
			];
		}

		$this->db->select('tba.*');
		$this->db->from('tbl_alertas_cronjob tba');
		$this->db->where('tba.id_venda', $idcompra);
		$this->db->where('tba.tipo_alerta', 'R');
		$alertRenovacao = $this->db->get()->row();

		if (isset($alertRenovacao)) {
			$this->db->where('id', $alertRenovacao->id);
			$this->db->update('tbl_alertas_cronjob', $dadosRenovacao);
		} elseif($dadosRenovacao) {
			$this->db->insert('tbl_alertas_cronjob', $dadosRenovacao);
		}


		$this->db->select('tba.*');
		$this->db->from('tbl_alertas_cronjob tba');
		$this->db->where('tba.id_venda',  $idcompra);
		$this->db->where('tba.tipo_alerta', 'F');
		$alertFeedback = $this->db->get()->row();

		if (isset($alertFeedback)) {
			$this->db->where('id', $alertFeedback->id);
			$this->db->update('tbl_alertas_cronjob', $dadosFeedback);
		} elseif($dadosFeedback) {
			$this->db->insert('tbl_alertas_cronjob', $dadosFeedback);
		}

	}

	private function indexarApolice($key, $doc, $codcompra, $idcompra)
	{
		$explode = explode('_', $key);
		$idUser = $explode[1];

		$this->db->select('*');
		$this->db->where('id', $idUser);
		$viajante = $this->db->get('tbl_viajantes')->row();

		$nameDoc = $this->tratarName($doc->name);

		$data = array(
			'id_cliente'   => $idUser,
			'cpf_cliente'  => limpaCPF_CNPJ($viajante->cpf),

			'cod_compra'   => $codcompra,
			'id_compra'    => $idcompra,

			'nome_apolice' => $idUser . '_' . $nameDoc,
			'link_apolice' => 'documentos/apolices/' . $codcompra . '/' . $idUser . '_' . $nameDoc
		);

		$this->db->select('*');
		$this->db->where('id_cliente', $idUser);
		$this->db->where('id_compra', $idcompra);
		$encontrado = $this->db->get('tbl_cliente_apolice')->row();

		if ($encontrado) {
			$this->db->where('id', $encontrado->id);
			$this->db->update('tbl_cliente_apolice', $data);
		} else {
			return $this->db->insert('tbl_cliente_apolice', $data);
		}
	}

	private function tratarName($name)
	{
		return str_replace(" ", "_", $name);
		//return  str_replace(".", "_", $result);
	}

	private function enviar_doc_para_pasta($idcompra, $doc, $key, $codcompra)
	{
		$explode = explode('_', $key);
		$idUser = $explode[1];

		$config['file_name'] = $idUser . '_' . $doc->name;
		$config['allowed_types'] = 'gif|jpg|jpeg|png|pdf|doc|tif';
		$config['max_size'] = '5000';

		# Verifica se existe o diretorio para salvar imagens
		if (!is_dir("./documentos/apolices/$codcompra")) {
			@mkdir("./documentos/apolices/", 0777, $recursive = true);
			@mkdir("./documentos/apolices/$codcompra", 0777, $recursive = true);
		}

		#define o caminho para salvar as imagens
		$path = "./documentos/apolices/$codcompra";
		$config['upload_path'] = $path;

		#Inicializa o método de upload
		$this->upload->initialize($config);

		#processa o upload e verifica o status
		if (!$this->upload->do_upload($key)) {
			$aux = false;
			//Determina o status do header
			$this->output->set_status_header('400');
			//Retorna a mensagem de erro a ser exibida
			echo $this->upload->display_errors(null, null);
		}

		return true;
	}
}
