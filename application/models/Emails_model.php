<?php

class Emails_model extends CI_Model
{
	function __construct()
	{
		parent::__construct();
	}

	public function send_email_product_revision_client($id_product, $email_cc = null)
	{
		$this->db->select('up.*, uc.name as user_name, uc.email as user_email');
		$this->db->from('tbl_user_products up');
		$this->db->join('tbl_user_clients uc','uc.id = up.user_id');
		$this->db->where('id', $id_product);
		$client = $this->db->get()->row();

		$client_name = $client->user_name;
		$client_email = $client->user_email;

		$config = configemail();

		$this->load->library('email', $config);
		$this->email->set_newline("\r\n");

		$this->email->from('<EMAIL>', 'BigHub');

		$data = array(
			'saudacao' 	  => saudacao(),
			'client_name' => $client_name,
			'dateday' 	  => date('d/m/Y')
		);

		$this->email->subject('Seu produto foi enviado para revisão');
		$this->email->to($client_email);

		if ($email_cc) {
			$this->email->cc(
				array($email_cc)
			);
		}

		$body = $this->load->view('emails/send_email_product_pending.html', $data, TRUE);

		$this->email->message($body);
		$this->email->send();
		return TRUE;
	}
}
