<?php

class Condicoes_model extends CI_Model {
	private $table = 'tbl_condicoes_gerais';
	
	function __construct() {
		parent::__construct();
	}

	public function getLast() {
		return $this->db->select('*')
				->from($this->table)
				->get()
				->result();
	}

	function Inserir($data) {
		$data['data_cadastro'] = date('Y-m-d');
		if (!isset($data))
			return false;
		return $this->db->insert($this->table, $data);
	}

	function Atualizar($id, $data) {
		if (is_null($id) || !isset($data))
			return false;
		$this->db->where('id', $id);
		return $this->db->update($this->table, $data);
	}

}
