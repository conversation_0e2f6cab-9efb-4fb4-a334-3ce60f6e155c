<?php

class Termo_model extends CI_Model {

	function __construct() {

		parent::__construct();
	}

	public function getLast() {
		return $this->db->select('*')
				->from('tbl_termo')
				->get()
				->result();
	}

	function Inserir($data) {
		$data['data_cadastro'] = date('Y-m-d');
		if (!isset($data))
			return false;
		return $this->db->insert('tbl_termo', $data);
	}

	function Atualizar($id, $data) {
		if (is_null($id) || !isset($data))
			return false;
		$this->db->where('id', $id);
		return $this->db->update('tbl_termo', $data);
	}
}
