<?php

class Lembretes_model extends CI_Model
{

	public function marcar_como_checked($cod)
	{
		$this->db->where('cod_lembrete', $cod);
		$data['is_checked'] = 1;
		return $this->db->update('tbl_lembretes', $data);
	}

	public function marcar_como_excluido($cod)
	{
		$this->db->where('cod_lembrete', $cod);
		$data['is_deleted'] = 1;
		return $this->db->update('tbl_lembretes', $data);
	}



	

	public function salvar_testemunho()
	{

		$data['data_inserido'] = date('Y/m/d');
		$data['cadastrado_por'] = $this->session->userdata('nome_usuario');
		$data['cliente'] = $this->input->post('cliente');
		$data['msg'] = $this->input->post('msg');
		return $this->db->insert('tbl_testemunhos', $data);
	}

	public function salvar_edicao($id)
	{
		$this->db->where('id', $id);

		$data['cliente'] = $this->input->post('cliente');
		$data['msg'] = $this->input->post('msg');
		$data['cadastrado_por'] = $this->session->userdata('nome_usuario');

		return $this->db->update('tbl_testemunhos', $data);
		//return $this->db->delete('tbl_testemunhos');
	}


}
