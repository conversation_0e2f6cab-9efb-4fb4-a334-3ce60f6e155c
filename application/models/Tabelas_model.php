<?php

class Tabelas_model extends CI_Model {

	
	public function save() { 

		$data['author'] = $this->session->userdata('nome_usuario');
		$data['nome_tabela'] = $this->input->post('nome_tabela');
		$data['status'] = $this->input->post('status');
		return $this->db->insert('tbl_tabela', $data);
	}

	public function salvar_edicao($id) {
		$this->db->where('id', $id);

		$data['cliente'] = $this->input->post('cliente');
		$data['msg'] = $this->input->post('msg');
		$data['cadastrado_por'] = $this->session->userdata('nome_usuario');

		return $this->db->update('tbl_testemunhos', $data);
	}

	public function excluir_tabela($id) {
		$this->db->where('id_tabela', $id);
		return $this->db->delete('tbl_tabela');
	}



}
