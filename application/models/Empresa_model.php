<?php

class Empresa_model extends CI_Model {

	function __construct() {

		parent::__construct();
	}

	public function getLast() {
		return $this->db->select('*')
				->from('tbl_empresa')
				->get()
				->result();
	}

	function Inserir($data) {
		$data['data_cadastro'] = date('Y-m-d');
		if (!isset($data))
			return false;
		return $this->db->insert('tbl_empresa', $data);
	}

	function Atualizar($id, $data) {
		if (is_null($id) || !isset($data))
			return false;
		$this->db->where('id', $id);
		return $this->db->update('tbl_empresa', $data);
	}

}
