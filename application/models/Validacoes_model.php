<?php

class Validacoes_model extends CI_Model {

	function __construct() {

		parent::__construct();
	}

# funcoes para verifica se a senha digitada no altera senha no painel do prestador é valida
#==========================================================================================================

	public function verificando_se_senha_valida($id_gerente, $senha) {
		$this->db->select('id_usuario, senha');
		$this->db->where('id_usuario', $id_gerente);
		$gerente = $this->db->get('tbl_usuarios')->row();

		if ($gerente->senha == md5($senha)) {
			return true;
		}
	}

}
