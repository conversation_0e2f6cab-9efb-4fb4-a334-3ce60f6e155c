<?php

class Testemunho_model extends CI_Model {

	
	public function salvar_testemunho() { 

		$data['data_inserido'] = date('Y/m/d');
		$data['cadastrado_por'] = $this->session->userdata('nome_usuario');
		$data['cliente'] = $this->input->post('cliente');
		$data['msg'] = $this->input->post('msg');
		return $this->db->insert('tbl_testemunhos', $data);
	}

	public function salvar_edicao($id) {
		$this->db->where('id', $id);

		$data['cliente'] = $this->input->post('cliente');
		$data['msg'] = $this->input->post('msg');
		$data['cadastrado_por'] = $this->session->userdata('nome_usuario');

		return $this->db->update('tbl_testemunhos', $data);
	}

	public function excluir_testemunho($id) {
		$this->db->where('id', $id);
		return $this->db->delete('tbl_testemunhos');
	}



}
