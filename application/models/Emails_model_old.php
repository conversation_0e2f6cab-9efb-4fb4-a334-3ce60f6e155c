<?php

class Emails_model extends CI_Model
{

	function __construct()
	{
		parent::__construct();
	}

	public function compra_autorizada($id_compra)
	{
		$this->db->select('sc.*, s.seguradora');
		$this->db->where('sc.id', $id_compra);
		$this->db->join('tbl_newseguros s', 'sc.id_plano = s.id');
		$resultado = $this->db->get('tbl_solicitacao_de_compra sc')->row();

		$this->db->select('termos, nome_seguradora');
		$this->db->where('id', $resultado->seguradora);
		$condicoes = $this->db->get('tbl_seguradoras')->row();

		// $this->db->select('nome');
		// $this->db->where('cod_compra', $resultado->cod_compra);
		// $seguradora = $this->db->get('tbl_viajantes')->row();

		// $this->db->select('nome');
		// $this->db->where('cod_compra', $resultado->cod_compra);
		// $result = $this->db->get('tbl_viajantes')->result();

		// $viajantes = '';
		// foreach ($result as $r) {
		// 	$viajantes .= $r->nome . ', ';
		// }

		$this->db->select('v.*, tca.nome_apolice as apolice, tca.link_apolice, tca.nome_apolice');
		$this->db->from('tbl_viajantes v');
		$this->db->join('tbl_cliente_apolice tca', 'tca.id_cliente = v.id', 'left');
		$this->db->where('v.cod_compra', $resultado->cod_compra);
		$viajantes = $this->db->get()->result();

		$apolices = '';
		foreach ($viajantes as $key => $v) {
			// $result[$key] = [
			// 	"nome" => $v->nome,
			// 	"link_apolice" => base_url().$v->link_apolice
			// ];

			$apolices .= ' <a href="' . base_url() . $v->link_apolice . '"> ' . $v->nome . ' ( apólice )</a><br><br>';
		}


		if ($resultado->tipo_pagamento == 'B') {
			$tipo = 'Boleto';
		} elseif ($resultado->tipo_pagamento == 'CC') {
			$tipo = 'Cartão de Crédito';
		} elseif ($resultado->tipo_pagamento == 'TB') {
			$tipo = 'Tranferênca Bancária';
		} else {
			$tipo = 'Não possui tipo de pagamento selecionado';
		}

		$config = configemail();
		$this->load->library('email', $config);
		$this->email->set_newline("\r\n");

		$this->email->from('<EMAIL>', 'Otripulante - Compra autorizada');

		$link_termo = base_url() . 'documentos/condicoes-gerais/' . $condicoes->termos;

		$data = array(
			'saudacao' => saudacao(),
			'nome_cliente' => $resultado->primeiro_nome,

			'plano' => nome_plano($resultado->id_plano),
			'vigencia' => date_mysql_to_human($resultado->data_ida) . ' a ' . date_mysql_to_human($resultado->data_volta),

			'apolices' => $apolices,
			'valor_total' => FormatarValor($resultado->valor_com_desconto),
			'forma_pagamento' => $tipo,

			'seguradora' => $condicoes->nome_seguradora,
			'termo' =>  '<a href="'.$link_termo.'" target="_blank">Condições Gerais - ' . $condicoes->nome_seguradora . '</a>'
		);

		// var_dump($data);
		// die();

		$this->email->subject('Sua compra foi autorizada');
		//$this->email->to($resultado->endereco_email);
		$this->email->to('<EMAIL>');
		$this->email->bcc('<EMAIL>');

		$body = $this->load->view('emails/confirmacao_compra.html', $data, TRUE);

		$this->email->message($body);
		$this->email->send();
		return TRUE;
	}







	public function enviar_cotacao($tabela, $destino, $ida, $volta, $viajantes, $nome_cliente, $email_cliente, $email_cc, $observacao, $titulo, $dias, $valores_rodape)
	{

		$passageiros = $viajantes . ' passageiro';
		$frase = 'Segue abaixo sua cotação!';
		$seguro = 'O melhor seguro para você.';

		if ($viajantes > 1) {
			$passageiros = $viajantes . ' passageiros';
			$frase = 'Seguem abaixo suas cotações!';
			$seguro = 'Os melhores seguros para você.';
		}

		$config = configemail();
		$this->load->library('email', $config);
		$this->email->set_newline("\r\n");

		$this->email->from('<EMAIL>', 'Otripulante - ' . $seguro);

		// print_r($valores_rodape);
		// die('fim');

		$data = array(
			'saudacao' => saudacao(),
			'dateday' => date('d/m/Y'),
			'nome_cliente' => $nome_cliente,
			'vigencia' => $ida . ' até ' . $volta,
			'destino' => $destino,
			'passageiros' => $passageiros,
			'tabela_planos' => $tabela,
			'obs' => $observacao,
			'frase' => $frase,
			'dias' => $dias,
			'titulo' => $titulo,
			'valores_rodape' => $valores_rodape
		);


		$this->email->subject($seguro);
		$this->email->to($email_cliente);

		if ($email_cc) {
			$this->email->cc(
				array($email_cc)
			);
		}


		//$this->email->bcc('<EMAIL>');

		$body = $this->load->view('emails/envio_de_cotacoes.html', $data, TRUE);
		$this->email->message($body);
		$this->email->send();
		return TRUE;
	}
}
