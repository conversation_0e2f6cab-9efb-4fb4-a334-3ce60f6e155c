<?php

class Compras_model extends CI_Model
{

	function __construct()
	{

		parent::__construct();
	}

	public function dados_da_autorizacao()
	{
		$idcompra = $this->input->post('id_compra');
		$codcompra = $this->input->post('cod_compra');

		$this->db->where('id', $idcompra);

		$data['obs_interna'] = $this->input->post('obs_interna');

		$data['data_autorizacao_compra'] = date('Y-m-d');
		$data['status_pagamento'] = $this->input->post('status_pagamento');

		$this->db->update('tbl_solicitacao_de_compra', $data);

		if ($_FILES) {
			$this->load->library('upload');

			foreach ($_FILES as $key => $doc) {
                if ($key != 'condicoes_gerais' && $doc['name'] != "") {
                    if ($this->enviar_doc_para_pasta($idcompra, (object) $doc, $key, $codcompra)) {
                        $this->indexarApolice($key, (object) $doc, $codcompra, $idcompra);
                    }
                }	
			}
		}

		return true;
	}

	private function indexarApolice($key, $doc, $codcompra, $idcompra)
	{
	
			$explode = explode('_', $key);
			$idUser = $explode[1];

			$this->db->select('*');
			$this->db->where('id', $idUser);
			$viajante = $this->db->get('tbl_viajantes')->row();

			$nameDoc = $this->tratarName($doc->name);

			$data = array(
				'id_cliente'   => $idUser,
				'cpf_cliente'  => limpaCPF_CNPJ($viajante->cpf),

				'cod_compra'   => $codcompra,
				'id_compra'    =>$idcompra,

				'nome_apolice' => $nameDoc,
				'link_apolice' => 'documentos/apolices/'.$codcompra.'/'.$nameDoc
			);
	
			return $this->db->insert('tbl_cliente_apolice', $data);	
		
	}

	private function tratarName($name){
		return str_replace(" ", "_", $name);
		//return  str_replace(".", "_", $result);
	}

	private function enviar_doc_para_pasta($idcompra, $doc, $key, $codcompra)
	{
    		$config['file_name'] = $doc->name;
			$config['allowed_types'] = 'gif|jpg|jpeg|png|pdf|doc|tif';
			$config['max_size'] = '5000';

			# Verifica se existe o diretorio para salvar imagens
			if (!is_dir("./documentos/apolices/$codcompra")) {
				@mkdir("./documentos/apolices/", 0777, $recursive = true);
				@mkdir("./documentos/apolices/$codcompra", 0777, $recursive = true);
			}

			#define o caminho para salvar as imagens
			$path = "./documentos/apolices/$codcompra";
			$config['upload_path'] = $path;

			#Inicializa o método de upload
			$this->upload->initialize($config);

			#processa o upload e verifica o status
			if (!$this->upload->do_upload($key)) {
				$aux = false;
				//Determina o status do header
				$this->output->set_status_header('400');
				//Retorna a mensagem de erro a ser exibida
				echo $this->upload->display_errors(null, null);
			}

			return true;
	}
}
