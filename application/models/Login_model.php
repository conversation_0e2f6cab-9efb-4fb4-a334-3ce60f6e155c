<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

class Login_Model extends CI_Model {

    public function __construct() {
        parent::__construct();
        //Do your magic here
    }

    public function getLogin($user, $pass) {
        // Primeiro precisamos obter o usuário pelo email para ter acesso ao hash da senha
        $usuario = $this->buscar_usuario_por_email($user);
        
        if ($usuario && password_verify($pass, $usuario->senha)) {
            // Se a senha for verificada com sucesso, pegamos os dados completos
            $dados = $this->pegando_usuario_pelo_id($usuario->id_usuario);
            
            if ($dados->ativo == 0){
                $dados->erro = 2; /* Usuario Desativado */
            } else {
                $dados->erro = 0;
            }
        } else {
            $dados = json_decode('{"erro":1}');
        }
        
        return $dados;
    }

    public function buscar_usuario_por_email($email) {
        $this->db->select('id_usuario, senha, ativo');
        $this->db->where('email', $email);
    
        $query = $this->db->get('tbl_usuarios');
        
        if ($query->num_rows() > 0) {
            return $query->row();
        } else {
            return null;
        }
    }
    

    /*
    * Metodo é chamado assim que usuario solicita o login
    * É vericado atrvés do usuario e senha digitados
    */
     public function getLogin_old($user, $pass) {
            $id = $this->verificar_usuario($user, md5($pass));

            if ($id > 0) {
             
                    $dados = $this->pegando_usuario_pelo_id($id);
    
                if ($dados->ativo == 0){
                     $dados->erro = 2; /* Usuario Desativado */
                }else{
                    $dados->erro = 0;
                }
            } else {
                $dados = json_decode('{"erro":1}');
            }

            return $dados;
    }

    /*
    * Veifica se o usuario existe no banco de dados
    * É vericado através do usuario e senha digitados
    */
    public function verificar_usuario($user, $pass) {
        $ret = 0;
        $this->db->select('id_usuario');
        $this->db->where('email', $user);
        $this->db->where('senha', $pass);

        $query = $this->db->get('tbl_usuarios');
        
        if ($query->num_rows() > 0) {
            $result = $query->row();
            $ret = $result->id_usuario;
        } else {
            $ret = 0;
        }
         return $ret;
    }

   /*
   * Pegando os dados do USUARIO que está se logando
   */ 
    public function pegando_usuario_pelo_id($id) {
        $this->db->select("tbl_usuarios.*, '0' as erro");
        $this->db->from('tbl_usuarios');
        $this->db->where('id_usuario', $id);
        return $this->db->get()->row();
    }

    /*
    * Faz a inserção de Logs
    */
    public function inserir_log($id, $token, $tipo) {
       $dados = array(
           'id_usuario'      => $id,
           'token'           => $token,
           'tipo_de_acesso'  => $tipo,
           'data_de_acesso'  => date('Y-m-d'),
           'hora_de_acesso'  => date('H:i:s'),
           'ip_de_acesso'    => $this->input->ip_address()
        );
        return $this->db->insert('tbl_log_acesso_adm', $dados);
    }

    /*
    * Coloca o usuario online e offline
    // */
    // public function status_do_usuario($id, $status) {
    //     $this->db->where('id_usuario', $id);
    //     $dados = array('ONLINE' => $status);
    //     return $this->db->update('USUARIOS', $dados);
    //  }
}
