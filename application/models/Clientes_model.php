<?php

class Clientes_model extends CI_Model {


	public function update_status_user($user_token, $status){
		$this->db->where('user_token', $user_token);
		$data = [
			'user_active' => $status
		];
		return $this->db->update('tbl_user_clients', $data);
	}

	public function update_status_user_tbl_subscription($user_token, $status){

		// echo json_encode($status);
		// die();
		$state = 'pending';
		if($status == 1){
			$state = 'active';
		}
		
		$user = $this->db->where('user_token', $user_token)->get('tbl_user_clients')->row();

		// echo json_encode($state);
		// die();
		if(isset($user) && !empty($user)){
			$this->db->where('user_id', $user->id);
			$data = [
				'status' => $state
			];
			return $this->db->update('tbl_subscriptions', $data);
		}
		
	}










	public function salvar_cliente($id_user_system) {

		$codclient = hash('sha512', date('Y-m-d H:i:s'));
		
		$data['id_usuario_system'] = $id_user_system;
	
		$data['codcliente'] = $codclient;
		$data['data_cadastro'] = date('Y-m-d');

		$data['nascimento'] = date_human_to_mysql($this->input->post('data_nascimento'));

		$data['documento'] = $this->input->post('documento');

		$data['nome'] = $this->input->post('nome');
		$data['email'] = $this->input->post('email');
		$data['fone'] = $this->input->post('telefone');
		$data['genero'] = $this->input->post('genero');

		$data['codpostal'] = $this->input->post('codpostal');
		$data['endereco_morada'] = $this->input->post('endereco_morada');
		$data['numero'] = $this->input->post('numero');

		# Freguesia - Equivale a um conjunto de bairros
		$data['bairro_freguesia'] = $this->input->post('bairro_freguesia');

		# Concelho - Equivale a cidade
		$data['cidade_concelho'] = $this->input->post('cidade_concelho');

		# Distrito - Equivale aos estados
		$data['uf_distrito'] = $this->input->post('uf_distrito');
		
		$data['complemento'] = $this->input->post('complemento');
		$data['status'] = $this->input->post('status_cliente');
		$data['obs_interna'] = $this->input->post('observacoes_internas');

		return $this->db->insert('tbl_clientes', $data);
	}



	public function salvar_edicao($codcliente) {
		$this->db->where('codcliente', $codcliente);

		$data['nascimento'] = date_human_to_mysql($this->input->post('data_nascimento'));

		$data['documento'] = $this->input->post('documento');

		$data['nome'] = $this->input->post('nome');
		$data['email'] = $this->input->post('email');
		$data['fone'] = $this->input->post('telefone');
		$data['genero'] = $this->input->post('genero');

		$data['codpostal'] = $this->input->post('codpostal');
		$data['endereco_morada'] = $this->input->post('endereco_morada');
		$data['numero'] = $this->input->post('numero');

		# Freguesia - Equivale a um conjunto de bairros
		$data['bairro_freguesia'] = $this->input->post('bairro');

		# Concelho - Equivale a cidade
		$data['cidade_concelho'] = $this->input->post('cidade');

		# Distrito - Equivale aos estados
		$data['uf_distrito'] = $this->input->post('uf');
		
		$data['complemento'] = $this->input->post('complemento');
		$data['status'] = $this->input->post('status_cliente');
		$data['obs_interna'] = $this->input->post('observacoes_internas');

		return $this->db->update('tbl_clientes', $data);
	}



	public function excluir($codcli) {
		$historico = $this->verificar_agendamentos_do_cliente($codcli);
		if(!empty($historico)){
			$this->excluir_historico($historico);
		}
		return $this->excluindo_cliente($codcli);

	}


	private function verificar_agendamentos_do_cliente($codcli)
	{
		$this->db->select('id, cod_agendamento');
		
		$this->db->where('codcliente', $codcli);
		$this->db->from('tbl_agendamentos');
		return $this->db->get()->result();
	}


	private function excluir_historico($historico)
	{
		foreach($historico as $agendamento){
			 $this->db->where('cod_agendamento', $agendamento->cod_agendamento);
			 $this->db->delete('tbl_agendamentos');
		}
		return true;
		
	}

	private function excluindo_cliente($codcli)
	{
		$this->db->where('codcliente', $codcli);
		return $this->db->delete('tbl_clientes');
	}


	private function getClientId($codcli)
	{
				// $id = $this->getClientId($codcli)->id;
		// var_dump($id);
		// die();

		$this->db->select('id');
		$this->db->where('codcliente', $codcli);
		$this->db->from('tbl_clientes');
		return $this->db->get()->row();
	}

}
