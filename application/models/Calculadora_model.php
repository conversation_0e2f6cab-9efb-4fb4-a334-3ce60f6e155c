<?php

class Calculadora_model extends CI_Model {

	
	public function grava_cotacao($dados) { 
		return $this->db->insert('tbl_cotacoes_enviadas', $dados);
	}
	

	public function gravar_dados_dessa_cotacao($dados) { 
		return $this->db->insert('tbl_cotacoes_enviadas_planos', $dados);
	}


	public function excluir_cotacao($id) {
		$this->db->where('id', $id);
		return $this->db->delete('tbl_cotacoes_enviadas');
	}



	

	// public function salvar_edicao($id) {
	// 	$this->db->where('id_cupom', $id);

	// 	$data =  array(
	// 		'nome_cupom' => $this->input->post('nome_cupom'),
	// 		'tipo_desconto' => $this->input->post('tipo_desconto'),
	// 		'valor_desconto' => $this->input->post('valor'),
	// 		'data_expira' => date_human_to_mysql($this->input->post('data_expira')),
	// 		'status' => $this->input->post('status'),
	// 		'obs' => $this->input->post('obs')
	// 	);
	// 	return $this->db->update('tbl_cupom', $data);
	// }

	// public function excluir_cupom($id) {
	// 	$this->db->where('id_cupom', $id);
	// 	return $this->db->delete('tbl_cupom');
	// }



}
