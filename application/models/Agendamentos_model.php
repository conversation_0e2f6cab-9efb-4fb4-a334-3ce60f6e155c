<?php

class Agendamentos_model extends CI_Model
{
	public function salvar_agendamento($user_system, $idProfissional, $tipo_agendamento, $clienteId)
	{
		$cod_agendamento = $this->gerar_cod_agendamento();
		$data['tipo_agendamento'] = $tipo_agendamento;
		$data['cod_agendamento'] = $cod_agendamento;

		if ($tipo_agendamento == 1) {
			$cod_profissional = $this->input->post('profissional');
			$cod_cliente = $this->input->post('cod_cliente');
			
			$data +=  array(
				'id_cliente' => $clienteId,
				'codcliente' => $cod_cliente,
				'id_servico' => $this->input->post('servico'),
				'id_profissional' => $idProfissional,
				'cod_profissional' => $cod_profissional,
				'data' => date_human_to_mysql($this->input->post('data_profissional')),
				'hora' => $this->input->post('hora_profissional'),
				'link_confirmacao' => 'https://salaodoscachos.com/agendamento/'.$cod_agendamento,
			);
		}

		if ($tipo_agendamento == 2) {
			$data +=  array(
				'titulo' => $this->input->post('compromisso'),
				'data' => date_human_to_mysql($this->input->post('data_pessoal')),
				'hora' => $this->input->post('hora_pessoal'),
			);
		}

		$data['user_system'] = $user_system;
		$data['status'] = $this->input->post('status_agendamento');
		$data['observacao'] = $this->input->post('observacoes_internas');

		return $this->db->insert('tbl_agendamentos', $data);
	}

	private function gerar_cod_agendamento(){
		return hash('sha512', date('Y-m-d H:i:s'));
	}


	public function salvar_edicao($id)
	{
		$this->db->where('id_cupom', $id);

		$data =  array(
			'nome_cupom' => $this->input->post('nome_cupom'),
			'tipo_desconto' => $this->input->post('tipo_desconto'),
			'valor_desconto' => $this->input->post('valor'),
			'data_expira' => date_human_to_mysql($this->input->post('data_expira')),
			'status' => $this->input->post('status'),
			'obs' => $this->input->post('obs')
		);
		return $this->db->update('tbl_cupom', $data);
	}

	public function excluir_cupom($id)
	{
		$this->db->where('id_cupom', $id);
		return $this->db->delete('tbl_cupom');
	}
}
