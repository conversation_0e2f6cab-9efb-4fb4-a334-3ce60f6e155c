<?php

class Seguros_model extends CI_Model {

	/** CRUD
	 * Author: Sisdado
	 */
	public $table = '';

	function __construct() {
		parent::__construct();
	}

	function setTable($tabela) {
		$this->table = $tabela;
	}

	function getTable() {
		return $this->table;
	}

	function getAll($order_by = 'id', $ordem = 'DESC') {
		$this->db->select('*')
			->from($this->table)
			->order_by($order_by, $ordem);
		$q = $this->db->get()->result();
		return $q;
	}

	function Cadastrar($data) {
		$data['data_cadastro'] = date('Y-m-d');
		if (!isset($data)) {
			return false;
		}
		return $this->db->insert($this->table, $data);
	}

	function Atualizar($id, $data) {
		if (is_null($id) || !isset($data)) {
			return false;
		}
		$this->db->where('id', $id);
		return $this->db->update($this->table, $data);
	}

	function Deletar($id) {
		return $this->db->where('id', $id)
				->delete($this->table);
	}

	/** END CRUD * */
	public function cadastrar_plano() {
		$data['data_cadastro'] = date('Y/m/d');
		$data['cadastrado_por'] = $this->session->userdata('nome_usuario');
		$data['seguradora'] = $this->input->post('seguradora');
		$data['nome_plano'] = $this->input->post('nome_plano');
		$data['id_maxima'] = $this->input->post('id_maxima');
		$data['tipo_plano'] = $this->input->post('tipo_plano');
		$data['multi_viagem'] = $this->input->post('multi_viagem');
		$data['valor_dia'] = $this->input->post('valor_dia');
		$data['maximo_dias'] = $this->input->post('maximo_dias');
		$data['obs_cliente'] = $this->input->post('obs_cliente');
		$data['status_plano'] = $this->input->post('status_plano');

		$data['minimo_dias'] = $this->input->post('minimo_dias');
		$data['idade_adicional_tarifa'] = $this->input->post('idade_adicional_tarifa');
		$data['porcentagem_adicional'] = $this->input->post('porcentagem_adicional');

		$data['despesas_medicas_e_hospitalares'] = $this->input->post('despesas_medicas_e_hospitalares');
		$data['despesas_medicas_para_doenca_pre_existente'] = $this->input->post('despesas_medicas_para_doenca_pre_existente');
		$data['despesas_medicas_para_esportes_lazer'] = $this->input->post('despesas_medicas_para_esportes_lazer');
		$data['despesas_odontologicas'] = $this->input->post('despesas_odontologicas');
		$data['despesas_farmaceuticas'] = $this->input->post('despesas_farmaceuticas');
		$data['reparticao_sanitaria'] = $this->input->post('reparticao_sanitaria');

		$data['reparticao_de_morte'] = $this->input->post('reparticao_de_morte');
		$data['translado_medico'] = $this->input->post('translado_medico');
		$data['reembolso_de_gastos_por_demora'] = $this->input->post('reembolso_de_gastos_por_demora');
		$data['seguro_de_invalidez_permanente_acidente'] = $this->input->post('seguro_de_invalidez_permanente_acidente');
		$data['seguro_de_morte_acidental'] = $this->input->post('seguro_de_morte_acidental');
		$data['regresso_de_menor_ou_idoso'] = $this->input->post('regresso_de_menor_ou_idoso');
		$data['extensao_de_internacao_hospitalar'] = $this->input->post('extensao_de_internacao_hospitalar');
		$data['recuperacao_de_enfermidade'] = $this->input->post('recuperacao_de_enfermidade');
		$data['despesas_extraordinarias_porpermanencia'] = $this->input->post('despesas_extraordinarias_porpermanencia');
		$data['regresso_antecipado'] = $this->input->post('regresso_antecipado');
		$data['acompanhamento_familiar'] = $this->input->post('acompanhamento_familiar');
		$data['cancelamento_de_viagem'] = $this->input->post('cancelamento_de_viagem');
		$data['extravio_de_bagagem'] = $this->input->post('extravio_de_bagagem');
		$data['interrupcao_de_viagem'] = $this->input->post('interrupcao_de_viagem');
		$data['orientacao_em_caso_de_perda_de_documentos'] = $this->input->post('orientacao_em_caso_de_perda_de_documentos');
		$data['assistencia_juridica_em_caso_de_acidente'] = $this->input->post('assistencia_juridica_em_caso_de_acidente');
		$data['assistencia_de_fianca_em_caso_de_acidente'] = $this->input->post('assistencia_de_fianca_em_caso_de_acidente');
		$data['cobertura_covid'] = $this->input->post('cobertura_covid');
		$data['observacoes_internas'] = $this->input->post('observacoes_internas');

		$data['selo_seguro'] = $this->input->post('selo');
		$data['nacional'] = $this->input->post('nacional');
		$data['seguro_europa'] = $this->input->post('seguro_europa');
		$data['seguro_eua'] = $this->input->post('seguro_eua');
		$data['relevancia'] = $this->input->post('relevancia');
		$data['disponibilidade'] = $this->input->post('disponibilidade');

		$data['id_tabela'] = $this->input->post('tabela_seguradora');


		return $this->db->insert('tbl_newseguros', $data);
	}

	public function salvar_edicao($id) {
		$this->db->where('id', $id);

		$data['seguradora'] = $this->input->post('seguradora');
		$data['nome_plano'] = $this->input->post('nome_plano');
		$data['id_maxima'] = $this->input->post('id_maxima');
		$data['tipo_plano'] = $this->input->post('tipo_plano');
		$data['multi_viagem'] = $this->input->post('multi_viagem');
		$data['valor_dia'] = $this->input->post('valor_dia');
		$data['maximo_dias'] = $this->input->post('maximo_dias');
		$data['obs_cliente'] = $this->input->post('obs_cliente');
		$data['status_plano'] = $this->input->post('status_plano');

		$data['minimo_dias'] = $this->input->post('minimo_dias');
		$data['idade_adicional_tarifa'] = $this->input->post('idade_adicional_tarifa');
		$data['porcentagem_adicional'] = $this->input->post('porcentagem_adicional');

		$data['despesas_medicas_e_hospitalares'] = $this->input->post('despesas_medicas_e_hospitalares');
		$data['despesas_medicas_para_doenca_pre_existente'] = $this->input->post('despesas_medicas_para_doenca_pre_existente');
		$data['despesas_medicas_para_esportes_lazer'] = $this->input->post('despesas_medicas_para_esportes_lazer');
		$data['despesas_odontologicas'] = $this->input->post('despesas_odontologicas');
		$data['despesas_farmaceuticas'] = $this->input->post('despesas_farmaceuticas');
		$data['reparticao_sanitaria'] = $this->input->post('reparticao_sanitaria');

		$data['reparticao_de_morte'] = $this->input->post('reparticao_de_morte');
		$data['translado_medico'] = $this->input->post('translado_medico');
		$data['reembolso_de_gastos_por_demora'] = $this->input->post('reembolso_de_gastos_por_demora');
		$data['seguro_de_invalidez_permanente_acidente'] = $this->input->post('seguro_de_invalidez_permanente_acidente');
		$data['seguro_de_morte_acidental'] = $this->input->post('seguro_de_morte_acidental');
		$data['regresso_de_menor_ou_idoso'] = $this->input->post('regresso_de_menor_ou_idoso');
		$data['extensao_de_internacao_hospitalar'] = $this->input->post('extensao_de_internacao_hospitalar');
		$data['recuperacao_de_enfermidade'] = $this->input->post('recuperacao_de_enfermidade');
		$data['despesas_extraordinarias_porpermanencia'] = $this->input->post('despesas_extraordinarias_porpermanencia');
		$data['regresso_antecipado'] = $this->input->post('regresso_antecipado');
		$data['acompanhamento_familiar'] = $this->input->post('acompanhamento_familiar');
		$data['cancelamento_de_viagem'] = $this->input->post('cancelamento_de_viagem');
		$data['extravio_de_bagagem'] = $this->input->post('extravio_de_bagagem');
		$data['interrupcao_de_viagem'] = $this->input->post('interrupcao_de_viagem');
		$data['orientacao_em_caso_de_perda_de_documentos'] = $this->input->post('orientacao_em_caso_de_perda_de_documentos');
		$data['assistencia_juridica_em_caso_de_acidente'] = $this->input->post('assistencia_juridica_em_caso_de_acidente');
		$data['assistencia_de_fianca_em_caso_de_acidente'] = $this->input->post('assistencia_de_fianca_em_caso_de_acidente');
		$data['observacoes_internas'] = $this->input->post('observacoes_internas');
		$data['selo_seguro'] = $this->input->post('selo');
		$data['nacional'] = $this->input->post('nacional');
		$data['seguro_europa'] = $this->input->post('seguro_europa');
		$data['seguro_eua'] = $this->input->post('seguro_eua');
		$data['relevancia'] = $this->input->post('relevancia');
		$data['disponibilidade'] = $this->input->post('disponibilidade');
		$data['cobertura_covid'] = $this->input->post('cobertura_covid');

		$data['id_tabela'] = $this->input->post('tabela_seguradora');


		return $this->db->update('tbl_newseguros', $data);
	}

	public function excluir_plano($id) {
		$this->db->where('id', $id);
		return $this->db->delete('tbl_newseguros');
	}

	public function salvar_dolar() {
		$this->db->where('id', 1);
		$data = array(
			'dolar_dia' => $this->input->post('dolar_dia')
		);
		return $this->db->update('tbl_configuracoes', $data);
	}

}
