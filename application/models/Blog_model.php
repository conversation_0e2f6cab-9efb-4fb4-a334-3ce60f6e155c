<?php

class Blog_model extends CI_Model {

	private $table;

	function __construct() {
		parent::__construct();
	}

	/**
	 *
	 * @param type $table : Indica a tabela a ser utilizada no CRUD
	 */
	public function setTable($table) {
		$this->table = $table;
	}

	/**
	 *
	 * @return type : Retorna a tabela utilizada nas operações
	 */
	public function getTable() {
		return $this->table;
	}

	function getAll($order_by = 'id', $ordem = 'DESC') {
		$this->db->select('*')
			->from($this->table)
			->order_by($order_by, $ordem);
		$q = $this->db->get()->result();
		return $q;
	}

	function Cadastrar($data) {
		$data['data_cadastro'] = date('Y-m-d');
		if (!isset($data)) {
			return false;
		}
		return $this->db->insert($this->table, $data);
	}

	function Atualizar($id, $data) {
		if (is_null($id) || !isset($data)) {
			return false;
		}
		$this->db->where('id', $id);
		return $this->db->update($this->table, $data);
	}

	function Deletar($id) {
		return $this->db->where('id', $id)
				->delete($this->table);
	}

	public function findAllCategoria($arr = '', $campo = 'id', $ord = 'desc') {
		$query = '';
		if (isset($arr['busca'])) {
			$termos = explode(' ', str_replace('  ', ' ', $arr['busca']));
			$i = 1;
			foreach ($termos as $item) {
				$termo = Uteis::space2like($item);
				if ($i < 2) {
					$query = "AND (categoria LIKE '" . $termo . "') ";
				} else {
					$query .= "OR (categoria LIKE '" . $termo . "') ";
				}
				$i++;
			}
		}
		if (isset($arr['status'])) {
			$query .= "AND a.status = '" . $arr['status'] . "' ";
		}
		if (isset($arr['itens'])) {
			$n_de_resultados = $arr['itens'];
		}
		if (isset($query) && !empty($query)) {
			$query = 'WHERE' . strstr($query, ' ');
		}

		$this->db->select("a.id, a.categoria, a.ordem, a.slug, a.status,
					date_format(a.date, '%d/%m/%Y') AS data_cadastro_formatada,
                    date_format(a.date, '%H:%i') AS hora_cadastro_formatada,
                    date_format(a.date, '%d/%m/%Y')as data_atualizada_formatada,
                    date_format(a.date, '%H:%i')as hora_atualizada_formatada")
			->from("$this->table a");
		if (isset($query) && !empty($query)) {
			$query = strstr($query, ' ');
			$this->db->where($query);
		}
		$this->db->order_by("a." . $campo, $ord);
		$query = $this->db->get()->result();

		return $query;
	}

	public function findAllBlog($arr = '', $campo = 'id', $ord = 'desc') {
		$query = '';
		if (isset($arr['busca'])) {
			$termos = explode(' ', str_replace('  ', ' ', $arr['busca']));
			$i = 1;
			foreach ($termos as $item) {
				$termo = Uteis::space2like($item);
				if ($i < 2) {
					$query = "AND (a.titulo LIKE '" . $termo . "') ";
				} else {
					$query .= "OR (a.titulo LIKE '" . $termo . "') ";
				}
				$i++;
			}
		}
		if (isset($arr['status'])) {
			$query .= "AND a.status = '" . $arr['status'] . "' ";
		}
		if (isset($arr['itens'])) {
			$n_de_resultados = $arr['itens'];
		}
		if (isset($query) && !empty($query)) {
			$query = 'WHERE' . strstr($query, ' ');
		}

		$this->db->select("a.id, b.categoria as nome_categoria, a.titulo, a.slug,
				IF ( a.chamada IS NULL OR a.chamada = '', a.descricao, a.chamada ) as chamada, a.descricao, a.view, a.status,
					date_format(a.date, '%d') AS dia,
					date_format(a.date, '%m') AS mes,
					date_format(a.date, '%Y') AS ano,
                    date_format(a.date, '%d/%m/%Y') AS data_cadastro_formatada,
                    date_format(a.date, '%H:%i') AS hora_cadastro_formatada,
                    date_format(a.date, '%d/%m/%Y')as data_atualizada_formatada,
                    date_format(a.date, '%H:%i')as hora_atualizada_formatada")
			->from("$this->table a")
			->join("tbl_categoria_de_blog b", "b.id = a.categoria");
		if (isset($query) && !empty($query)) {
			$query = strstr($query, ' ');
			$this->db->where($query);
		}
		$this->db->order_by("a." . $campo, $ord);
		$query = $this->db->get()->result();

		return $query;
	}

	public function findAllImagem($arr = [], $campo = 'a.id', $ord = 'ASC') {
		$query = '';
		if (!empty($arr['id'])) {
			$query .= "AND a.blog = '" . $arr['id'] . "' ";
		}
		if (!empty($arr['blog'])) {
			$query .= "AND a.blog = '" . $arr['blog'] . "' ";
		}
		if (!empty($query)) {
			$query = 'WHERE' . strstr($query, ' ');
		}

		$this->db->select("a.*")
			->from("tbl_blog_imagem a");

		if (isset($query) && !empty($query)) {
			$query = strstr($query, ' ');
			$this->db->where($query);
		}
		$this->db->order_by($campo, $ord);
		$query = $this->db->get()->result();

		return $query;
	}

}
