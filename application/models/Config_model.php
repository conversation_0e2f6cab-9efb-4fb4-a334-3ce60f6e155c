<?php

class Config_model extends CI_Model {

	function __construct() {
		parent::__construct();
	}

	public function lastconfig() {
		return $this->db->get('tbl_site_config')->result();
	}

	public function create($post) {
		return $this->db->insert('tbl_site_config', $post);
	}

	public function update($post) {
		return $this->db->update('tbl_site_config', $post);
	}

	public function truncateAndCrateRedesSociais($post) {
		$this->db->empty_table('tbl_site_config_redes_sociais');
		if (count($post) > 0) {
			foreach ($post as $item) {
				$this->db->insert('tbl_site_config_redes_sociais', $item);
			}
		}
		return true;
	}

	public function findAllRedeSocial($modo = 'false', $arr = '', $campo = 'a.codigo', $ord = 'desc', $pagina = 0) {
		$query = '';
		$n_de_resultados = 15;
		if (isset($arr['busca'])) {
			$termos = explode(' ', str_replace('  ', ' ', $arr['busca']));
			$i = 1;
			foreach ($termos as $item) {
				$termo = Uteis::space2like($item);
				if ($i < 2) {
					$query = "AND (a.titulo LIKE '" . $termo . "') ";
				} else {
					$query .= "OR (a.titulo LIKE '" . $termo . "') ";
				}
				$i++;
			}
		}
		if (isset($arr['ativo'])) {
			$query .= "AND a.ativo = '" . $arr['ativo'] . "' ";
		}
		if (isset($arr['itens'])) {
			$n_de_resultados = $arr['itens'];
		}
		if (isset($query) && !empty($query)) {
			$query = 'WHERE' . strstr($query, ' ');
		}

		$this->db->select("a.*,
				DATE_FORMAT('a.data_cadastro', '%d/%m/%Y') AS data_cadastro_formatada,
				DATE_FORMAT('a.data_cadastro', '%H:%i') AS hora_cadastro_formatada,
				DATE_FORMAT('a.data_atualizacao', '%d/%m/%Y') AS data_atualizacao_formatada,
				DATE_FORMAT('a.data_atualizacao', '%H:%i') AS hora_atualizacao_formatada")
			->from('tbl_site_config_redes_sociais a');
		if (isset($query) && !empty($query)) {
			$query = 'WHERE' . strstr($query, ' ');
			$this->db->where($query);
		}
		$this->db->order_by($campo, $ord);
		$query = $this->db->get()->result();

		return $query;
	}

	public function findAllIcone($modo = 'false', $arr = '', $campo = 'a.codigo', $ord = 'desc', $pagina = 0) {
		$query = '';
		$n_de_resultados = 15;
		if (isset($arr['busca'])) {
			$termos = explode(' ', str_replace('  ', ' ', $arr['busca']));
			$i = 1;
			foreach ($termos as $item) {
				$termo = Uteis::space2like($item);
				if ($i < 2) {
					$query = "AND (a.icone LIKE '" . $termo . "') ";
				} else {
					$query .= "OR (a.icone LIKE '" . $termo . "') ";
				}
				$i++;
			}
		}
		if (isset($arr['ativo'])) {
			$query .= "AND a.ativo = '" . $arr['ativo'] . "' ";
		}
		if (isset($arr['itens'])) {
			$n_de_resultados = $arr['itens'];
		}
		if (isset($query) && !empty($query)) {
			$query = 'WHERE' . strstr($query, ' ');
		}

		$this->db->select("a.*,
				DATE_FORMAT('a.data_cadastro', '%d/%m/%Y') AS data_cadastro_formatada,
				DATE_FORMAT('a.data_cadastro', '%H:%i') AS hora_cadastro_formatada,
				DATE_FORMAT('a.data_atualizacao', '%d/%m/%Y') AS data_atualizacao_formatada,
				DATE_FORMAT('a.data_atualizacao', '%H:%i') AS hora_atualizacao_formatada")
			->from('tbl_site_config_icones a');
		if (isset($query) && !empty($query)) {
			$query = 'WHERE' . strstr($query, ' ');
			$this->db->where($query);
		}
		$this->db->order_by($campo, $ord);
		$query = $this->db->get()->result();

		return $query;
	}

}
