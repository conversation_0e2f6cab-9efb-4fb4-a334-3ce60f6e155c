# Função get_orders_new - Documentação

## Descrição
A função `get_orders_new()` é uma versão melhorada da função `get_orders()`, com tratamento aprimorado para JOINs, valores NULL e melhor manipulação de erros. Esta função é responsável por buscar e retornar dados de pedidos (orders) para exibição em uma tabela com paginação, filtragem e ordenação.

## Localização
- **Arquivo**: `application/controllers/Logistics.php`
- **Classe**: `Logistics`
- **Rota**: `get-purchase-orders-new` (definida em `routes.php`)

## Melhorias em Relação à Versão Original

1. **Tratamento Aprimorado de JOINs**:
   - Uso de LEFT JOIN garantindo que todos os pedidos sejam retornados, mesmo sem correspondência em `tbl_purchase_details`
   - Uso de COALESCE para tratar valores NULL no status do pedido
   - Subquery para verificar a existência de itens em `tbl_purchase_details_itens`

2. **Tratamento de Erros**:
   - Try/catch para capturar exceções durante o processamento
   - Logs de erros para facilitar a depuração
   - Verificação de JSON inválido ao decodificar itens

3. **Melhor Estrutura de Código**:
   - Comentários detalhados explicando cada seção
   - Nomes de variáveis mais descritivos
   - Organização lógica do fluxo de execução

4. **Filtros Aprimorados**:
   - Lógica melhorada para filtrar por status
   - Tratamento especial para pedidos "abertos" (sem itens ou com status 1)
   - Verificação de lista vazia de fornecedores

5. **Formatação de Dados**:
   - Verificação de validade de datas antes da formatação
   - Tratamento seguro para JSON inválido
   - Estrutura de dados consistente na resposta

## Parâmetros de Entrada
A função recebe dados via POST, enviados pelo DataTables:

- `draw`: Contador usado pelo DataTables para garantir que as respostas sejam processadas na ordem correta
- `start`: Índice inicial para paginação
- `length`: Número de registros a serem retornados
- `search['value']`: Termo de busca inserido pelo usuário
- `order[0]['column']`: Índice da coluna para ordenação
- `order[0]['dir']`: Direção da ordenação (asc/desc)

## Funcionamento

1. **Inicialização de Variáveis**:
   - Converte os parâmetros recebidos para os tipos apropriados
   - Define as colunas disponíveis para ordenação

2. **Construção da Consulta SQL**:
   - Seleciona dados da tabela `tbl_orders` com aliases claros
   - Usa COALESCE para tratar valores NULL no status
   - Realiza LEFT JOIN com `tbl_purchase_details`

3. **Processamento de Busca por Status**:
   - Caso especial para status "aberta" (1) - verifica se não há itens
   - Usa subquery para verificar a não existência de itens
   - Tratamento para pedidos sem registro em `tbl_purchase_details`

4. **Filtros Adicionais**:
   - Filtra por fornecedores específicos (LIST_USERS_ID)
   - Filtra pedidos a partir de uma data específica
   - Busca por referência, canal ou data

5. **Paginação e Ordenação**:
   - Aplica limites para paginação
   - Ordena os resultados pelo ID da tabela em ordem decrescente (do maior para o menor)

6. **Processamento dos Resultados**:
   - Tratamento seguro para JSON inválido
   - Formatação de datas com verificação de validade
   - Obtenção do status com tratamento de erro

7. **Montagem da Resposta**:
   - Estrutura de dados consistente
   - Contagem total de registros
   - Contagem de registros filtrados

## Funções Auxiliares
- `check_order_status_new()`: Versão melhorada da função para determinar o status do pedido
- `get_total_orders()`: Conta o número total de pedidos
- `get_sigla_status()`: Converte códigos de status em siglas

## Exemplo de Uso
Esta função é chamada via AJAX pelo DataTables na página de listagem de pedidos:

```javascript
$('#datatable-purchase-orders-new').DataTable({
  "processing": true,
  "serverSide": true,
  "ajax": {
    "url": "<?php echo base_url('get-purchase-orders-new'); ?>",
    "type": "POST"
  },
  // Configurações adicionais...
});
```

## Observações
- A função filtra pedidos a partir de 01/08/2024
- Apenas pedidos de fornecedores específicos são exibidos (definidos em `LIST_USERS_ID`)
- Logs de depuração são gerados para facilitar a identificação de problemas
- Tratamento de erros evita falhas na exibição dos dados
