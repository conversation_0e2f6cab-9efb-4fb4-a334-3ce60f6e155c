# Função get_orders - Documentação

## Descrição
A função `get_orders()` é responsável por buscar e retornar dados de pedidos (orders) para exibição em uma tabela com paginação, filtragem e ordenação. Esta função é utilizada no módulo de logística para gerenciar pedidos de compra.

## Localização
- **Arquivo**: `application/controllers/Logistics.php`
- **Classe**: `Logistics`
- **Rota**: `get-purchase-orders` (definida em `routes.php`)

## Parâmetros de Entrada
A função recebe dados via POST, enviados pelo DataTables:

- `draw`: Contador usado pelo DataTables para garantir que as respostas sejam processadas na ordem correta
- `start`: Índice inicial para paginação
- `length`: Número de registros a serem retornados
- `search['value']`: Termo de busca inserido pelo usuário
- `order[0]['column']`: <PERSON>ndice da coluna para ordenação
- `order[0]['dir']`: Direção da ordenação (asc/desc)

## Funcionamento

1. **Inicialização de Variáveis**:
   - Converte os parâmetros recebidos para os tipos apropriados
   - Define as colunas disponíveis para ordenação

2. **Construção da Consulta SQL**:
   - Seleciona dados das tabelas `tbl_orders` e `tbl_purchase_details`
   - Realiza um JOIN entre as tabelas
   - Filtra pedidos de fornecedores específicos (armazenados em `LIST_USERS_ID`)
   - Filtra pedidos a partir de uma data específica (2024-08-01)

3. **Processamento de Busca**:
   - Verifica se o termo de busca corresponde a um status específico
   - Permite busca por referência, canal, país ou data
   - Converte datas no formato DD/MM/YYYY para YYYY-MM-DD para busca no banco

4. **Paginação e Ordenação**:
   - Aplica limites para paginação
   - Ordena os resultados conforme solicitado

5. **Formatação dos Resultados**:
   - Processa cada registro retornado
   - Formata datas
   - Conta o número de itens em cada pedido
   - Verifica o status atual do pedido
   - Adiciona botões de ação para cada registro

6. **Retorno**:
   - Retorna um objeto JSON com:
     - `draw`: O mesmo valor recebido na requisição
     - `recordsTotal`: Total de registros na tabela
     - `recordsFiltered`: Total de registros após aplicação dos filtros
     - `data`: Array com os dados formatados para exibição

## Tabelas Utilizadas
- `tbl_orders`: Armazena os dados principais dos pedidos
- `tbl_purchase_details`: Armazena detalhes adicionais sobre o processamento do pedido
- `tbl_purchase_details_itens`: Armazena informações sobre os itens individuais de cada pedido

## Campos Retornados
A função retorna um array com os seguintes campos para cada pedido:
1. Coluna vazia (para expansão)
2. Botões de ação
3. Referência do pedido
4. Data do pedido (formatada como DD/MM/YYYY)
5. Número de itens
6. Canal/País
7. ID do usuário
8. Preço total
9. Status da compra

## Funções Auxiliares
- `check_order_status()`: Determina o status atual do pedido
- `get_total_orders()`: Conta o número total de pedidos
- `get_sigla_status()`: Converte códigos de status em siglas

## Exemplo de Uso
Esta função é chamada via AJAX pelo DataTables na página de listagem de pedidos:

```javascript
$('#datatable-purchase-orders').DataTable({
  "processing": true,
  "serverSide": true,
  "ajax": {
    "url": "<?php echo base_url('get-purchase-orders'); ?>",
    "type": "POST"
  },
  // Configurações adicionais...
});
```

## Observações
- A função filtra pedidos a partir de 01/08/2024
- Apenas pedidos de fornecedores específicos são exibidos (definidos em `LIST_USERS_ID`)
- O status do pedido é determinado com base em registros nas tabelas `tbl_purchase_details` e `tbl_purchase_details_itens`
